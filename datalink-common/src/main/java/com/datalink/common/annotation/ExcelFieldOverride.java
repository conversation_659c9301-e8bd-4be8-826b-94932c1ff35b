package com.datalink.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel字段排序覆盖注解
 * 用于在子类中重新定义继承字段的Excel导出排序
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ExcelFieldOverride
{
    /**
     * 字段覆盖配置
     */
    FieldOverride[] value();
    
    /**
     * 字段覆盖配置
     */
    @interface FieldOverride
    {
        /**
         * 字段名
         */
        String fieldName();
        
        /**
         * 新的排序值
         */
        int sort();
        
        /**
         * 新的列名（可选）
         */
        String name() default "";
        
        /**
         * 新的列宽（可选）
         */
        double width() default -1;
        
        /**
         * 新的日期格式（可选）
         */
        String dateFormat() default "";

        boolean isShow() default true;
    }
}
