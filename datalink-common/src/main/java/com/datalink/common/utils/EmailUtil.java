package com.datalink.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件发送工具类
 * 
 * <AUTHOR>
 */
public class EmailUtil {
    
    private static final Logger log = LoggerFactory.getLogger(EmailUtil.class);
    
    /**
     * 发送HTML格式邮件给单个收件人
     * 
     * @param smtpHost SMTP服务器地址
     * @param smtpPort SMTP端口
     * @param username 发件人邮箱
     * @param password 邮箱密码/授权码
     * @param sslEnabled 是否启用SSL
     * @param fromName 发件人名称
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content HTML格式邮件内容
     * @return 发送结果
     */
    public static EmailResult sendHtmlMail(String smtpHost, int smtpPort, String username,
                                          String password, boolean sslEnabled, String fromName,
                                          String to, String subject, String content) {
        try {
            Session session = createMailSession(smtpHost, smtpPort, username, password, sslEnabled);

            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username, fromName));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
            message.setSubject(subject, "UTF-8");
            message.setContent(content, "text/html;charset=UTF-8");

            Transport.send(message);

            log.info("邮件发送成功 - 收件人: {}, 主题: {}", to, subject);
            return EmailResult.success("邮件发送成功");

        } catch (Exception e) {
            log.error("邮件发送失败 - 收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
            return EmailResult.error("邮件发送失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量发送HTML格式邮件
     * 
     * @param smtpHost SMTP服务器地址
     * @param smtpPort SMTP端口
     * @param username 发件人邮箱
     * @param password 邮箱密码/授权码
     * @param sslEnabled 是否启用SSL
     * @param fromName 发件人名称
     * @param toList 收件人邮箱列表
     * @param subject 邮件主题
     * @param content HTML格式邮件内容
     * @return 发送结果
     */
    public static EmailResult sendBatchHtmlMail(String smtpHost, int smtpPort, String username,
                                               String password, boolean sslEnabled, String fromName,
                                               String fromAddress, List<String> toList, String subject, String content) {
        if (toList == null || toList.isEmpty()) {
            return EmailResult.error("收件人列表为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        try {
            Session session = createMailSession(smtpHost, smtpPort, username, password, sslEnabled);

            for (String to : toList) {
                try {
                    if (StringUtils.isEmpty(to)) {
                        continue;
                    }

                    MimeMessage message = new MimeMessage(session);
                    message.setFrom(new InternetAddress(fromAddress, fromName));
                    message.addRecipient(Message.RecipientType.TO, new InternetAddress(to));
                    message.setSubject(subject, "UTF-8");
                    message.setContent(content, "text/html;charset=UTF-8");

                    Transport.send(message);

                    successCount++;
                    log.info("邮件发送成功 - 收件人: {}, 主题: {}", to, subject);

                } catch (Exception e) {
                    failCount++;
                    String errorMsg = String.format("发送给 %s 失败: %s", to, e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    log.error("邮件发送失败 - 收件人: {}, 主题: {}, 错误: {}", to, subject, e.getMessage(), e);
                }
            }
            
            String resultMessage = String.format("批量发送完成 - 成功: %d, 失败: %d", successCount, failCount);
            if (failCount > 0) {
                resultMessage += ", 失败详情: " + errorMessages.toString();
                return EmailResult.warning(resultMessage);
            } else {
                return EmailResult.success(resultMessage);
            }
            
        } catch (Exception e) {
            log.error("批量邮件发送异常: {}", e.getMessage(), e);
            return EmailResult.error("批量邮件发送异常: " + e.getMessage());
        }
    }
    
    /**
     * 创建邮件会话
     */
    private static Session createMailSession(String smtpHost, int smtpPort, String username,
                                            String password, boolean sslEnabled) {
        Properties props = new Properties();
        props.put("mail.smtp.host", smtpHost);
        props.put("mail.smtp.port", smtpPort);
        props.put("mail.smtp.auth", "true");

        if (sslEnabled) {
            props.put("mail.smtp.ssl.enable", "true");
        } else {
            props.put("mail.smtp.starttls.enable", "true");
        }

        return Session.getInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
    }
    
    /**
     * 替换邮件模板中的变量
     * 
     * @param template 邮件模板，变量格式为 {variableName}
     * @param variables 变量映射
     * @return 替换后的内容
     */
    public static String replaceTemplateVariables(String template, Map<String, Object> variables) {
        if (StringUtils.isEmpty(template) || variables == null || variables.isEmpty()) {
            return template;
        }
        
        String result = template;
        Pattern pattern = Pattern.compile("\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(template);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = variables.get(variableName);
            if (value != null) {
                result = result.replace("{" + variableName + "}", value.toString());
            }
        }
        
        return result;
    }
    
    /**
     * 验证邮箱地址格式
     * 
     * @param email 邮箱地址
     * @return 是否有效
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        Pattern pattern = Pattern.compile(emailRegex);
        return pattern.matcher(email).matches();
    }
    
    /**
     * 邮件发送结果类
     */
    public static class EmailResult {
        private boolean success;
        private String message;
        private String type; // success, warning, error
        
        private EmailResult(boolean success, String message, String type) {
            this.success = success;
            this.message = message;
            this.type = type;
        }
        
        public static EmailResult success(String message) {
            return new EmailResult(true, message, "success");
        }
        
        public static EmailResult warning(String message) {
            return new EmailResult(true, message, "warning");
        }
        
        public static EmailResult error(String message) {
            return new EmailResult(false, message, "error");
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public String getType() {
            return type;
        }
        
        @Override
        public String toString() {
            return String.format("EmailResult{success=%s, type='%s', message='%s'}", success, type, message);
        }
    }
}
