package com.datalink.common.config;

import com.datalink.common.filter.RequestLoggingFilter;
import javax.servlet.FilterRegistration;
import javax.servlet.ServletContext;
import org.springframework.web.WebApplicationInitializer;

public class WebConfig implements WebApplicationInitializer {

    @Override
    public void onStartup(ServletContext servletContext) {
        // 直接实例化 Filter
        RequestLoggingFilter requestLoggingFilter = new RequestLoggingFilter();

        FilterRegistration.Dynamic filter = servletContext.addFilter("requestLoggingFilter", requestLoggingFilter);
        filter.addMappingForUrlPatterns(null, false, "/api/return/receive");
    }
}