package com.datalink.common.utils.poi;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.AjaxResult;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * ExcelUtil 扁平化导出测试
 */
public class ExcelUtilFlatTest {

    /**
     * 测试父对象
     */
    public static class TestParent {
        @Excel(name = "父对象ID", sort = 1)
        private Long parentId;
        
        @Excel(name = "父对象名称", sort = 2)
        private String parentName;
        
        @Excel(name = "子对象名称", targetAttr = "childName", sort = 3)
        private List<TestChild> children;
        
        @Excel(name = "父对象描述", sort = 4)
        private String parentDesc;

        // 构造函数
        public TestParent() {}
        
        public TestParent(Long parentId, String parentName, String parentDesc) {
            this.parentId = parentId;
            this.parentName = parentName;
            this.parentDesc = parentDesc;
            this.children = new ArrayList<>();
        }

        // Getters and Setters
        public Long getParentId() { return parentId; }
        public void setParentId(Long parentId) { this.parentId = parentId; }
        
        public String getParentName() { return parentName; }
        public void setParentName(String parentName) { this.parentName = parentName; }
        
        public String getParentDesc() { return parentDesc; }
        public void setParentDesc(String parentDesc) { this.parentDesc = parentDesc; }
        
        public List<TestChild> getChildren() { return children; }
        public void setChildren(List<TestChild> children) { this.children = children; }
    }

    /**
     * 测试子对象
     */
    public static class TestChild {
        private Long childId;
        private String childName;
        private BigDecimal amount;

        public TestChild() {}
        
        public TestChild(Long childId, String childName, BigDecimal amount) {
            this.childId = childId;
            this.childName = childName;
            this.amount = amount;
        }

        // Getters and Setters
        public Long getChildId() { return childId; }
        public void setChildId(Long childId) { this.childId = childId; }
        
        public String getChildName() { return childName; }
        public void setChildName(String childName) { this.childName = childName; }
        
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
    }

    @Test
    public void testExportExcelFlat() {
        // 创建测试数据
        List<TestParent> testData = createTestData();
        
        // 创建ExcelUtil实例
        ExcelUtil<TestParent> excelUtil = new ExcelUtil<>(TestParent.class);
        
        try {
            // 测试扁平化导出
            AjaxResult result = excelUtil.exportExcelFlat(testData, "扁平化测试数据");
            
            System.out.println("扁平化导出测试结果: " + result.get("msg"));
            System.out.println("生成的文件名: " + result.get("data"));
            
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("测试失败: " + e.getMessage());
        }
    }

    private List<TestParent> createTestData() {
        List<TestParent> data = new ArrayList<>();
        
        // 创建第一个父对象
        TestParent parent1 = new TestParent(1L, "父对象1", "这是第一个父对象");
        parent1.getChildren().add(new TestChild(101L, "子对象1-1", new BigDecimal("100.50")));
        parent1.getChildren().add(new TestChild(102L, "子对象1-2", new BigDecimal("200.75")));
        data.add(parent1);
        
        // 创建第二个父对象
        TestParent parent2 = new TestParent(2L, "父对象2", "这是第二个父对象");
        parent2.getChildren().add(new TestChild(201L, "子对象2-1", new BigDecimal("300.25")));
        parent2.getChildren().add(new TestChild(202L, "子对象2-2", new BigDecimal("400.00")));
        parent2.getChildren().add(new TestChild(203L, "子对象2-3", new BigDecimal("500.99")));
        data.add(parent2);
        
        // 创建第三个父对象（没有子对象）
        TestParent parent3 = new TestParent(3L, "父对象3", "这是第三个父对象，没有子对象");
        data.add(parent3);
        
        return data;
    }
}
