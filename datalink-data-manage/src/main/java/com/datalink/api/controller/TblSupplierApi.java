package com.datalink.api.controller;

import com.datalink.api.common.SapApiResult;
import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.SapSupplierRequest;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.entity.SysDept;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 供应商接口 控制层
 * 
 * <AUTHOR>
 */
@Api(tags = "SAP供应商接口")
@Validated
@RestController
@RequestMapping("/api/supplier")
public class TblSupplierApi extends BaseController
{
    @Autowired
    private ISysDeptService  deptService;

    @ApiOperation("接收供应商")
    @ApiImplicitParam(name = "request", value = "供应商信息", dataType = "SapSupplierRequest")
    @PreAuthorize("@ss.hasRole('sapApiUser')")
    @PostMapping("/receive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "接收供应商", businessType = BusinessType.IMPORT)
    public AjaxResult receive(@Valid @RequestBody SapSupplierRequest request) {
        

        if (StringUtils.isNull(request) || StringUtils.isEmpty(request.getLifnr())) {
            throw new RuntimeException("供应商编号不能为空");
        }

        // 查找根节点（parentId=0的节点）
        SysDept rootDept = new SysDept();
        rootDept.setParentId(0L);
        List<SysDept> rootDepts = deptService.selectDeptList(rootDept);
        if (rootDepts == null || rootDepts.isEmpty()) {
            throw new RuntimeException("根节点不存在");
        }
        Long rootId = rootDepts.get(0).getDeptId();

        // 根据供应商编码查询供应商信息
        SysDept dept = new SysDept();
        dept.setSupplierCode(request.getLifnr());
        List<SysDept> depts = deptService.selectDeptList(dept);

        // 将供应商接口数据转换为部门数据
        SysDept sysDept = SapObjectConverter.convertToDept(request);
        sysDept.setParentId(rootId);
        sysDept.setStatus("0"); // 正常状态
        sysDept.setDelFlag("0"); // 未删除

        int result = 0;
        if (depts == null || depts.isEmpty()) {
            // 新增供应商
            sysDept.setOrderNum("0"); // 默认顺序号
            sysDept.setCreateBy(SecurityUtils.getUsername());
            result = deptService.insertDept(sysDept);
        } else {
            // 更新供应商
            SysDept existDept = depts.get(0);
            sysDept.setDeptId(existDept.getDeptId());
            sysDept.setAncestors(existDept.getAncestors());
            sysDept.setOrderNum(existDept.getOrderNum());
            sysDept.setUpdateBy(SecurityUtils.getUsername());
            result = deptService.updateDept(sysDept);
        }

        if (result > 0) {
            // 处理成功，返回处理结果
            return AjaxResult.success();
        } else {
            throw new RuntimeException("处理供应商数据失败");
        }
    }
} 