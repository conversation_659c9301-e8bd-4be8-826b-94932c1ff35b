package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.service.ITblAsnService;
import io.swagger.annotations.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api(tags = "WMS ASN接口")
@Validated
@RestController
@RequestMapping("/api/asn")
public class TblAsnApi {
    @Autowired
    private ITblAsnService asnService;

    //    @ApiOperation("查询ASN")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"asnid","searchvalue","createby","updateby","updatetime","params","createtime","itemid","direction","kafkastatus","articleid","docno","docdate","remark"}, value={TblAsn.class, TblAsnItem.class, TblAsnArticle.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询ASN", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblAsn> asnList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblAsn searchParam = new TblAsn();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            asnList = asnService.selectTblAsnFullList(searchParam);
        }
        if(asnList.isEmpty()){
            Long lastId = asnService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+asnList.get(asnList.size()-1).getAsnId());
            ajax.put("time", asnList.get(asnList.size()-1).getCreateTime());
        }

        ajax.put("items", asnList);
        return ajax;
    }

    @ApiOperation("查询ASN")
    @ApiImplicitParam(name = "asnCode", value = "ASN编号", dataType = "String")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = TblAsn.class)
    })
    @PreAuthorize("@ss.hasRole('wmsApiUser')")
    @JacksonFilter(exclude={"asnId","searchValue","createBy","updateBy","updateTime","params","createTime","itemId","direction","kafkaStatus","articleId","docno","docdate","remark","boxId", "boxes"}, value={TblAsn.class, TblAsnItem.class, TblAsnArticle.class, TblAsnBox.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @GetMapping(value = "/{asnCode}")
    @Log(title = "查询ASN", businessType = BusinessType.EXPORT)
    public AjaxResult query(@PathVariable("asnCode") String asnCode) {
        if (asnCode == null || asnCode.isEmpty()) {
            return AjaxResult.error("ASN编号不能为空");
        }
        // 使用包含箱信息的查询方法
        TblAsn asn = asnService.selectTblAsnByAsnCode(asnCode);
        return AjaxResult.success(asn);
    }
}
