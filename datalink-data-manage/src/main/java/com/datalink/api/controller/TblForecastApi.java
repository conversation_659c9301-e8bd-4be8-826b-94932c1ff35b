package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.api.domain.dto.TblForecastBatchRequestDTO;
import com.datalink.api.domain.dto.TblForecastItemDTO;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;
import com.datalink.datamanage.service.ITblForecastService;
import com.datalink.datamanage.service.EmailNotificationService;
import com.datalink.system.service.ISysDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.datalink.api.common.SapObjectConverter.convertToTblForecast;

@Api(tags = "SAP预测接口")
@Validated
@RestController
@RequestMapping("/api/forecast")
public class TblForecastApi {

    private static final Logger logger = LoggerFactory.getLogger(TblForecastApi.class);

    @Autowired
    private ITblForecastService forecastService;

    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private ISysDictDataService dictDataService;

//    @ApiOperation("查询预测")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"forecastid","searchvalue","createby","updateby","updatetime","remark","params","createtime","itemid","direction","kafkastatus"}, value={TblForecast.class, TblForecastItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","orderid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblForecastItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询预测", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblForecast> forecastList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblForecast searchParam = new TblForecast();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            forecastList = forecastService.selectTblForecastFullList(searchParam);
        }
        if(forecastList.isEmpty()){
            Long lastId = forecastService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+forecastList.get(forecastList.size()-1).getForecastId());
            ajax.put("time", forecastList.get(forecastList.size()-1).getCreateTime());
        }

        ajax.put("items", forecastList);
        return ajax;
    }

//    @ApiOperation("发送")
    @ApiImplicitParam(name = "forecast", value = "预测信息", dataType = "TblForecast")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送预测", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblForecast tblForecast) {
        tblForecast.setDirection(DataConstants.DIRECTION_OUT);
        tblForecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblForecast.setCreateBy(SecurityUtils.getUsername());
        forecastService.insertTblForecast(tblForecast);
        return ApiResult.success();
    }

//    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "forecast", value = "预测信息", dataType = "TblForecast", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送预测", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblForecast> forecasts) {
        for (TblForecast forecast : forecasts){
            forecast.setDirection(DataConstants.DIRECTION_OUT);
            forecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            forecast.setCreateBy(SecurityUtils.getUsername());
            forecastService.insertTblForecast(forecast);
        }

        return ApiResult.success();
    }


    @ApiOperation("批量接收预测")
    @ApiImplicitParam(name = "requestDTO", value = "预测信息", dataType = "TblForecastBatchRequestDTO")
    @PreAuthorize("@ss.hasRole('sapApiUser')")
    @PostMapping("/batchReceive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量接收预测", businessType = BusinessType.IMPORT)
    public AjaxResult batchReceive(@Valid @RequestBody TblForecastBatchRequestDTO requestDTO) {
        try {
            // 生成统一的接收时间戳，确保同批次数据时间一致性
            Date batchReceiveTime = new Date();
            String currentUser = SecurityUtils.getUsername();

            // 按照预测编号分组
            Map<String, List<TblForecastItemDTO>> groupedData = requestDTO.getData().stream()
                    .collect(Collectors.groupingBy(TblForecastItemDTO::getForecastCode));

            // 处理每个分组 - 纯新增模式，不再进行版本管理
            for (Map.Entry<String, List<TblForecastItemDTO>> entry : groupedData.entrySet()) {
                String forecastCode = entry.getKey();
                List<TblForecastItemDTO> items = entry.getValue();

                TblForecast forecast = convertToTblForecast(items, dictDataService);
                if (forecast != null) {
                    // 设置统一的接收时间和基本属性
                    forecast.setCreateTime(batchReceiveTime);
                    forecast.setVersion("1"); // 简化版本管理，统一设为1
                    forecast.setDirection(DataConstants.DIRECTION_OUT);
                    forecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
                    forecast.setCreateBy(currentUser);

                    // 为所有行项目设置统一的创建时间
                    if (forecast.getDetail() != null) {
                        forecast.getDetail().forEach(item -> {
                            item.setCreateTime(batchReceiveTime);
                            item.setCreateBy(currentUser);
                        });
                    }

                    forecastService.insertTblForecast(forecast);
                    logger.info("成功保存预测编号: {}，接收时间: {}", forecastCode, batchReceiveTime);

                    // 发送邮件通知
                    emailNotificationService.sendForecastNotification(forecast);
                } else {
                    logger.warn("预测编号: {} 转换失败，跳过处理", forecastCode);
                }
            }

            return AjaxResult.success();
        } catch (Exception e) {
            logger.error("批量处理预测失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量处理预测失败: " + e.getMessage());
        }
    }
}
