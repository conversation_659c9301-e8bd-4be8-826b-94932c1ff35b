package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.domain.ReturnFeedbackResponse;
import com.datalink.api.domain.ReturnReceiveRequest;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblReturn;
import com.datalink.datamanage.domain.TblReturnBox;
import com.datalink.datamanage.service.ITblReturnService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * WMS退货接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Api(tags = "WMS退货接口")
@RestController
@RequestMapping("/api/return")
public class TblReturnApi {

    private static final Logger logger = LoggerFactory.getLogger(TblReturnApi.class);

    @Autowired
    private ITblReturnService tblReturnService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 接收WMS退货信息
     */
    @ApiOperation("接收WMS退货信息")
    @PreAuthorize("@ss.hasRole('wmsApiUser')")
    @PostMapping("/receive")
    @Log(title = "接收WMS退货信息", businessType = BusinessType.INSERT)
    public AjaxResult receiveReturnFromWms(@Valid @RequestBody ReturnReceiveRequest request) {
        try {
            logger.info("接收WMS退货信息：{}", objectMapper.writeValueAsString(request));

            // 将请求对象转换为JSON字符串
            String requestJson = objectMapper.writeValueAsString(request);

            // 调用服务层处理
            return tblReturnService.receiveReturnFromWms(requestJson);

        } catch (Exception e) {
            logger.error("接收WMS退货信息失败", e);
            return AjaxResult.error("接收退货信息失败：" + e.getMessage());
        }
    }
}
