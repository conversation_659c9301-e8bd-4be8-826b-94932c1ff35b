package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * WMS退货信息接收请求
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@ApiModel("WMS退货信息接收请求")
public class ReturnReceiveRequest {

    @ApiModelProperty(value = "退货单号", required = true)
    @JsonProperty("returnNo")
    @NotEmpty(message = "退货单号不能为空")
    private String returnNo;

    @ApiModelProperty(value = "供应商代码", required = true)
    @JsonProperty("suppCode")
    @NotEmpty(message = "供应商代码不能为空")
    private String suppCode;

    @ApiModelProperty(value = "箱子列表", required = true)
    @JsonProperty("boxList")
    @NotNull(message = "箱子列表不能为空")
    @Valid
    private List<BoxInfo> boxList;

    @ApiModel("箱子信息")
    public static class BoxInfo {
        @ApiModelProperty(value = "ASN编号")
        @JsonProperty("asnCode")
        private String asnCode;

        @ApiModelProperty(value = "订单编号")
        @JsonProperty("orderCode")
        private String orderCode;

        @ApiModelProperty(value = "行号")
        @JsonProperty("itemNo")
        private String itemNo;

        @ApiModelProperty(value = "下达号")
        @JsonProperty("releaseNo")
        private String releaseNo;

        @ApiModelProperty(value = "批次号")
        @JsonProperty("batchNo")
        private String batchNo;

        @ApiModelProperty(value = "明细列表")
        @JsonProperty("detail")
        @Valid
        private List<BoxDetail> detail;

        // Getters and Setters
        public String getAsnCode() {
            return asnCode;
        }

        public void setAsnCode(String asnCode) {
            this.asnCode = asnCode;
        }

        public String getOrderCode() {
            return orderCode;
        }

        public void setOrderCode(String orderCode) {
            this.orderCode = orderCode;
        }

        public String getItemNo() {
            return itemNo;
        }

        public void setItemNo(String itemNo) {
            this.itemNo = itemNo;
        }

        public String getReleaseNo() {
            return releaseNo;
        }

        public void setReleaseNo(String releaseNo) {
            this.releaseNo = releaseNo;
        }

        public String getBatchNo() {
            return batchNo;
        }

        public void setBatchNo(String batchNo) {
            this.batchNo = batchNo;
        }

        public List<BoxDetail> getDetail() {
            return detail;
        }

        public void setDetail(List<BoxDetail> detail) {
            this.detail = detail;
        }
    }

    @ApiModel("箱子明细")
    public static class BoxDetail {
        @ApiModelProperty(value = "标签")
        @JsonProperty("label")
        private String label;

        @ApiModelProperty(value = "箱子序号", required = true)
        @JsonProperty("boxIndex")
        @NotNull(message = "箱子序号不能为空")
        private Integer boxIndex;

        @ApiModelProperty(value = "数量", required = true)
        @JsonProperty("qty")
        @NotNull(message = "数量不能为空")
        private String qty;

        // Getters and Setters
        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public Integer getBoxIndex() {
            return boxIndex;
        }

        public void setBoxIndex(Integer boxIndex) {
            this.boxIndex = boxIndex;
        }

        public String getQty() {
            return qty;
        }

        public void setQty(String qty) {
            this.qty = qty;
        }
    }

    // Getters and Setters
    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public List<BoxInfo> getBoxList() {
        return boxList;
    }

    public void setBoxList(List<BoxInfo> boxList) {
        this.boxList = boxList;
    }
}
