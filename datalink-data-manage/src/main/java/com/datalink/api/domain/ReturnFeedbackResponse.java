package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * WMS退货信息回传响应
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@ApiModel("WMS退货信息回传响应")
public class ReturnFeedbackResponse {

    @ApiModelProperty(value = "明细列表")
    @JsonProperty("detail")
    private List<BoxDetail> detail;

    // Getters and Setters
    public List<BoxDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<BoxDetail> detail) {
            this.detail = detail;
        }

    @ApiModel("箱子明细")
    public static class BoxDetail {
        @ApiModelProperty(value = "原标签")
        @JsonProperty("label")
        private String label;

        @ApiModelProperty(value = "新标签")
        @JsonProperty("newLabel")
        private String newLabel;

        @ApiModelProperty(value = "新批次号")
        @JsonProperty("newBatchNo")
        private String newBatchNo;

        // Getters and Setters
        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getNewLabel() {
            return newLabel;
        }

        public void setNewLabel(String newLabel) {
            this.newLabel = newLabel;
        }

        public String getNewBatchNo() {
            return newBatchNo;
        }

        public void setNewBatchNo(String newBatchNo) {
            this.newBatchNo = newBatchNo;
        }
    }
}
