package com.datalink.api.common;

import com.datalink.api.domain.*;
import com.datalink.api.domain.dto.TblForecastItemDTO;
import com.datalink.common.DataConstants;
import com.datalink.common.core.domain.entity.SysDept;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.system.service.ISysDictDataService;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class SapObjectConverter {

    /**
     * 将DTO转换为TblForecast对象
     * 注意：传入的dtoList应该已经按照ZFNO分组，所有元素具有相同的预测编号
     *
     * @param dtoList 已按ZFNO分组的DTO列表
     * @param dictDataService 字典数据服务
     * @return TblForecast对象
     */
    public static TblForecast convertToTblForecast(List<TblForecastItemDTO> dtoList, ISysDictDataService dictDataService) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }

        // 由于已经按照ZFNO分组，所有元素具有相同的预测编号，使用第一个元素获取共同属性
        TblForecastItemDTO firstItem = dtoList.get(0);

        TblForecast forecast = new TblForecast();
        forecast.setForecastCode(firstItem.getForecastCode());
        forecast.setPlantCode(firstItem.getPlantCode());
        forecast.setPlantName(dictDataService.selectDictLabel(DataConstants.DICT_TYPE_PLANT, firstItem.getPlantCode()));
        forecast.setSuppCode(firstItem.getSuppCode());

        // 设置行项目列表
        List<TblForecastItem> itemList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        for (TblForecastItemDTO dto : dtoList) {
            TblForecastItem item = new TblForecastItem();
            item.setItemNo(dto.getItemNo());
            item.setArticleNo(dto.getArticleNo());

            // 设置交货日期，从KMONTH解析
            try {
                if (StringUtils.isNotEmpty(dto.getKmonth())) {
                    item.setDeliveryDate(sdf.parse(dto.getKmonth()));
                }
            } catch (ParseException e) {
                throw new RuntimeException("解析预测日期失败: " + dto.getKmonth(), e);
            }

            // 设置数量
            if (StringUtils.isNotEmpty(dto.getQuantity())) {
                try {
                    item.setQuantity(new BigDecimal(dto.getQuantity()));
                } catch (NumberFormatException e) {
                    throw new RuntimeException("解析预测数量失败: " + dto.getQuantity(), e);
                }
            }

            item.setUnit(dto.getUnit());
            item.setProType(dto.getProType());

            itemList.add(item);
        }

        forecast.setDetail(itemList);
        return forecast;
    }
    
    /**
     * 将SAP物料数据转换为TblMaterial对象
     *
     * @param item SAP物料数据项
     * @return TblMaterial对象
     */
    public static TblMaterial convertToTblMaterial(SapMaterialItem item) {
        if (item == null) {
            return null;
        }
        
        TblMaterial material = new TblMaterial();
        material.setMaterialCode(item.getMatnr());
        material.setBaseUnit(item.getMeins());
        
        // 处理物料描述：优先使用英文描述，如果没有则使用中文描述
        if (item.getMatk() != null && !item.getMatk().isEmpty()) {
            String materialName = null;
            
            // 先查找英文描述
            for (SapMaterialText text : item.getMatk()) {
                if ("E".equals(text.getSpras())) {
                    materialName = text.getMaktx();
                    break;
                }
            }
            
            // 如果没有英文描述，则使用中文描述
            if (materialName == null) {
                for (SapMaterialText text : item.getMatk()) {
                    if ("1".equals(text.getSpras())) {
                        materialName = text.getMaktx();
                        break;
                    }
                }
            }
            
            material.setMaterialName(materialName);
        }
        
        // 处理工厂信息
        if (item.getMarc() != null && !item.getMarc().isEmpty()) {
            List<TblMaterialPlant> plants = new ArrayList<>();
            
            for (SapMaterialPlant plantData : item.getMarc()) {
                TblMaterialPlant plant = new TblMaterialPlant();
                plant.setPlantCode(plantData.getWerks());
                
                // 转换包装数量
                if (StringUtils.isNotEmpty(plantData.getBstrf())) {
                    try {
                        plant.setPackQuantity(convertSapNumber(plantData.getBstrf()));
                    } catch (Exception e) {
                        throw new RuntimeException("解析包装数量失败: " + plantData.getBstrf(), e);
                    }
                }
                
                plant.setCreateBy(SecurityUtils.getUsername());
                plants.add(plant);
            }
            
            material.setPlants(plants);
        }
        
        material.setCreateBy(SecurityUtils.getUsername());
        return material;
    }
    
    public static List<TblFeedback> convertToFeedBack(SapListRequst<SapGrItem> request){
        Map<String, TblFeedback> feedbackMap = new HashMap<String, TblFeedback>();
        for (SapGrItem item : request.getItemList()){
            String asnNo = item.getLIFEX();
            TblFeedback feedback = feedbackMap.get(asnNo);
            if (feedback == null){
                feedback = new TblFeedback();
                feedback.setDnNo(asnNo);
                feedback.setDepot(item.getZDEPOT());
                feedback.setCompCode(item.getBUKRS());
                feedback.setPlantCode(item.getWERKS());
                feedback.setReceivingPlace(item.getWANST());
                feedback.setReceivingQuantity(convertSapNumber(item.getMENGE()));
                feedback.setPlantName("");
                feedback.setSuppCode(item.getLIFNR());
                feedback.setSuppName("");
                feedback.setOrderUnit(item.getMEINS());
                feedback.setReceivingDate(convertSapDate(item.getEINDT()));
                feedback.setDeliveryNoteDate(convertSapDate(item.getERDAT()));
                feedback.setDeliveryNoteTime(convertSapTime(item.getERZET()));
                feedback.setDirection(DataConstants.DIRECTION_IN);
                feedback.setKafkaStatus(DataConstants.KAFKA_STATUS_NO_SENT);
                feedback.setDetail(new ArrayList<TblFeedbackItem>());
                feedbackMap.put(asnNo, feedback);
            }
            TblFeedbackItem feedbackItem = new TblFeedbackItem();
            feedbackItem.setArticleNo(item.getMATNR());
            feedbackItem.setArticleName(item.getMAKTX());
            feedbackItem.setQuantity(convertSapNumber(item.getMENGE_1()));
            feedbackItem.setOrderCode(item.getEBELN());
            feedbackItem.setOrderLineNo(item.getEBELP());
            feedbackItem.setUnit(item.getMEINS_1());
            feedbackItem.setRcvDate(convertSapDate(item.getBUDAT()));
            feedbackItem.setRcvTime(DateUtils.parseDate("1970-01-01"));
            feedbackItem.setRcvDocNo(item.getMBLNR());
            feedbackItem.setRcvDocItemNo(convertSapNumber(item.getZEILE()).intValue());
            feedback.getDetail().add(feedbackItem);
        }
        return new ArrayList<>(feedbackMap.values());
    }

    public static List<TblOrder> convertToTblOrder(SapListRequst<SapPoItem> request){
        Map<String, TblOrder> poMap = new LinkedHashMap<>();  // 使用LinkedHashMap保持插入顺序
        for (SapPoItem item : request.getItemList()){
            String poCode = item.getEBELN();
            TblOrder po = poMap.get(poCode);
            if (po == null){
                po = new TblOrder();
                po.setOrderCode(poCode);
                po.setDirection(DataConstants.DIRECTION_IN);
                po.setCompCode(item.getBUKRS());
                po.setCompName(item.getZNAME());
                po.setPlantCode(item.getWERKS());
//                po.setPlantName(item.getSORT1());
                po.setSuppCode(item.getLIFNR());
                po.setSuppName(item.getZNAME1());
                po.setPlannerNo(item.getEKORG());
                po.setDetail(new ArrayList<TblOrderItem>());
                // 若AEDAT长度为8，则表示是YYYYMMDD格式，需要补0成YYYYMMDDHHMMSS格式
                if (item.getAEDAT().length() == 8) {
                    item.setAEDAT(item.getAEDAT() + "000000");
                }
                po.setCreateTime(convertSapDateTime(item.getAEDAT()));
                if (item.getZMDDT() != null) {
                    po.setSapUpdateTime(convertSapDateTime(item.getZMDDT()));
                }
                po.setCustomerCode(item.getKUNNR());
                po.setRequester(item.getVBPA());
                poMap.put(poCode, po);
            }
            TblOrderItem orderItem = new TblOrderItem();
            orderItem.setArticleNo(item.getMATNR());
            orderItem.setArticleName(item.getTXZ01());
            orderItem.setItemNo(item.getEBELP());
            orderItem.setQuantity(convertSapNumber(item.getMENGE()));
            orderItem.setUnit(item.getMEINS());
            orderItem.setCurrencyCode(item.getWAERS());
            orderItem.setDeliveryDate(convertSapDate(item.getEINDT()));
            orderItem.setItemType(item.getPSTYP());
            orderItem.setDelIden(item.getLOKEZ());
            orderItem.setQtyPerPack(convertSapNumber(item.getBSTRF()));
            orderItem.setPriceUnit(item.getPEINH());
            orderItem.setRemark(item.getEDATU_02());
            orderItem.setPurDocType(item.getBSART());
            orderItem.setNetPrice(convertSapNumber(item.getNETPR()));
            orderItem.setStockLoc(item.getLGORT());
            orderItem.setLocAdd(item.getLGPBE());
            orderItem.setUnloadingNo(item.getWANST());
            orderItem.setUnloadingName(item.getSORT1());
            orderItem.setCustomerOrderCode(item.getBSTNK());
            orderItem.setCustomerOrderLineCode(item.getPOSNR());
            orderItem.setRcvType(item.getZMETHD());
            orderItem.setCustomerDeliveryDate(convertSapDateTime(item.getEDATU()));
            orderItem.setProductType(item.getSTRGR());
            orderItem.setPurchaseType(item.getZMM001());
            orderItem.setDepot(item.getZDEPOT());
            orderItem.setRcvName(item.getZMM002());
            orderItem.setCustomerArticleNo(item.getKDMAT());
            orderItem.setSecure(item.getMAABC());
            orderItem.setArticleType(item.getCODE());
            orderItem.setDeliverySplit(convertSapNumber(item.getZFGHS()).intValue());
            orderItem.setOrderType(item.getAUART());
            orderItem.setSoOrderQuantity(convertSapNumber(item.getKWMEMG()));
            orderItem.setCustomerPoNo(item.getBSTNK());
            orderItem.setRankNo(item.getIHREZ());
            orderItem.setBoxPackageQuantity(item.getZXBSL());
            orderItem.setSupplierCode03(item.getLIFNR_03());
            orderItem.setSupplierCode04(item.getLIFNR_04());
            orderItem.setPoRan(item.getZPORAN());
            orderItem.setSoCreateDate(convertSapDate(item.getERDAT()));
            orderItem.setShipmentPlant02(item.getWERKS_02());
            po.getDetail().add(orderItem);
        }
        return new ArrayList<>(poMap.values());
    }

    /**
     * 将新格式的SAP订单数据转换为TblOrder对象列表
     *
     * @param request         新格式的SAP订单请求
     * @param dictDataService
     * @return TblOrder对象列表
     */
    public static List<TblOrder> convertNewFormatToTblOrder(SapOrderRequest request, ISysDictDataService dictDataService) {
        List<TblOrder> orders = new ArrayList<>();

        TblOrder order = new TblOrder();
        order.setOrderCode(request.getEBELN());
        order.setDirection(DataConstants.DIRECTION_IN);
        order.setStatus(DataConstants.ORDER_STATUS_NEW);
        order.setCompCode(request.getBUKRS());
        // 去除sap前导0
        order.setSuppCode(convertSapCode(request.getLIFNR()));
        order.setPurDocType(request.getBSART());
        order.setDelIden(request.getLOEKZ());
        order.setCurrencyCode(request.getWAERS());
        order.setCreator(request.getERNAM());

        // 解析订单日期
        if (StringUtils.isNotEmpty(request.getBEDAT())) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                order.setDocDate(sdf.parse(request.getBEDAT()));
            } catch (ParseException e) {
                throw new RuntimeException("解析订单日期失败: " + request.getBEDAT(), e);
            }
        }

        List<TblOrderItem> orderItems = new ArrayList<>();

        for (SapEkpoItem ekpoItem : request.getEKPO()) {
            // 处理每个EKET交货计划
            if (ekpoItem.getEKET() != null && !ekpoItem.getEKET().isEmpty()) {
                int index = 1;
                for (SapEketItem eketItem : ekpoItem.getEKET()) {
                    TblOrderItem orderItem = new TblOrderItem();
                    orderItem.setItemNo(ekpoItem.getEBELP());
                    orderItem.setDelIden(ekpoItem.getLOEKZ());
                    // 去除sap前导0
                    orderItem.setArticleNo(convertSapCode(ekpoItem.getMATNR()));
                    orderItem.setArticleName(ekpoItem.getTXZ01());
                    orderItem.setPlantCode(ekpoItem.getWERKS());
                    orderItem.setStockLoc(ekpoItem.getLGORT());
                    orderItem.setUnit(ekpoItem.getMEINS());
                    orderItem.setDeliveryScheduleNo(StringUtils.isNotEmpty(eketItem.getETENR()) ? eketItem.getETENR() : String.valueOf(index++));
                    // 冗余字段，同一订单不同行项目工厂代码相同
                    order.setPlantCode(ekpoItem.getWERKS());
                    order.setPlantName(dictDataService.selectDictLabel(DataConstants.DICT_TYPE_PLANT, ekpoItem.getWERKS()));

                    // 解析数量
                    if (StringUtils.isNotEmpty(eketItem.getMENGE())) {
                        try {
                            orderItem.setQuantity(convertSapNumber(eketItem.getMENGE()));
                        } catch (Exception e) {
                            throw new RuntimeException("解析订单数量失败: " + eketItem.getMENGE(), e);
                        }
                    }

                    // 解析交货日期
                    if (StringUtils.isNotEmpty(eketItem.getEINDT())) {
                        try {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                            orderItem.setDeliveryDate(sdf.parse(eketItem.getEINDT()));
                        } catch (ParseException e) {
                            throw new RuntimeException("解析交货日期失败: " + eketItem.getEINDT(), e);
                        }
                    }

                    orderItems.add(orderItem);
                }
            } else {
                // 如果没有EKET，创建一个基本的订单项目
                TblOrderItem orderItem = new TblOrderItem();
                orderItem.setItemNo(ekpoItem.getEBELP());
                orderItem.setDelIden(ekpoItem.getLOEKZ());
                orderItem.setArticleNo(ekpoItem.getMATNR());
                orderItem.setArticleName(ekpoItem.getTXZ01());
                orderItem.setPlantCode(ekpoItem.getWERKS());
                orderItem.setStockLoc(ekpoItem.getLGORT());
                orderItem.setUnit(ekpoItem.getMEINS());
                orderItem.setDeliveryScheduleNo("1"); // 默认交货计划行号
                // 冗余字段，同一订单不同行项目工厂代码相同
                order.setPlantCode(ekpoItem.getWERKS());
                order.setPlantName(dictDataService.selectDictLabel(DataConstants.DICT_TYPE_PLANT, ekpoItem.getWERKS()));

                // 解析数量
                if (StringUtils.isNotEmpty(ekpoItem.getMENGE())) {
                    try {
                        orderItem.setQuantity(convertSapNumber(ekpoItem.getMENGE()));
                    } catch (Exception e) {
                        throw new RuntimeException("解析订单数量失败: " + ekpoItem.getMENGE(), e);
                    }
                }

                orderItems.add(orderItem);
            }
        }

        order.setDetail(orderItems);
        orders.add(order);

        return orders;
    }

    /**
     * 将验收明细请求转换为TblFeedback对象
     */
    public static TblFeedback convertToFeedback(AcceptanceDetailRequest request) {
        TblFeedback feedback = new TblFeedback();

        // 设置头部信息
        feedback.setDnNo(request.getZsett()); // 使用结算单号
        feedback.setDelFlag(request.getLoekz()); // 删除标识
        feedback.setCompCode(request.getBukrs()); // 公司代码
        feedback.setCompName(request.getButxt()); // 公司名称
        feedback.setSuppCode(request.getLifnr()); // 供应商编号
        feedback.setSuppName(request.getName1()); // 供应商名称
        feedback.setDocDate(request.getBldat()); // 凭证日期
        feedback.setTotalAmount(request.getWrbtr()); // 总金额
        feedback.setCurrency(request.getWaers()); // 货币

        // 从第一个明细项目获取工厂信息
        if (request.getItems() != null && !request.getItems().isEmpty()) {
            // sap田亚成确认同一个下所有行项目的工厂相同 2025/06/24
            feedback.setPlantCode(request.getItems().get(0).getWerks());
        }

        // 转换明细项目
        List<TblFeedbackItem> items = new ArrayList<>();
        if (request.getItems() != null) {
            for (AcceptanceDetailItem item : request.getItems()) {
                TblFeedbackItem feedbackItem = new TblFeedbackItem();

                // 设置新增字段
                feedbackItem.setSeqNo(item.getZseno()); // 序号
                feedbackItem.setPlantCode(item.getWerks()); // 工厂代码
                feedbackItem.setOrderCode(item.getEbeln()); // 采购订单号
                feedbackItem.setOrderLineNo(item.getEbelp()); // 采购订单行项目
                feedbackItem.setArticleNo(item.getMatnr()); // 物料编号
                feedbackItem.setArticleName(item.getTxz01()); // 物料描述
                feedbackItem.setUnit(item.getMeins()); // 单位
                feedbackItem.setItemAmount(item.getDmbtr()); // 行项目金额
                feedbackItem.setTaxCode(item.getMwskz()); // 税码
                feedbackItem.setRcvDocNo(item.getMblnr()); // 物料凭证号

                // 转换过账日期
                if (item.getBudat() != null && !item.getBudat().isEmpty()) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                        feedbackItem.setRcvDate(sdf.parse(item.getBudat()));
                    } catch (ParseException e) {
                        throw new RuntimeException("无法解析过账日期: " + item.getBudat(), e);
                    }
                }

                // 转换数量
                if (item.getMenge() != null && !item.getMenge().isEmpty()) {
                    try {
                        feedbackItem.setQuantity(new BigDecimal(item.getMenge()));
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("无法解析数量: " + item.getMenge(), e);
                    }
                }

                // 安全转换整数字段
                if (item.getMjahr() != null && !item.getMjahr().isEmpty()) {
                    try {
                        feedbackItem.setArticleDocAnnual(Integer.valueOf(item.getMjahr()));
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("无法解析物料凭证年度: " + item.getMjahr(), e);
                    }
                }

                if (item.getMblpo() != null && !item.getMblpo().isEmpty()) {
                    try {
                        feedbackItem.setRcvDocItemNo(Integer.valueOf(item.getMblpo()));
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("无法解析物料凭证行项目: " + item.getMblpo(), e);
                    }
                }

                feedbackItem.setCreateBy(SecurityUtils.getUsername());
                items.add(feedbackItem);
            }
        }

        feedback.setDetail(items);
        return feedback;
    }

    public static SysDept convertToDept(SapSupplierRequest request)
    {
        SysDept dept = new SysDept();
        dept.setSupplierCode(request.getLifnr());
        dept.setDeptName(request.getName1());
        dept.setSupplierType(request.getKtokk());
        dept.setCountry(request.getLand1());
        dept.setRegion(request.getRegio());
        dept.setAddress(request.getAddr2Street());
        dept.setPostcode(request.getAddr2Post());
        dept.setFax(request.getTelfx());
        dept.setPhone(request.getTelx1());
        return dept;
    }

    /**
     * 将ASN数据转换为SAP请求体
     *
     * @param asn ASN对象
     * @param orderList 关联的订单列表
     * @return SAP请求体Map
     */
    public static Map<String, Object> convertToSapAsn(TblAsn asn, List<TblOrder> orderList) {
        // 构建SAP请求体
        Map<String, Object> requestBody = new HashMap<>();
        
        // 检查ASN头部必要字段
        if (asn.getDeliveryDate() == null) {
            throw new RuntimeException("交货日期(LFDAT)不能为空");
        }
        if (StringUtils.isEmpty(asn.getAsnCode())) {
            throw new RuntimeException("ASN编号(ZZASN)不能为空");
        }
        if (StringUtils.isEmpty(asn.getSuppCode())) {
            throw new RuntimeException("供应商编码(LIFNR)不能为空");
        }
        
        requestBody.put("LFDAT", DateFormatUtils.format(asn.getDeliveryDate(), "yyyyMMdd", TimeZone.getTimeZone("GMT+8")));
        requestBody.put("ZZASN", asn.getAsnCode());
        requestBody.put("LIFNR", asn.getSuppCode());
        
        List<Map<String, Object>> itemList = new ArrayList<>();
        
        // 遍历ASN行项目和物料
        for (TblAsnItem item : asn.getDetail()) {
            for (TblAsnArticle article : item.getArticles()) {
                Map<String, Object> itemMap = new HashMap<>();
                
                // 检查行项目必要字段
                if (StringUtils.isEmpty(item.getOrderCode())) {
                    throw new RuntimeException("采购订单号(EBELN)不能为空，ASN编号：" + asn.getAsnCode());
                }
                if (StringUtils.isEmpty(article.getOrderLineNo())) {
                    throw new RuntimeException("行项目(EBELP)不能为空，采购订单号：" + item.getOrderCode() + "，ASN编号：" + asn.getAsnCode());
                }
                if (article.getQuantity() == null) {
                    throw new RuntimeException("数量(LFIMG)不能为空，采购订单号：" + item.getOrderCode() + "，行项目：" + article.getOrderLineNo() + "，ASN编号：" + asn.getAsnCode());
                }
                if (StringUtils.isEmpty(item.getRcvLocNo())) {
                    throw new RuntimeException("库存地点(LGORT)不能为空，采购订单号：" + item.getOrderCode() + "，ASN编号：" + asn.getAsnCode());
                }
                
                // 设置基本字段
                itemMap.put("EBELN", item.getOrderCode());
                itemMap.put("EBELP", article.getOrderLineNo());
                itemMap.put("LFIMG", article.getQuantity().toString());
                itemMap.put("LGORT", item.getRcvLocNo());
                
                String meins = "";
                // 从物料中获取单位信息
                if (StringUtils.isEmpty(meins)) {
                    meins = article.getUnit();
                }

                // 如果没有从物料获取到单位，则使用订单的单位
                for (TblOrder order : orderList) {
                    if (order.getOrderCode().equals(item.getOrderCode())) {
                        for (TblOrderItem orderItem : order.getDetail()) {
                            if (orderItem.getItemNo().equals(article.getOrderLineNo())) {
                                meins = orderItem.getUnit();
                                break;
                            }
                        }
                        break;
                    }
                }


                // 检查单位是否存在
                if (StringUtils.isEmpty(meins)) {
                    throw new RuntimeException("单位(MEINS)不能为空，采购订单号：" + item.getOrderCode() + "，行项目：" + article.getOrderLineNo() + "，ASN编号：" + asn.getAsnCode());
                }
                
                itemMap.put("MEINS", meins);
                itemList.add(itemMap);
            }
        }
        
        // 检查是否有行项目
        if (itemList.isEmpty()) {
            throw new RuntimeException("ASN行项目不能为空，ASN编号：" + asn.getAsnCode());
        }
        
        requestBody.put("ITEMS", itemList);
        return requestBody;
    }

    public static Map<String,Object> convertToMap(SapAsnItem item){
        Map<String,Object> map = new HashMap<>();
        map.put("LFDAT",item.getLFDAT());
        map.put("LFIMG",item.getLFIMG());
        map.put("LFUHR",item.getLFUHR());
        map.put("LGORT",item.getLGORT());
        map.put("MATNR",item.getMATNR());
        map.put("VERUR_LA",item.getVERUR_LA());
        map.put("VGBEL",item.getVGBEL());
        map.put("VGPOS",item.getVGPOS());
        map.put("VSTEL",item.getVSTEL());
        map.put("WADAT",item.getWADAT());
        map.put("WERKS",item.getWERKS());
        map.put("BSTRF",item.getBSTRF());
        map.put("MEINS",item.getMEINS());
        return map;
    }

    /**
     * SAP编码转换，去除前导0
     * @param codeStr
     * @return
     */
    public static String convertSapCode(String codeStr) {
        return codeStr.replaceAll("^0+", "");
    }

    public static BigDecimal convertSapNumber(String numberStr){
        String str = numberStr.trim();
        if (str.endsWith("-")){
            str = "-"+str.replace("-","");
        }else if(str.endsWith("+")){
            str = str.replace("+","");
        }
        return new BigDecimal(str);
    }

    public static Date convertSapDateTime(String dateStr){
        if(dateStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("yyyyMMddHHmmss", dateStr.trim());
    }

    public static Date convertSapDate(String dateStr){
        if(dateStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("yyyyMMdd", dateStr.trim());
    }

    public static Date convertSapTime(String timeStr){
        if(timeStr.trim().replaceAll("0", "").isEmpty()){
            return null;
        }
        return DateUtils.dateTime("HHmmss", timeStr.trim());
    }

    // 根据配置从部品番号中拆分制品区分字段
    public static String handleProductClass(ISysDictDataService dictDataService, String partNumber){
        if (StringUtils.isNotEmpty(dictDataService.selectDictLabel("split_part_number", partNumber))) {
            return partNumber.substring(partNumber.indexOf("+") + 1);
        }
//        List<SysDictData> splitPartNumbers = dictTypeService.selectDictDataByType("split_part_number");
//        if (splitPartNumbers.stream().anyMatch(item -> partNumber.equals(item.getDictValue()))) {
//            return partNumber.substring(partNumber.indexOf("+") + 1);
//        }
        return "";
    }
}
