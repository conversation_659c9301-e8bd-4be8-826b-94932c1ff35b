package com.datalink.common.utils;

import com.datalink.common.DataConstants;
import com.datalink.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * WMS接口调用工具类
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
@Component
public class WmsApiClient {

    private static final Logger logger = LoggerFactory.getLogger(WmsApiClient.class);

    @Autowired
    private ISysConfigService configService;


    /**
     * 调用WMS接口（自定义请求体）
     *
     * @param wmsUrl WMS接口URL
     * @param requestBody 自定义请求体
     * @return WMS接口返回结果
     */
    public HashMap<String, Object> callWmsApi(String wmsUrl, Map<String, Object> requestBody) {
        try {
            logger.info("开始调用WMS接口，URL: {}, 请求体: {}", wmsUrl, requestBody);

            // 获取WMS认证信息

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);

            // 调用WMS接口
            HashMap<String, Object> result = restTemplate.postForObject(wmsUrl, entity, HashMap.class);
            
            logger.info("WMS接口调用成功，返回结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("调用WMS接口失败，URL: {}, 错误信息: {}", wmsUrl, e.getMessage(), e);
            throw new RuntimeException("调用WMS接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用WMS接口（完全自定义）
     *
     * @param wmsUrl WMS接口URL
     * @param wmsUser WMS用户名
     * @param wmsPass WMS密码
     * @param requestBody 请求体
     * @return WMS接口返回结果
     */
    public HashMap<String, Object> callWmsApi(String wmsUrl, String wmsUser, String wmsPass, Map<String, Object> requestBody) {
        try {
            logger.info("开始调用WMS接口，URL: {}, 用户: {}", wmsUrl, wmsUser);

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 设置Basic认证
            String auth = wmsUser + ":" + wmsPass;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            headers.set("Authorization", "Basic " + encodedAuth);

            // 创建请求实体
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);

            // 调用WMS接口
            HashMap<String, Object> result = restTemplate.postForObject(wmsUrl, entity, HashMap.class);
            
            logger.info("WMS接口调用成功，返回结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("调用WMS接口失败，URL: {}, 错误信息: {}", wmsUrl, e.getMessage(), e);
            throw new RuntimeException("调用WMS接口失败: " + e.getMessage(), e);
        }
    }
}
