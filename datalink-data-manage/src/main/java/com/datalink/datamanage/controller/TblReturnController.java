package com.datalink.datamanage.controller;

import java.util.List;

import com.datalink.datamanage.domain.TblReturnBox;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblReturn;
import com.datalink.datamanage.service.ITblReturnService;
import com.datalink.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 退货主表Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Api(tags = "退货管理")
@RestController
@RequestMapping("/datamanage/return")
public class TblReturnController extends BaseController
{
    @Autowired
    private ITblReturnService tblReturnService;

    /**
     * 接口2：查询退货主表列表
     */
    @ApiOperation("查询退货列表")
    @PreAuthorize("@ss.hasPermi('datamanage:return:list')")
    @GetMapping("/list")
    public TableDataInfo list(TblReturn tblReturn)
    {
        startPage();
        List<TblReturn> list = tblReturnService.selectTblReturnList(tblReturn);
        return getDataTable(list);
    }

    /**
     * 获取退货主表详细信息
     */
//    @ApiOperation("获取退货主表详细信息")
    @PreAuthorize("@ss.hasPermi('datamanage:return:query')")
    @GetMapping(value = "/{returnId}")
    public AjaxResult getInfo(@PathVariable("returnId") Long returnId)
    {
        return AjaxResult.success(tblReturnService.selectTblReturnByReturnId(returnId));
    }

    /**
     * 根据退货单号获取退货详细信息
     */
    @ApiOperation("根据退货单id获取退货详细信息")
    @PreAuthorize("@ss.hasPermi('datamanage:return:query')")
    @GetMapping(value = "/detail/{returnId}")
    public AjaxResult getDetailByReturnNo(@PathVariable("returnId") long returnId)
    {
        TblReturn searchParam = new TblReturn();
        searchParam.setReturnId(returnId);
        List<TblReturn> list = tblReturnService.selectTblReturnWithBoxList(searchParam);
        if (list.isEmpty()) {
            return AjaxResult.error("退货单不存在，id：" + returnId);
        }
        return AjaxResult.success(list.get(0));
    }

    /**
     * 新增退货主表
     */
//    @ApiOperation("新增退货主表")
    @PreAuthorize("@ss.hasPermi('datamanage:return:add')")
    @Log(title = "退货主表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblReturn tblReturn)
    {
        return toAjax(tblReturnService.insertTblReturn(tblReturn));
    }

    /**
     * 修改退货主表
     */
//    @ApiOperation("修改退货主表")
    @PreAuthorize("@ss.hasPermi('datamanage:return:edit')")
    @Log(title = "退货主表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblReturn tblReturn)
    {
        return toAjax(tblReturnService.updateTblReturn(tblReturn));
    }

    /**
     * 接口3：修改退货批次号
     */
    @ApiOperation("修改退货批次号")
    @PreAuthorize("@ss.hasPermi('datamanage:return:edit')")
    @Log(title = "修改退货批次号", businessType = BusinessType.UPDATE)
    @PutMapping("/updateBatchNo/{returnId}")
    public AjaxResult updateBatchNo(@PathVariable("returnId") long returnId,
                                   @RequestBody List<TblReturnBox> boxList)
    {
        return tblReturnService.updateReturnBatchNo(returnId, boxList);
    }

    /**
     * 接口4：打印退货现品票
     */
    @ApiOperation("打印退货现品票")
    @PreAuthorize("@ss.hasPermi('datamanage:return:print')")
    @Log(title = "打印退货现品票", businessType = BusinessType.EXPORT)
    @GetMapping("/print")
    public AjaxResult printReturnTag(@RequestParam("returnId") long returnId, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        return tblReturnService.printReturnTag(returnId, tz);
    }

    /**
     * 回传标签信息给WMS
     */
    @ApiOperation("回传标签信息给WMS")
    @PreAuthorize("@ss.hasPermi('datamanage:return:feedback')")
    @Log(title = "回传标签信息给WMS", businessType = BusinessType.EXPORT)
    @PostMapping("/feedback/{returnId}")
    public AjaxResult feedbackToWms(@PathVariable("returnId") long returnId)
    {
        return tblReturnService.feedbackToWms(returnId);
    }

    /**
     * 删除退货主表
     */
//    @ApiOperation("删除退货主表")
    @PreAuthorize("@ss.hasPermi('datamanage:return:remove')")
    @Log(title = "退货主表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{returnIds}")
    public AjaxResult remove(@PathVariable Long[] returnIds)
    {
        return toAjax(tblReturnService.deleteTblReturnByReturnIds(returnIds));
    }
}
