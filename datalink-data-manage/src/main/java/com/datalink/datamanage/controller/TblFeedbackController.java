package com.datalink.datamanage.controller;

import com.datalink.common.DataConstants;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblFeedbackItem;
import com.datalink.datamanage.service.ITblFeedbackService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 收货反馈Controller
 * 
 * <AUTHOR>
 * @date 2021-07-06
 */
@Api(tags = "结算单管理")
@RestController
@RequestMapping("/datamanage/feedback")
public class TblFeedbackController extends BaseController
{
    @Autowired
    private ITblFeedbackService tblFeedbackService;

    /**
     * 查询收货反馈列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:list')")
    @ApiOperation("查询结算单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "操作成功", response = TblFeedback.class)
    })
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(TblFeedback tblFeedback)
    {
        startPage();
        List<TblFeedback> list = tblFeedbackService.selectTblFeedbackList(tblFeedback);
        return getDataTable(list);
    }

    /**
     * 导出收货反馈列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:export')")
    @Log(title = "收货反馈", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a")
    @GetMapping("/export")
    public AjaxResult export(TblFeedback tblFeedback)
    {
        List<TblFeedback> list = tblFeedbackService.selectTblFeedbackList(tblFeedback);
        ExcelUtil<TblFeedback> util = new ExcelUtil<TblFeedback>(TblFeedback.class);
        return util.exportExcel(list, "收货反馈数据");
    }

    /**
     * 获取收货反馈详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:query')")
    @GetMapping(value = "/{feedId}")
    public AjaxResult getInfo(@PathVariable("feedId") Long feedId)
    {
        return AjaxResult.success(tblFeedbackService.selectTblFeedbackById(feedId));
    }

    /**
     * 新增收货反馈
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:add')")
    @Log(title = "收货反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblFeedback tblFeedback)
    {
        return toAjax(tblFeedbackService.insertTblFeedback(tblFeedback));
    }

    /**
     * 修改收货反馈
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:edit')")
    @Log(title = "收货反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblFeedback tblFeedback)
    {
        return toAjax(tblFeedbackService.updateTblFeedback(tblFeedback));
    }

    /**
     * 删除收货反馈
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:remove')")
    @Log(title = "收货反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{feedIds}")
    public AjaxResult remove(@PathVariable Long[] feedIds)
    {
        return toAjax(tblFeedbackService.deleteTblFeedbackByIds(feedIds));
    }

    /**
     * 查询收货反馈行项目列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(TblFeedbackItem feedbackItem)
    {
        startPage();
        List<TblFeedbackItem> list = tblFeedbackService.selectTblFeedbackItemList(feedbackItem);
        return getDataTable(list);
    }

    /**
     * 获取收货反馈详细信息(不包含行项目)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:query')")
    @GetMapping(value = "/head/{feedId}")
    public AjaxResult getFeedbackOnly(@PathVariable("feedId") Long feedId)
    {
        return AjaxResult.success(tblFeedbackService.selectTblFeedbackOnlyById(feedId));
    }

    /**
     * 下载收货反馈txt
     */
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:list')")
    @DataScope(supplierAlias = "a")
    @PostMapping("/downloadFeedbackTxt")
    public AjaxResult downloadFeedbackTxt(@RequestBody List<Long> feedbackIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        return tblFeedbackService.downloadFeedbackTxt(feedbackIds, tz);
    }

    /**
     * 确认结算单
     */
    @ApiOperation("确认结算单")
    @ApiImplicitParam(name = "feedIds", value = "结算单ID列表", dataType = "List<Long>", allowMultiple = true)
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:confirm')")
    @Log(title = "结算单确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirm(@RequestBody List<Long> feedIds)
    {
        return tblFeedbackService.confirmFeedback(feedIds);
    }

    /**
     * 更新结算单开票信息
     */
    @ApiOperation("更新结算单开票信息")
    @ApiImplicitParam(name = "feedback", value = "结算单信息", dataType = "TblFeedback")
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:edit')")
    @Log(title = "更新结算单开票信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateInvoiceInfo")
    public AjaxResult updateInvoiceInfo(@RequestBody TblFeedback tblFeedback)
    {
        TblFeedback feedback = new TblFeedback();
        feedback.setFeedId(tblFeedback.getFeedId());
        // 已确认的结算单不允许修改开票信息
        TblFeedback existFeedback = tblFeedbackService.selectTblFeedbackOnlyById(tblFeedback.getFeedId());
        if (existFeedback == null) {
            return AjaxResult.error("结算单不存在");
        }
        if (DataConstants.FORECAST_STATUS_CONFIRMED.equals(existFeedback.getStatus())) {
            return AjaxResult.error("已确认的结算单不允许修改开票信息");
        }
        feedback.setInvoiceDate(tblFeedback.getInvoiceDate());
        feedback.setInvoiceNo(tblFeedback.getInvoiceNo());
        feedback.setInvoiceTax(tblFeedback.getInvoiceTax());
        feedback.setUpdateTime(new Date());
        feedback.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(tblFeedbackService.updateTblFeedbackOnly(feedback));
    }

    /**
     * 下载结算单Excel
     */
    @ApiOperation("下载结算单Excel")
    @ApiImplicitParam(name = "feedbackIds", value = "结算单ID列表", dataType = "List<Long>", allowMultiple = true)
    @PreAuthorize("@ss.hasPermi('datamanage:feedback:export')")
    @Log(title = "结算单Excel", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a")
    @PostMapping("/downloadExcel")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult downloadExcel(@RequestBody List<Long> feedbackIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        List<TblFeedback> list = new ArrayList<>(feedbackIds.size());
        for (Long feedId : feedbackIds) {
            TblFeedback feedback = tblFeedbackService.selectTblFeedbackById(feedId);
            if (feedback != null) {
                list.add(feedback);
                tblFeedbackService.updateTblFeedbackOnly(new TblFeedback() {{
                    setFeedId(feedId);
                    setDownloadStatus(DataConstants.FEEDBACK_STATUS_DOWNLOADED);
                    setLastDownloadTime(new Date());
                }});
            }
        }
        ExcelUtil<TblFeedback> util = new ExcelUtil<TblFeedback>(TblFeedback.class);
        return util.exportExcelFlat(list, "验收明细");
    }
}
