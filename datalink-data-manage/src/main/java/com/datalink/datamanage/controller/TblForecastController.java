package com.datalink.datamanage.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblForecastItem;
import io.swagger.annotations.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.service.ITblForecastService;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.DataConstants;

import javax.validation.Valid;

/**
 * 预测Controller
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
@Api(tags = "预测管理")
@RestController
@RequestMapping("/datamanage/forecast")
public class TblForecastController extends BaseController
{
    @Autowired
    private ITblForecastService tblForecastService;

    /**
     * 查询预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(TblForecast tblForecast)
    {
        startPage();
        List<TblForecast> list = tblForecastService.selectTblForecastList(tblForecast);
        return getDataTable(list);
    }

    /**
     * 查询预测行项目列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(TblForecastItem tblForecastItem)
    {
        startPage();
        List<TblForecastItem> list = tblForecastService.selectTblForecastItemList(tblForecastItem);
        return getDataTable(list);
    }

    /**
     * 导出预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:export')")
    @DataScope(supplierAlias = "a")
    @Log(title = "预测", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblForecast tblForecast)
    {
        List<TblForecast> list = tblForecastService.selectTblForecastList(tblForecast);
        ExcelUtil<TblForecast> util = new ExcelUtil<TblForecast>(TblForecast.class);
        return util.exportExcel(list, "预测数据");
    }

    /**
     * 获取预测详细信息（返回同分组的所有预测数据合并后的结果）
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:query')")
    @GetMapping(value = "/{forecastId}")
    public AjaxResult getInfo(@PathVariable("forecastId") Long forecastId)
    {
        // 获取同分组的所有预测数据
        List<TblForecast> groupForecasts = tblForecastService.selectTblForecastGroupWithItemsByForecastId(forecastId);

        if (groupForecasts.isEmpty()) {
            return AjaxResult.error("未找到预测数据");
        }

        // 合并同分组的预测数据，保持前端兼容性
        TblForecast mergedForecast = mergeGroupForecasts(groupForecasts);
        return AjaxResult.success(mergedForecast);
    }

    /**
     * 新增预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:add')")
    @Log(title = "预测", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblForecast tblForecast)
    {
        return toAjax(tblForecastService.insertTblForecast(tblForecast));
    }

    /**
     * 修改预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:edit')")
    @Log(title = "预测", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblForecast tblForecast)
    {
        return toAjax(tblForecastService.updateTblForecast(tblForecast));
    }

    /**
     * 删除预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:remove')")
    @Log(title = "预测", businessType = BusinessType.DELETE)
	@DeleteMapping("/{forecastIds}")
    public AjaxResult remove(@PathVariable Long[] forecastIds)
    {
        return toAjax(tblForecastService.deleteTblForecastByIds(forecastIds));
    }

    /**
     * 获取预测详细信息(不包含行项目，返回同分组的合并数据)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:query')")
    @GetMapping(value = "/head/{forecastId}")
    public AjaxResult getForecastOnly(@PathVariable("forecastId") Long forecastId)
    {
        // 获取同分组的所有预测数据（不包含行项目）
        List<TblForecast> groupForecasts = tblForecastService.selectTblForecastGroupByForecastId(forecastId);

        if (groupForecasts.isEmpty()) {
            return AjaxResult.error("未找到预测数据");
        }

        // 返回第一个预测的基本信息（因为同分组的基本信息相同）
        TblForecast firstForecast = groupForecasts.get(0);
        // 清空详情数据，因为这是head接口
        firstForecast.setDetail(null);
        return AjaxResult.success(firstForecast);
    }

    /**
     * 查询每个预测编号的最新版本预测列表
     */
    @ApiOperation(value = "查询预测列表")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功", response = TblForecast.class)
    })
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/latestVersionList")
    public TableDataInfo latestVersionList(TblForecast tblForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        startPage();
        List<TblForecast> list = tblForecastService.selectTblForecastLatestVersionList(tblForecast, tz);
        return getDataTable(list);
    }

    /**
     * 导出每个预测编号的最新版本预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:export')")
    @DataScope(supplierAlias = "a")
    @Log(title = "导出预测excel", businessType = BusinessType.EXPORT)
    @GetMapping("/exportLatestVersion")
    public AjaxResult exportLatestVersion(TblForecast tblForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        List<TblForecast> list = tblForecastService.selectTblForecastLatestVersionList(tblForecast, tz);
        
        // 更新下载状态和下载时间
        if (list != null && !list.isEmpty()) {
            for (TblForecast forecast : list) {
                TblForecast updateForecast = new TblForecast();
                updateForecast.setForecastId(forecast.getForecastId());
                updateForecast.setDownloadStatus(DataConstants.FORECAST_STATUS_DOWNLOADED); // 使用下载完成状态常量
                updateForecast.setLastDownloadTime(new Date()); // 设置当前时间为下载时间
                tblForecastService.updateTblForecastOnly(updateForecast);
            }
        }
        
        ExcelUtil<TblForecast> util = new ExcelUtil<TblForecast>(TblForecast.class);
        return util.exportExcel(list, "最新版本预测数据");
    }

    /**
     * 预测确认
     */
    @ApiOperation("预测确认")
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:confirm')")
    @Log(title = "预测确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirm(@RequestBody List<Long> forecastIds)
    {
        return toAjax(tblForecastService.confirmForecasts(forecastIds));
    }

    /**
     * 下载预测Excel
     */
    @ApiOperation("下载预测Excel")
    @ApiImplicitParam(name = "forecastIds", value = "预测ID列表", dataType = "List<Long>", allowMultiple = true)
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:export')")
    @Log(title = "预测Excel", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a")
    @PostMapping("/downloadExcel")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult downloadExcel(@RequestBody List<Long> forecastIds, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+8") String tz)
    {
        List<TblForecast> list = new ArrayList<>(forecastIds.size());
        Date downloadTime = new Date();
        String currentUser = SecurityUtils.getUsername();

        for (Long forecastId : forecastIds) {
            TblForecast forecast = tblForecastService.selectTblForecastById(forecastId);
            if (forecast != null) {
                list.add(forecast);
                // 更新下载状态，适应新的分组方式
                TblForecast updateForecast = new TblForecast();
                updateForecast.setForecastId(forecastId);
                updateForecast.setDownloadStatus(DataConstants.FORECAST_STATUS_DOWNLOADED);
                updateForecast.setLastDownloadTime(downloadTime);
                updateForecast.setUpdateBy(currentUser);
                tblForecastService.updateTblForecastOnly(updateForecast);
            }
        }

        ExcelUtil<TblForecast> util = new ExcelUtil<TblForecast>(TblForecast.class);
        return util.exportExcelFlat(list, "预测数据");
    }

    /**
     * 合并同分组的预测数据，保持前端兼容性
     *
     * @param groupForecasts 同分组的预测列表
     * @return 合并后的预测对象
     */
    private TblForecast mergeGroupForecasts(List<TblForecast> groupForecasts) {
        if (groupForecasts.isEmpty()) {
            return null;
        }

        // 使用第一个预测作为基础对象
        TblForecast mergedForecast = groupForecasts.get(0);

        // 合并所有预测的行项目
        List<TblForecastItem> allItems = new ArrayList<>();
        for (TblForecast forecast : groupForecasts) {
            if (forecast.getDetail() != null) {
                allItems.addAll(forecast.getDetail());
            }
        }

        // 设置合并后的行项目
        mergedForecast.setDetail(allItems);

        // 可以在这里设置一些合并后的标识，比如在预测编号后加上分组信息
        String groupInfo = String.format("(%s-%s-%s)",
            mergedForecast.getSuppCode(),
            mergedForecast.getPlantCode(),
            mergedForecast.getCreateTime() != null ?
                new java.text.SimpleDateFormat("yyyy-MM-dd").format(mergedForecast.getCreateTime()) : "");

        return mergedForecast;
    }

}
