package com.datalink.datamanage.mapper;

import java.util.List;
import com.datalink.datamanage.domain.TblAsnBox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ASN箱信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface TblAsnBoxMapper 
{
    /**
     * 查询ASN箱信息
     * 
     * @param boxId ASN箱信息ID
     * @return ASN箱信息
     */
    public TblAsnBox selectTblAsnBoxById(Long boxId);

    /**
     * 查询ASN箱信息列表
     * 
     * @param tblAsnBox ASN箱信息
     * @return ASN箱信息集合
     */
    public List<TblAsnBox> selectTblAsnBoxList(TblAsnBox tblAsnBox);

    /**
     * 根据物料ID查询ASN箱信息列表
     * 
     * @param articleId ASN物料ID
     * @return ASN箱信息集合
     */
    public List<TblAsnBox> selectTblAsnBoxByArticleId(Long articleId);

    /**
     * 根据ASN ID查询ASN箱信息列表
     * 
     * @param asnId ASN ID
     * @return ASN箱信息集合
     */
    public List<TblAsnBox> selectTblAsnBoxByAsnId(Long asnId);

    /**
     * 新增ASN箱信息
     * 
     * @param tblAsnBox ASN箱信息
     * @return 结果
     */
    public int insertTblAsnBox(TblAsnBox tblAsnBox);

    /**
     * 批量新增ASN箱信息
     * 
     * @param tblAsnBoxList ASN箱信息列表
     * @return 结果
     */
    public int batchInsertTblAsnBox(List<TblAsnBox> tblAsnBoxList);

    /**
     * 修改ASN箱信息
     * 
     * @param tblAsnBox ASN箱信息
     * @return 结果
     */
    public int updateTblAsnBox(TblAsnBox tblAsnBox);

    /**
     * 删除ASN箱信息
     * 
     * @param boxId ASN箱信息ID
     * @return 结果
     */
    public int deleteTblAsnBoxById(Long boxId);

    /**
     * 批量删除ASN箱信息
     * 
     * @param boxIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblAsnBoxByIds(Long[] boxIds);

    /**
     * 根据物料ID删除ASN箱信息
     * 
     * @param articleId ASN物料ID
     * @return 结果
     */
    public int deleteTblAsnBoxByArticleId(Long articleId);

    /**
     * 根据ASN ID删除ASN箱信息
     * 
     * @param asnId ASN ID
     * @return 结果
     */
    public int deleteTblAsnBoxByAsnId(Long asnId);
}
