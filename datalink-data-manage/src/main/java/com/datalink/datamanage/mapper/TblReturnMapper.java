package com.datalink.datamanage.mapper;

import java.util.List;
import com.datalink.datamanage.domain.TblReturn;
import org.apache.ibatis.annotations.Mapper;

/**
 * 退货主表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Mapper
public interface TblReturnMapper 
{
    /**
     * 查询退货主表
     * 
     * @param returnId 退货主表主键
     * @return 退货主表
     */
    public TblReturn selectTblReturnByReturnId(Long returnId);

    /**
     * 根据退货单号查询退货主表
     * 
     * @param returnNo 退货单号
     * @return 退货主表
     */
    public TblReturn selectTblReturnByReturnNo(String returnNo);

    /**
     * 查询退货主表列表
     * 
     * @param tblReturn 退货主表
     * @return 退货主表集合
     */
    public List<TblReturn> selectTblReturnList(TblReturn tblReturn);

    /**
     * 查询退货主表列表（包含箱子信息）
     * 
     * @param tblReturn 退货主表
     * @return 退货主表集合
     */
    public List<TblReturn> selectTblReturnWithBoxList(TblReturn tblReturn);

    /**
     * 新增退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    public int insertTblReturn(TblReturn tblReturn);

    /**
     * 修改退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    public int updateTblReturn(TblReturn tblReturn);

    /**
     * 删除退货主表
     * 
     * @param returnId 退货主表主键
     * @return 结果
     */
    public int deleteTblReturnByReturnId(Long returnId);

    /**
     * 批量删除退货主表
     * 
     * @param returnIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTblReturnByReturnIds(Long[] returnIds);
}
