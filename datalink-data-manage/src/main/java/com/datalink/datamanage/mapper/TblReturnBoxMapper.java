package com.datalink.datamanage.mapper;

import java.util.List;
import com.datalink.datamanage.domain.TblReturnBox;
import org.apache.ibatis.annotations.Mapper;

/**
 * 退货箱表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Mapper
public interface TblReturnBoxMapper 
{
    /**
     * 查询退货箱表
     * 
     * @param boxId 退货箱表主键
     * @return 退货箱表
     */
    public TblReturnBox selectTblReturnBoxByBoxId(Long boxId);

    /**
     * 查询退货箱表列表
     * 
     * @param tblReturnBox 退货箱表
     * @return 退货箱表集合
     */
    public List<TblReturnBox> selectTblReturnBoxList(TblReturnBox tblReturnBox);

    /**
     * 根据退货ID查询退货箱表列表
     *
     * @param returnId 退货ID
     * @return 退货箱表集合
     */
    public List<TblReturnBox> selectTblReturnBoxByReturnId(Long returnId);

    /**
     * 新增退货箱表
     * 
     * @param tblReturnBox 退货箱表
     * @return 结果
     */
    public int insertTblReturnBox(TblReturnBox tblReturnBox);

    /**
     * 批量新增退货箱表
     * 
     * @param tblReturnBoxList 退货箱表列表
     * @return 结果
     */
    public int batchInsertTblReturnBox(List<TblReturnBox> tblReturnBoxList);

    /**
     * 修改退货箱表
     * 
     * @param tblReturnBox 退货箱表
     * @return 结果
     */
    public int updateTblReturnBox(TblReturnBox tblReturnBox);

    /**
     * 批量修改退货箱表批次号
     * 
     * @param tblReturnBoxList 退货箱表列表
     * @return 结果
     */
    public int batchUpdateTblReturnBoxBatch(List<TblReturnBox> tblReturnBoxList);

    /**
     * 删除退货箱表
     * 
     * @param boxId 退货箱表主键
     * @return 结果
     */
    public int deleteTblReturnBoxByBoxId(Long boxId);

    /**
     * 批量删除退货箱表
     * 
     * @param boxIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTblReturnBoxByBoxIds(Long[] boxIds);

    /**
     * 根据退货ID删除退货箱表
     *
     * @param returnId 退货ID
     * @return 结果
     */
    public int deleteTblReturnBoxByReturnId(Long returnId);
}
