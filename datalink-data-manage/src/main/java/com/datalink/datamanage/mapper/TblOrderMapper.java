package com.datalink.datamanage.mapper;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.FlatOrder;
import com.datalink.datamanage.domain.TblOrder;
import com.datalink.datamanage.domain.TblOrderAsnQuantity;
import com.datalink.datamanage.domain.TblOrderItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 订单Mapper接口
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
public interface TblOrderMapper
{
    /**
     * 查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    public TblOrder selectTblOrderById(Long orderId);

    /**
     * 查询订单列表
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderList(TblOrder tblOrder);

    /**
     * 查询未删除订单列表
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectValidTblOrderList(TblOrder tblOrder);

    /**
     * 查询订单列表(包含行项目)
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderWithItemList(TblOrder tblOrder);

    /**
     * 查询订单列表（包括订单行项目）--接口专用
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderFullList(TblOrder tblOrder);

    /**
     * 新增订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    public int insertTblOrder(TblOrder tblOrder);

    /**
     * 修改订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    public int updateTblOrder(TblOrder tblOrder);

    /**
     * 删除订单
     *
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteTblOrderById(Long orderId);

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblOrderByIds(Long[] orderIds);

    /**
     * 批量删除订单行项目
     *
     * @param orderIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblOrderItemByOrderIds(Long[] orderIds);

    /**
     * 批量新增订单行项目
     *
     * @param tblOrderItemList 订单行项目列表
     * @return 结果
     */
    public int batchTblOrderItem(List<TblOrderItem> tblOrderItemList);


    /**
     * 通过订单ID删除订单行项目信息
     *
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteTblOrderItemByOrderId(Long orderId);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询订单行项目列表
     *
     * @param orderItem 订单行项目
     * @return 订单行项目集合
     */
    public List<TblOrderItem> selectTblOrderItemList(TblOrderItem orderItem);

    /**
     * 查询订单(不包含行项目)
     *
     * @param order 订单
     * @return 订单
     */
    @DataScope(supplierAlias = "a")
    public TblOrder selectTblOrderOnlyById(TblOrder order);

    /**
     * 批量新增订单物料剩余数量
     *
     * @param tblOrderAsnQuantityList 订单物料剩余数量列表
     * @return 结果
     */
    public int batchTblOrderAsnQuantity(List<TblOrderAsnQuantity> tblOrderAsnQuantityList);

    /**
     * 更新订单完成状态
     *
     * @param orderCode 订单编号
     * @return 结果
     */
    public int updateOrderCompleteStatus(String orderCode);

    public int updateOrderByCodeMap(Map<String, Object> map);

    public int markRead(Long orderId);

    List<FlatOrder> selectFlatOrderList(FlatOrder flatOrder);

    public int updateTblOrderAsnQuantity(TblOrderAsnQuantity quantity);

    /**
     * 批量更新订单状态
     *
     * @param orderIds 订单ID列表
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateOrderStatus(@Param("orderIds")List<Long> orderIds, @Param("status")String status);
}
