package com.datalink.datamanage.service;

import com.datalink.common.core.domain.entity.SysUser;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.EmailUtil;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblOrder;
import com.datalink.datamanage.domain.TblReturn;
import com.datalink.system.service.ISysConfigService;
import com.datalink.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 邮件通知服务
 * 
 * <AUTHOR>
 */
@Service
public class EmailNotificationService {
    
    private static final Logger log = LoggerFactory.getLogger(EmailNotificationService.class);
    
    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    /**
     * 发送订单通知邮件
     * 
     * @param order 订单信息
     */
    @Async
    public void sendOrderNotification(TblOrder order) {
        try {
            // 检查邮件功能是否启用
            if (!isEmailEnabled()) {
                log.info("邮件功能未启用，跳过订单通知邮件发送");
                return;
            }
            
            // 获取收件人列表
            List<String> recipients = getSupplierUserEmails(order.getSuppCode());
            if (recipients.isEmpty()) {
                log.warn("未找到供应商 {} 的有效邮箱地址", order.getSuppCode());
                return;
            }
            
            // 构建邮件内容
            String template = configService.selectConfigByKey("email.template.order");
            Map<String, Object> variables = new HashMap<>();
            variables.put("orderCode", order.getOrderCode());
            variables.put("viewLink", buildViewLink("order", order.getOrderId() + ""));
            
            String content = EmailUtil.replaceTemplateVariables(template, variables);
            String subject = "【SPS_HMC】新订单通知 - " + order.getOrderCode();
            
            // 发送邮件
            EmailUtil.EmailResult result = sendEmail(recipients, subject, content);
            
            log.info("订单通知邮件发送完成 - 订单: {}, 供应商: {}, 结果: {}", 
                    order.getOrderCode(), order.getSuppCode(), result.getMessage());
                    
        } catch (Exception e) {
            log.error("发送订单通知邮件异常 - 订单: {}, 供应商: {}", 
                    order.getOrderCode(), order.getSuppCode(), e);
        }
    }
    
    /**
     * 发送预测通知邮件
     * 
     * @param forecast 预测信息
     */
    @Async
    public void sendForecastNotification(TblForecast forecast) {
        try {
            if (!isEmailEnabled()) {
                log.info("邮件功能未启用，跳过预测通知邮件发送");
                return;
            }
            
            List<String> recipients = getSupplierUserEmails(forecast.getSuppCode());
            if (recipients.isEmpty()) {
                log.warn("未找到供应商 {} 的有效邮箱地址", forecast.getSuppCode());
                return;
            }
            
            String template = configService.selectConfigByKey("email.template.forecast");
            Map<String, Object> variables = new HashMap<>();
            variables.put("supplierName", forecast.getSuppName());
            variables.put("forecastCode", forecast.getForecastCode());
            variables.put("plantName", forecast.getPlantName());
            variables.put("receiveTime", DateUtils.getTime());
            variables.put("viewLink", buildViewLink("forecast", forecast.getForecastId() + ""));
            
            String content = EmailUtil.replaceTemplateVariables(template, variables);
            String subject = "【SPS_HMC】新预测通知 - " + forecast.getForecastCode();
            
            EmailUtil.EmailResult result = sendEmail(recipients, subject, content);
            
            log.info("预测通知邮件发送完成 - 预测: {}, 供应商: {}, 结果: {}", 
                    forecast.getForecastCode(), forecast.getSuppCode(), result.getMessage());
                    
        } catch (Exception e) {
            log.error("发送预测通知邮件异常 - 预测: {}, 供应商: {}", 
                    forecast.getForecastCode(), forecast.getSuppCode(), e);
        }
    }
    
    /**
     * 发送结算单通知邮件
     * 
     * @param feedback 结算单信息
     */
    @Async
    public void sendFeedbackNotification(TblFeedback feedback) {
        try {
            if (!isEmailEnabled()) {
                log.info("邮件功能未启用，跳过结算单通知邮件发送");
                return;
            }
            
            List<String> recipients = getSupplierUserEmails(feedback.getSuppCode());
            if (recipients.isEmpty()) {
                log.warn("未找到供应商 {} 的有效邮箱地址", feedback.getSuppCode());
                return;
            }
            
            String template = configService.selectConfigByKey("email.template.feedback");
            Map<String, Object> variables = new HashMap<>();
            variables.put("supplierName", feedback.getSuppName());
            variables.put("dnNo", feedback.getDnNo());
            variables.put("plantName", feedback.getPlantName());
            variables.put("receiveTime", DateUtils.getTime());
            variables.put("viewLink", buildViewLink("feedback", feedback.getFeedId() + ""));
            
            String content = EmailUtil.replaceTemplateVariables(template, variables);
            String subject = "【SPS_HMC】新结算单通知 - " + feedback.getDnNo();
            
            EmailUtil.EmailResult result = sendEmail(recipients, subject, content);
            
            log.info("结算单通知邮件发送完成 - 结算单: {}, 供应商: {}, 结果: {}", 
                    feedback.getDnNo(), feedback.getSuppCode(), result.getMessage());
                    
        } catch (Exception e) {
            log.error("发送结算单通知邮件异常 - 结算单: {}, 供应商: {}", 
                    feedback.getDnNo(), feedback.getSuppCode(), e);
        }
    }
    
    /**
     * 发送退货通知邮件
     * 
     * @param returnData 退货信息
     */
    @Async
    public void sendReturnNotification(TblReturn returnData) {
        try {
            if (!isEmailEnabled()) {
                log.info("邮件功能未启用，跳过退货通知邮件发送");
                return;
            }
            
            List<String> recipients = getSupplierUserEmails(returnData.getSuppCode());
            if (recipients.isEmpty()) {
                log.warn("未找到供应商 {} 的有效邮箱地址", returnData.getSuppCode());
                return;
            }
            
            String template = configService.selectConfigByKey("email.template.return");
            Map<String, Object> variables = new HashMap<>();
            variables.put("supplierName", returnData.getSuppCode()); // 退货表中没有供应商名称，使用代码
            variables.put("returnNo", returnData.getReturnNo());
            variables.put("receiveTime", DateUtils.getTime());
            variables.put("viewLink", buildViewLink("return", returnData.getReturnId() + ""));
            
            String content = EmailUtil.replaceTemplateVariables(template, variables);
            String subject = "【SPS_HMC】新退货通知 - " + returnData.getReturnNo();
            
            EmailUtil.EmailResult result = sendEmail(recipients, subject, content);
            
            log.info("退货通知邮件发送完成 - 退货单: {}, 供应商: {}, 结果: {}", 
                    returnData.getReturnNo(), returnData.getSuppCode(), result.getMessage());
                    
        } catch (Exception e) {
            log.error("发送退货通知邮件异常 - 退货单: {}, 供应商: {}", 
                    returnData.getReturnNo(), returnData.getSuppCode(), e);
        }
    }
    
    /**
     * 检查邮件功能是否启用
     */
    private boolean isEmailEnabled() {
        String enabled = configService.selectConfigByKey("email.enabled");
        return "true".equalsIgnoreCase(enabled);
    }
    
    /**
     * 根据供应商代码获取用户邮箱列表
     */
    private List<String> getSupplierUserEmails(String suppCode) {
        if (StringUtils.isEmpty(suppCode)) {
            return Collections.emptyList();
        }

        // 直接查询该供应商下有邮箱的用户
        List<SysUser> supplierUsers = userService.selectUsersBySupplierCode(suppCode);

        // 过滤出有效邮箱地址
        return supplierUsers.stream()
                .filter(user -> StringUtils.isNotEmpty(user.getEmail()) &&
                               EmailUtil.isValidEmail(user.getEmail()))
                .map(SysUser::getEmail)
                .collect(Collectors.toList());
    }
    
    /**
     * 发送邮件
     */
    public EmailUtil.EmailResult sendEmail(List<String> recipients, String subject, String content) {
        String smtpHost = configService.selectConfigByKey("smtp.host");
        String smtpPortStr = configService.selectConfigByKey("smtp.port");
        String username = configService.selectConfigByKey("smtp.username");
        String password = configService.selectConfigByKey("smtp.password");
        String sslEnabledStr = configService.selectConfigByKey("smtp.ssl.enable");
        String fromName = configService.selectConfigByKey("email.from.name");
        String fromAddress = configService.selectConfigByKey("email.from.address");

        int smtpPort = StringUtils.isNotEmpty(smtpPortStr) ? Integer.parseInt(smtpPortStr) : 587;
        boolean sslEnabled = "true".equalsIgnoreCase(sslEnabledStr);
        
        return EmailUtil.sendBatchHtmlMail(smtpHost, smtpPort, username, password, 
                                          sslEnabled, fromName, fromAddress, recipients, subject, content);
    }

    /**
     * 发送系统升级自定义通知（异步）并记录发送结果
     */
    @Async
    public void sendUpgradeNoticeAsync(String userName, String password, String email) {
        String subject = "【重要】Highly-Marelli系统升级通知：SPS开放访问及账号发送！";
        String content = buildUpgradeEmailContent(userName, password);
        String status = "FAIL";
        String message = "";
        try {
            EmailUtil.EmailResult result = sendEmail(Collections.singletonList(email), subject, content);
            status = "SUCCESS";
            message = result != null ? result.getMessage() : "";
        } catch (Exception ex) {
            message = ex.getMessage();
            log.error("发送系统升级通知失败, 用户:{}, 邮箱:{}", userName, email, ex);
        } finally {
            try {
                jdbcTemplate.update(
                    "update sys_user_account set sendStatus = ?, sendMsg = ? where user_name = ?",
                    status, truncateMsg(message), userName
                );
            } catch (Exception e) {
                log.error("更新发送状态失败, 用户:{}", userName, e);
            }
        }
    }

    private String truncateMsg(String msg) {
        if (msg == null) {
            return "";
        }
        return msg.length() > 500 ? msg.substring(0, 500) : msg;
    }

    private String buildUpgradeEmailContent(String userName, String password) {
        StringBuilder sb = new StringBuilder();
        sb.append("尊敬的用户，<br/><br/>");
        sb.append("您好！<br/><br/>");
        sb.append("为了给您带来更好的使用体验，我们即将进行系统升级。<br/><br/>");
        sb.append("经过精心准备，全新的SPS系统已开放访问！ 您可以使用以下信息登录新系统：<br/><br/>");
        sb.append("登录链接： <a href=\\\"https://sps.highly-marelli.com/cn/login?redirect=%2Findex\\\">https://sps.highly-marelli.com/cn/login?redirect=%2Findex</a><br/>");
        sb.append("用户名： ").append(escapeHtml(userName)).append("<br/>");
        sb.append("密码：").append(escapeHtml(password)).append("（强烈建议您登录后尽快修改密码）<br/><br/>");
        sb.append("重要提示：<br/>");
        sb.append("新订单数据将于9月1日开始通过SPS下发，请提前确认您可以正常登录系统。<br/>");
        sb.append("VCP系统停用后，所有业务均迁移至SPS系统。<br/>");
        sb.append("如果您在使用过程中遇到任何问题，请联系我们的工厂生管或技术支持团队（Wang Meiquan 王 美全 <EMAIL>）。<br/>");
        sb.append("感谢您一直以来对我们的支持！<br/><br/>");
        sb.append("此致，<br/><br/>");
        sb.append("海立马瑞利汽车系统有限公司");
        return sb.toString();
    }

    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }
    
    /**
     * 构建查看链接
     */
    private String buildViewLink(String type, String code) {
        String baseUrl = configService.selectConfigByKey("system.base.url");
        if (StringUtils.isEmpty(baseUrl)) {
            baseUrl = "http://localhost:8080";
        }
        
        // 根据不同类型构建不同的链接
        switch (type) {
            case "order":
                return baseUrl + "/order/detail/" + code;
            case "forecast":
                return baseUrl + "/forecast/detail/" + code;
            case "feedback":
                return baseUrl + "/feedback/detail/" + code;
            case "return":
                return baseUrl + "/return/detail/" + code;
            default:
                return baseUrl;
        }
    }
}
