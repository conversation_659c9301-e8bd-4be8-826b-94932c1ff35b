package com.datalink.datamanage.service;

import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.FlatOrder;
import com.datalink.datamanage.domain.TblOrder;
import com.datalink.datamanage.domain.TblOrderItem;

import java.util.List;
import java.util.Map;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
public interface ITblOrderService
{
    /**
     * 查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    public TblOrder selectTblOrderById(Long orderId);

    /**
     * 查询订单列表
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderList(TblOrder tblOrder);

    /**
     * 查询订单列表（包括订单行项目）--接口专用
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderFullList(TblOrder tblOrder);

    /**
     * 查询订单列表(包含行项目)
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    public List<TblOrder> selectTblOrderWithItemList(TblOrder tblOrder);

    /**
     * 新增订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    public int insertTblOrder(TblOrder tblOrder);

    /**
     * 修改订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    public int updateTblOrder(TblOrder tblOrder);

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单ID
     * @return 结果
     */
    public int deleteTblOrderByIds(Long[] orderIds);

    /**
     * 删除订单信息
     *
     * @param orderId 订单ID
     * @return 结果
     */
    public int deleteTblOrderById(Long orderId);

    /**
     * 仅更新订单信息
     *
     * @param tblOrder 订单ID
     * @return 结果
     */
    public int updateTblOrderOnly(TblOrder tblOrder);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询订单行项目列表
     *
     * @param orderItem 订单行项目
     * @return 订单行项目集合
     */
    public List<TblOrderItem> selectTblOrderItemList(TblOrderItem orderItem);

    /**
     * 查询订单(不包含行项目)
     *
     * @param orderId 订单ID
     * @return 订单
     */
    public TblOrder selectTblOrderOnlyById(Long orderId);

    /**
     * 更新订单完成状态
     *
     * @param orderCode 订单编号
     * @return 结果
     */
    public int updateOrderCompleteStatus(String orderCode);

    public int updateOrderByCodeMap(Map<String, Object> map);

    public AjaxResult printOrderPdf(List<Long> orderIds, String tz);

    public AjaxResult printInstructPdf(List<Long> orderIds, String tz);

    public AjaxResult printOrderTxt(List<Long> orderIds, String tz);

    List<FlatOrder> selectFlatOrderList(FlatOrder flatOrder, String tz);

    AjaxResult printPickingList(List<Long> orderIds, String tz);

    boolean checkOrderCompleteStatus(String orderCode);

    /**
     * 订单确认
     *
     * @param orderIds 订单ID列表
     * @return 结果
     */
    int confirmOrders(List<Long> orderIds);

    /**
     * 同时检查订单的完成状态和确认状态
     *
     * @param orderCode 订单编号
     * @return Map<String, Boolean> 包含isComplete和isConfirmed两个状态
     */
    Map<String, Boolean> checkOrderStatus(String orderCode);

    List<TblOrder> selectValidTblOrderList(TblOrder tblOrder, String tz);
}
