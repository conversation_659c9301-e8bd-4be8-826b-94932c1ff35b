package com.datalink.datamanage.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.datalink.api.domain.ReturnFeedbackResponse;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.utils.*;
import com.datalink.datamanage.domain.TblAsn;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.TblAsnItem;
import com.datalink.datamanage.domain.TblReturnBox;
import com.datalink.datamanage.mapper.TblReturnBoxMapper;
import com.datalink.datamanage.service.ITblAsnService;
import com.datalink.datamanage.service.EmailNotificationService;
import com.datalink.system.domain.SysConfig;
import com.datalink.system.service.ISysConfigService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.datalink.datamanage.mapper.TblReturnMapper;
import com.datalink.datamanage.domain.TblReturn;
import com.datalink.datamanage.service.ITblReturnService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 退货主表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Service
public class TblReturnServiceImpl implements ITblReturnService 
{
    private static final Logger logger = LoggerFactory.getLogger(TblReturnServiceImpl.class);

    @Autowired
    private TblReturnMapper tblReturnMapper;

    @Autowired
    private TblReturnBoxMapper tblReturnBoxMapper;

    @Autowired
    private ITblAsnService tblAsnService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${file.jasper-template-dir}")
    private String templatePath;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private EmailNotificationService emailNotificationService;

    @Autowired
    private WmsApiClient wmsApiClient;

    /**
     * 查询退货主表
     * 
     * @param returnId 退货主表主键
     * @return 退货主表
     */
    @Override
    public TblReturn selectTblReturnByReturnId(Long returnId)
    {
        return tblReturnMapper.selectTblReturnByReturnId(returnId);
    }

    /**
     * 根据退货单号查询退货主表
     * 
     * @param returnNo 退货单号
     * @return 退货主表
     */
    @Override
    public TblReturn selectTblReturnByReturnNo(String returnNo)
    {
        return tblReturnMapper.selectTblReturnByReturnNo(returnNo);
    }

    /**
     * 查询退货主表列表
     * 
     * @param tblReturn 退货主表
     * @return 退货主表
     */
    @Override
    public List<TblReturn> selectTblReturnList(TblReturn tblReturn)
    {
        return tblReturnMapper.selectTblReturnList(tblReturn);
    }

    /**
     * 查询退货主表列表（包含箱子信息）
     * 
     * @param tblReturn 退货主表
     * @return 退货主表集合
     */
    @Override
    @DataScope(supplierAlias = "a")
    public List<TblReturn> selectTblReturnWithBoxList(TblReturn tblReturn)
    {
        return tblReturnMapper.selectTblReturnWithBoxList(tblReturn);
    }

    /**
     * 新增退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    @Override
    public int insertTblReturn(TblReturn tblReturn)
    {
        tblReturn.setCreateTime(DateUtils.getNowDate());
        return tblReturnMapper.insertTblReturn(tblReturn);
    }

    /**
     * 修改退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    @Override
    public int updateTblReturn(TblReturn tblReturn)
    {
        tblReturn.setUpdateTime(DateUtils.getNowDate());
        return tblReturnMapper.updateTblReturn(tblReturn);
    }

    /**
     * 批量删除退货主表
     * 
     * @param returnIds 需要删除的退货主表主键
     * @return 结果
     */
    @Override
    public int deleteTblReturnByReturnIds(Long[] returnIds)
    {
        return tblReturnMapper.deleteTblReturnByReturnIds(returnIds);
    }

    /**
     * 删除退货主表信息
     * 
     * @param returnId 退货主表主键
     * @return 结果
     */
    @Override
    public int deleteTblReturnByReturnId(Long returnId)
    {
        return tblReturnMapper.deleteTblReturnByReturnId(returnId);
    }

    /**
     * 接收WMS退货信息
     * 
     * @param returnReceiveData 退货接收数据
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult receiveReturnFromWms(String returnReceiveData)
    {
        try {
            JsonNode rootNode = objectMapper.readTree(returnReceiveData);
            
            String returnNo = rootNode.get("returnNo").asText();
            String suppCode = rootNode.get("suppCode").asText();
            
            // 检查退货单是否已存在
//            TblReturn existingReturn = selectTblReturnByReturnNo(returnNo);
//            if (existingReturn != null) {
//                return AjaxResult.error("退货单号已存在：" + returnNo);
//            }
            
            // 创建退货主表记录
            TblReturn tblReturn = new TblReturn();
            // 退货单号生成规则：wms退货单号 + 流水号，流水号从0000开始，每次递增1，最大9999，超过9999则从0000开始
            String configKey = "ReturnNo";
            String currentCode = configService.selectConfigByKeyWithoutRedis(configKey);
            boolean isExist = true;
            if (StringUtils.isEmpty(currentCode)) {
                currentCode = "0";
                isExist = false;
            }
            while(currentCode.length() < 4) {
                currentCode = "0" + currentCode;
            }
            returnNo = returnNo + currentCode;
            SysConfig update = new SysConfig();
            update.setConfigKey(configKey);
            update.setConfigValue(String.valueOf(Integer.parseInt(currentCode) + 1 == 10000 ? 0 : Integer.parseInt(currentCode) + 1));
            int count = 0;
            if (!isExist) {
                count = configService.insertConfigWithoutRedis(update);
            } else {
                count = configService.updateConfigValueByKeyWithoutRedis(update);
            }
            if (count != 1){
                logger.error("退货单号生成失败，{}，{}", returnNo, suppCode);
                throw new RuntimeException("退货单号生成失败");
            }
            tblReturn.setReturnNo(returnNo);
            tblReturn.setSuppCode(suppCode);
            tblReturn.setStatus("New");
            tblReturn.setCreateTime(new Date());
            tblReturn.setCreateBy(SecurityUtils.getUsername());
            
            int result = tblReturnMapper.insertTblReturn(tblReturn);
            if (result <= 0) {
                return AjaxResult.error("创建退货主表失败");
            }
            
            // 处理箱子信息
            JsonNode boxListNode = rootNode.get("boxList");
            if (boxListNode != null && boxListNode.isArray()) {
                for (JsonNode boxNode : boxListNode) {

                    JsonNode detailNode = boxNode.get("detail");
                    if (detailNode != null && detailNode.isArray()) {
                        for (JsonNode detail : detailNode) {

                            // label数据样例："ASN@ASN10999600006%PO@4500000568%ITEM@00010%RELEASE@1%BATCH@20250715%PALLET@1%BOX@6%BQTY@10.0%"
                            // 拆分label数据获取所有字段
                            String label = detail.get("label").asText();
                            String[] labelData = label.split("%");
                            Map<String, String> labelMap = new HashMap<>();
                            for (String data : labelData) {
                                String[] keyValue = data.split("@");
                                labelMap.put(keyValue[0], keyValue[1]);
                            }


                            String asnCode = labelMap.get("ASN");
                            String orderCode = labelMap.get("PO");
                            String itemNo = labelMap.get("ITEM");
                            String releaseNo = labelMap.get("RELEASE");
                            String batchNo = labelMap.get("BATCH");
                            int boxIndex = Integer.parseInt(labelMap.get("BOX"));
                            double qty = Double.parseDouble(labelMap.get("BQTY"));

                            TblReturnBox returnBox = new TblReturnBox();
                            returnBox.setReturnId(tblReturn.getReturnId());
                            returnBox.setAsnCode(asnCode);
                            returnBox.setOrderCode(orderCode);
                            returnBox.setItemNo(itemNo);
                            returnBox.setReleaseNo(releaseNo);
                            returnBox.setOriginalBatchNo(batchNo);
                            returnBox.setOriginalLabel(label);
                            returnBox.setBoxIndex(boxIndex);
                            returnBox.setQty(qty);
                            returnBox.setStatus("New");
                            returnBox.setCreateTime(new Date());
                            returnBox.setCreateBy(SecurityUtils.getUsername());
                            
                            tblReturnBoxMapper.insertTblReturnBox(returnBox);
                        }
                    }
                }
            }

            // 发送邮件通知
            emailNotificationService.sendReturnNotification(tblReturn);

            return AjaxResult.success("接收退货信息成功");
            
        } catch (Exception e) {
            logger.error("接收WMS退货信息失败", e);
            return AjaxResult.error("接收退货信息失败：" + e.getMessage());
        }
    }

    /**
     * 修改退货批次号
     *
     * @param returnId 退货单号
     * @param boxList  箱子列表
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult updateReturnBatchNo(long returnId, List<TblReturnBox> boxList)
    {
        try {
            // 检查退货单是否存在
            TblReturn tblReturn = selectTblReturnByReturnId(returnId);
            if (tblReturn == null) {
                throw new RuntimeException("退货单不存在，id：" + returnId);
            }
            
            // 更新箱子批次号
            for (TblReturnBox box : boxList) {
                if (StringUtils.isNotEmpty(box.getNewBatchNo())) {
                    // 生成新标签
                    String newLabel = generateLabel(tblReturn.getReturnNo(), box);
                    box.setNewLabel(newLabel);
                    box.setStatus("Modified");
                    box.setUpdateTime(new Date());
                    box.setUpdateBy(SecurityUtils.getUsername());

                    int i = tblReturnBoxMapper.updateTblReturnBox(box);
                    if (i <= 0) {
                        throw new RuntimeException("更新箱子批次号失败");
                    }
                } else {
                    throw new RuntimeException("批次号不能为空");
                }
            }
            
            // 更新主表状态
            tblReturn.setStatus("Processing");
            tblReturn.setUpdateTime(new Date());
            tblReturn.setUpdateBy(SecurityUtils.getUsername());
            updateTblReturn(tblReturn);
            
            return AjaxResult.success("修改批次号成功");
            
        } catch (Exception e) {
            logger.error("修改退货批次号失败", e);
            return AjaxResult.error("修改批次号失败：" + e.getMessage());
        }
    }

    /**
     * 打印退货现品票
     *
     * @param returnId 退货单号
     * @param tz       时区
     * @return 结果
     */
    @Override
    public AjaxResult printReturnTag(long returnId, String tz)
    {
        try {
            // 检查退货单是否存在
            TblReturn tblReturn = selectTblReturnByReturnId(returnId);
            if (tblReturn == null) {
                return AjaxResult.error("退货单不存在，id：" + returnId);
            }

            if (!"Completed".equals(tblReturn.getStatus())) {
                return AjaxResult.error("退货单状态不正确，无法打印现品票");
            }

            // 获取箱子信息
            List<TblReturnBox> boxList = tblReturnBoxMapper.selectTblReturnBoxByReturnId(tblReturn.getReturnId());
            if (boxList.isEmpty()) {
                return AjaxResult.error("没有找到退货箱信息");
            }

            // 检查是否有修改过的批次号
            boolean hasModified = boxList.stream().anyMatch(box -> StringUtils.isNotEmpty(box.getNewBatchNo()));
            if (!hasModified) {
                return AjaxResult.error("批次号未修改，无法打印");
            }

            // 使用现有的现品票模板
            String jasperPath = templatePath + "xpp.jrxml";
            Map<String, Object> objectMap = genPrintDataForReturnTag(tblReturn, boxList, tz);
            String path;
            try {
                path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(),
                    (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
            } catch (Exception e) {
                return AjaxResult.error("生成PDF失败：" + e.getMessage());
            }

            return AjaxResult.success(path);

        } catch (Exception e) {
            logger.error("打印退货现品票失败", e);
            return AjaxResult.error("打印失败：" + e.getMessage());
        }
    }

    /**
     * 回传标签信息给WMS
     *
     * @param returnId 退货单号
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult feedbackToWms(long returnId)
    {
        try {
            logger.info("回传标签信息给WMS，退货单id：{}", returnId);

            // 检查退货单是否存在
            TblReturn tblReturn = selectTblReturnByReturnId(returnId);
            if (tblReturn == null) {
                throw new RuntimeException("退货单不存在，id：" + returnId);
            }

//            // 获取箱子信息
//            List<TblReturnBox> boxList = tblReturnBoxMapper.selectTblReturnBoxByReturnId(tblReturn.getReturnId());
//            if (boxList.isEmpty()) {
//                throw new RuntimeException("没有找到退货箱信息");
//            }

            // 获取退货箱信息
            List<TblReturn> returnWithBoxList = new ArrayList<>();
            TblReturn searchParam = new TblReturn();
            searchParam.setReturnId(returnId);
            returnWithBoxList = selectTblReturnWithBoxList(searchParam);

            if (returnWithBoxList.isEmpty()) {
                return AjaxResult.error("没有找到退货箱信息");
            }

            // 检查是否所有箱子都已修改批次号
            boolean allModified = returnWithBoxList.get(0).getBoxList().stream().allMatch(box -> StringUtils.isNotEmpty(box.getNewBatchNo()));
            if (!allModified) {
                throw new RuntimeException("还有箱子未修改批次号，无法回传");
            }

            // 构建回传响应
            ReturnFeedbackResponse response = buildFeedbackResponse(returnWithBoxList.get(0));
            // 参数配置中获取wms接口地址
            String wmsUrl = configService.selectConfigByKey(DataConstants.WMS_RETURN_FEEDBACK_URL);
            if (StringUtils.isEmpty(wmsUrl)) {
                return AjaxResult.error("WMS退货回调接口URL未配置");
            }
            // 调用WMS接口
            logger.info("调用WMS接口，URL: {}", wmsUrl);
            // 将response转换成Map
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> requestBody = mapper.convertValue(response, Map.class);

            HashMap<String, Object> result = wmsApiClient.callWmsApi(wmsUrl, requestBody);
            if (result != null) {
                if ((int)result.get("tag") == 1) {
                    logger.info("WMS接口调用成功，响应: {}", result);
                } else {
                    throw new RuntimeException("WMS接口调用失败，错误信息: " + result.get("msg"));
                }
            } else {
                throw new RuntimeException("调用WMS接口失败");
            }

            // 更新状态
            tblReturn.setStatus("Completed");
            tblReturn.setUpdateTime(new Date());
            tblReturn.setUpdateBy(SecurityUtils.getUsername());
            updateTblReturn(tblReturn);

            // 更新箱子状态
            for (TblReturnBox box : returnWithBoxList.get(0).getBoxList()) {
                box.setStatus("Completed");
                box.setUpdateTime(new Date());
                box.setUpdateBy(SecurityUtils.getUsername());
                tblReturnBoxMapper.updateTblReturnBox(box);
            }

            return AjaxResult.success("回传成功");

        } catch (Exception e) {
            logger.error("回传标签信息给WMS失败", e);
            return AjaxResult.error("回传失败：" + e.getMessage());
        }
    }

    private ReturnFeedbackResponse buildFeedbackResponse(TblReturn tblReturn) {
        ReturnFeedbackResponse response = new ReturnFeedbackResponse();

        // 按ASN、订单、行号等分组处理箱子信息
        if (tblReturn.getBoxList() != null) {
            List<ReturnFeedbackResponse.BoxDetail> detailList = new ArrayList<>();
            for (TblReturnBox box : tblReturn.getBoxList()) {
                ReturnFeedbackResponse.BoxDetail detail = new ReturnFeedbackResponse.BoxDetail();
                detail.setLabel(box.getOriginalLabel()); // 使用新标签
                detail.setNewLabel(box.getNewLabel());
                detail.setNewBatchNo(box.getNewBatchNo());

                detailList.add(detail);
            }
            response.setDetail(detailList);
        }

        return response;
    }

    /**
     * 生成标签编号
     *
     * @param returnNo 退货单号
     * @param box 退货箱信息
     * @return 标签编号
     */
    private String generateLabel(String returnNo, TblReturnBox box)
    {
        return "RETURN@" + returnNo + "%ASN@" + box.getAsnCode() +
                "%PO@" + box.getOrderCode() + "%ITEM@" + box.getItemNo() + "%RELEASE@" + box.getReleaseNo() +
                "%BATCH@" + box.getNewBatchNo() + "%PALLET@%BOX@" + box.getBoxIndex() + "%BQTY@" + box.getQty() + "%";
    }

    /**
     * 生成退货现品票打印数据
     *
     * @param tblReturn 退货主表
     * @param boxList 箱子列表
     * @param tz 时区
     * @return 打印数据
     */
    private Map<String, Object> genPrintDataForReturnTag(TblReturn tblReturn, List<TblReturnBox> boxList, String tz) {
        TimeZone timeZone = TimeZone.getTimeZone(tz == null ? "GMT+8" : tz);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息
        headerMap.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd", timeZone));
        headerMap.put("returnNo", tblReturn.getReturnNo());
        headerMap.put("suppCode", tblReturn.getSuppCode());

        // ASN信息缓存，避免重复查询
        Map<String, TblAsn> asnCache = new HashMap<>();
        // ASN Item信息缓存，key为asnCode+orderCode+itemNo+releaseNo
        Map<String, AsnItemInfo> asnItemCache = new HashMap<>();

        // 生成现品票数据列表
        List<Map<String, Object>> allDataList = new ArrayList<>();

        for (int i = 0; i < boxList.size(); i++) {
            TblReturnBox box = boxList.get(i);
            // 只处理有新批次号的箱子
            if (StringUtils.isNotEmpty(box.getNewBatchNo())) {
                Map<String, Object> dataMap = new HashMap<>();

                // 从ASN获取详细信息
                AsnItemInfo asnItemInfo = getAsnItemInfo(box, asnCache, asnItemCache);

                // 基本信息
                dataMap.put("partNo", asnItemInfo != null ? asnItemInfo.getArticleNo() : null);
                dataMap.put("partDescription", asnItemInfo != null ? asnItemInfo.getArticleName() : null);
                dataMap.put("supplierCode", tblReturn.getSuppCode());
                dataMap.put("supplierName", asnItemInfo != null ? asnItemInfo.getSuppName() : "");

                dataMap.put("shipQty", asnItemInfo != null ? asnItemInfo.getQuantity() : null);
                dataMap.put("snp", new BigDecimal(box.getQty()));
                dataMap.put("tailSNP", new BigDecimal(box.getQty()).compareTo(asnItemInfo != null ? asnItemInfo.getQtyPerPack() : BigDecimal.ZERO) < 0);

                dataMap.put("batch", box.getNewBatchNo());
                dataMap.put("orderNo", box.getOrderCode());
                dataMap.put("orderLineNo", box.getItemNo());
                dataMap.put("releaseNo", box.getReleaseNo());
                dataMap.put("dueDate", asnItemInfo != null && asnItemInfo.getDeliveryDate() != null ? DateFormatUtils.format(asnItemInfo.getDeliveryDate(), "yyyy-MM-dd", timeZone) : null);

                dataMap.put("label",  (i + 1) + "/" + boxList.size());

                // 生成QR码数据
                String qrCodeData = generateLabel(tblReturn.getReturnNo(), box);
                dataMap.put("qrCodeData", qrCodeData);

                allDataList.add(dataMap);
            }
        }

        result.put("header", headerMap);
        result.put("data", allDataList);
        return result;
    }

    /**
     * 获取ASN Item信息
     *
     * @param box 退货箱信息
     * @param asnCache ASN缓存
     * @param asnItemCache ASN Item缓存
     * @return ASN Item信息
     */
    private AsnItemInfo getAsnItemInfo(TblReturnBox box, Map<String, TblAsn> asnCache, Map<String, AsnItemInfo> asnItemCache) {
//        try {
            // 构建缓存key
            String cacheKey = box.getAsnCode() + "+" + box.getOrderCode() + "+" + box.getItemNo() + "+" + box.getReleaseNo();

            // 先从缓存中获取
            if (asnItemCache.containsKey(cacheKey)) {
                return asnItemCache.get(cacheKey);
            }

            // 获取ASN信息（已包含行项目和物料信息）
            TblAsn asn = asnCache.get(box.getAsnCode());
            if (asn == null) {
                asn = tblAsnService.selectTblAsnByAsnCode(box.getAsnCode());
                if (asn != null) {
                    asnCache.put(box.getAsnCode(), asn);
                }
            }

            if (asn == null) {
                logger.warn("未找到ASN信息，ASN编号：{}", box.getAsnCode());
                return null;
            }

            // 查找匹配的行项目和物料
            // box.asnCode 对应 tblAsn.asnCode
            // box.orderCode 对应 tblAsnItem.orderCode
            // box.itemNo 对应 tblAsnArticle.orderLineNo
            // box.releaseNo 对应 tblAsnArticle.deliveryScheduleNo
            if (asn.getDetail() != null) {
                for (TblAsnItem item : asn.getDetail()) {
                    // 匹配订单编号
                    if (box.getOrderCode().equals(item.getOrderCode())) {

                        // 查找匹配的物料
                        if (item.getArticles() != null) {
                            for (TblAsnArticle article : item.getArticles()) {
                                // 匹配行号和发布号
                                if (box.getItemNo().equals(article.getOrderLineNo()) &&
                                    box.getReleaseNo().equals(article.getDeliveryScheduleNo())) {

                                    // 找到匹配的物料信息
                                    AsnItemInfo asnItemInfo = new AsnItemInfo();
                                    asnItemInfo.setArticleNo(article.getArticleNo());
                                    asnItemInfo.setArticleName(article.getArticleName());
                                    asnItemInfo.setQuantity(article.getQuantity());
                                    asnItemInfo.setUnit(article.getUnit());
                                    asnItemInfo.setQtyPerPack(article.getQtyPerPack());
                                    asnItemInfo.setPackQty(article.getPackQty());
                                    asnItemInfo.setPlantCode(item.getPlantCode());
                                    asnItemInfo.setPlantName(item.getPlantName());
                                    asnItemInfo.setSuppName(asn.getSuppName());
                                    asnItemInfo.setDocNo(asn.getDocNo());
                                    asnItemInfo.setDnNo(item.getDnNo());
                                    asnItemInfo.setDeliveryDate(asn.getDeliveryDate());

                                    // 放入缓存
                                    asnItemCache.put(cacheKey, asnItemInfo);
                                    return asnItemInfo;
                                }
                            }
                        }
                    }
                }
            }

            logger.warn("未找到匹配的ASN物料信息，ASN编号：{}，订单：{}，行号：{}，发布号：{}",
                box.getAsnCode(), box.getOrderCode(), box.getItemNo(), box.getReleaseNo());
            return null;

//        } catch (Exception e) {
//            logger.error("获取ASN信息失败", e);
//            return null;
//        }
    }

    /**
     * ASN Item信息内部类
     */
    @Setter
    @Getter
    private static class AsnItemInfo {
        // Getters and Setters
        private String articleNo;
        private String articleName;
        private java.math.BigDecimal quantity;
        private String unit;
        private java.math.BigDecimal qtyPerPack;
        private java.math.BigDecimal packQty;
        private String plantCode;
        private String plantName;
        private String suppName;
        private String docNo;
        private String dnNo;
        private Date deliveryDate;

    }
}
