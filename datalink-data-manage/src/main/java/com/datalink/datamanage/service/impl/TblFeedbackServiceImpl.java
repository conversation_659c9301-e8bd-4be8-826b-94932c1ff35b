package com.datalink.datamanage.service.impl;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.text.SimpleDateFormat;
import java.util.*;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.SapApiClient;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.TextFileUtil;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.datalink.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.datalink.datamanage.domain.TblFeedbackItem;
import com.datalink.datamanage.mapper.TblFeedbackMapper;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.service.ITblFeedbackService;
import com.datalink.system.service.ISysConfigService;

/**
 * 收货反馈Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-07-06
 */
@Service
@Slf4j
public class TblFeedbackServiceImpl implements ITblFeedbackService 
{
    @Autowired
    private TblFeedbackMapper tblFeedbackMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SapApiClient sapApiClient;

    /**
     * 查询收货反馈
     * 
     * @param feedId 收货反馈ID
     * @return 收货反馈
     */
    @Override
    public TblFeedback selectTblFeedbackById(Long feedId)
    {
        return tblFeedbackMapper.selectTblFeedbackById(feedId);
    }

    /**
     * 查询收货反馈列表
     * 
     * @param tblFeedback 收货反馈
     * @return 收货反馈
     */
    @Override
    public List<TblFeedback> selectTblFeedbackList(TblFeedback tblFeedback)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            tblFeedback.setDepot(depot);
        }
        return tblFeedbackMapper.selectTblFeedbackList(tblFeedback);
    }

    @Override
    public List<TblFeedback> selectTblFeedbackWithItemList(TblFeedback tblFeedback) {
        return tblFeedbackMapper.selectTblFeedbackWithItemList(tblFeedback);
    }

    @Override
    public List<TblFeedback> selectTblFeedbackFullList(TblFeedback tblFeedback) {
        return tblFeedbackMapper.selectTblFeedbackFullList(tblFeedback);
    }

    /**
     * 新增收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblFeedback(TblFeedback tblFeedback)
    {
        tblFeedback.setCreateTime(DateUtils.getNowDate());
        int rows = tblFeedbackMapper.insertTblFeedback(tblFeedback);
        insertTblFeedbackItem(tblFeedback);
        return rows;
    }

    /**
     * 修改收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblFeedback(TblFeedback tblFeedback)
    {
        tblFeedback.setUpdateTime(DateUtils.getNowDate());
        tblFeedbackMapper.deleteTblFeedbackItemByFeedId(tblFeedback.getFeedId());
        insertTblFeedbackItem(tblFeedback);
        return tblFeedbackMapper.updateTblFeedback(tblFeedback);
    }

    /**
     * 批量删除收货反馈
     * 
     * @param feedIds 需要删除的收货反馈ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblFeedbackByIds(Long[] feedIds)
    {
        tblFeedbackMapper.deleteTblFeedbackItemByFeedIds(feedIds);
        return tblFeedbackMapper.deleteTblFeedbackByIds(feedIds);
    }

    /**
     * 删除收货反馈信息
     * 
     * @param feedId 收货反馈ID
     * @return 结果
     */
    @Override
    public int deleteTblFeedbackById(Long feedId)
    {
        tblFeedbackMapper.deleteTblFeedbackItemByFeedId(feedId);
        return tblFeedbackMapper.deleteTblFeedbackById(feedId);
    }

    @Override
    public int updateTblFeedbackOnly(TblFeedback tblFeedback) {
        return tblFeedbackMapper.updateTblFeedback(tblFeedback);
    }

    /**
     * 新增收货反馈行项目信息
     * 
     * @param tblFeedback 收货反馈对象
     */
    public void insertTblFeedbackItem(TblFeedback tblFeedback)
    {
        List<TblFeedbackItem> tblFeedbackItemList = tblFeedback.getDetail();
        Long feedId = tblFeedback.getFeedId();
        if (StringUtils.isNotNull(tblFeedbackItemList))
        {
            List<TblFeedbackItem> list = new ArrayList<TblFeedbackItem>();
            for (TblFeedbackItem tblFeedbackItem : tblFeedbackItemList)
            {
                tblFeedbackItem.setFeedId(feedId);
                list.add(tblFeedbackItem);
            }
            if (list.size() > 0)
            {
                tblFeedbackMapper.batchTblFeedbackItem(list);
            }
        }
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblFeedbackMapper.selectLastId();
    }

    /**
     * 查询收货反馈行项目列表
     *
     * @param feedbackItem 收货反馈行项目
     * @return 收货行项目反馈集合
     */
    @Override
    public List<TblFeedbackItem> selectTblFeedbackItemList(TblFeedbackItem feedbackItem) {
        return tblFeedbackMapper.selectTblFeedbackItemList(feedbackItem);
    }

    /**
     * 查询收货反馈(不包含行项目)
     *
     * @param feedId 收货反馈ID
     * @return 收货反馈
     */
    @Override
    public TblFeedback selectTblFeedbackOnlyById(Long feedId) {
        TblFeedback param = new TblFeedback();
        param.setFeedId(feedId);
        return tblFeedbackMapper.selectTblFeedbackOnlyById(param);
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadFeedbackTxt(List<Long> feedbackIds, String tz) {
        try {
//            List<TblFeedback> feedbackList = this.selectTblFeedbackWithItemList(feedbackIds);
//            if (feedbackList == null || feedbackList.isEmpty()) {
//                return AjaxResult.error("未找到收货反馈数据");
//            }

            String fileName = String.format("HM04_%s.txt", UUID.randomUUID());
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), "Shift-JIS"))) {
                
                for (Long feedbackId : feedbackIds) {
                    TblFeedback feedback = this.selectTblFeedbackById(feedbackId);
                    if (CollectionUtils.isEmpty(feedback.getDetail())) {
                        continue;
                    }

                    for (TblFeedbackItem item : feedback.getDetail()) {
                        // 创建一个188字符长的字符数组，初始化为空格
                        char[] record = new char[188];
                        Arrays.fill(record, ' ');

                        // 按照规范填充数据
                        TextFileUtil.fillField(record, "30", 1, 2, "F/#");                    // F/# (1-2)
                        TextFileUtil.fillField(record, "", 3, 6, "発注会社");              // 発注会社 (3-6)
                        TextFileUtil.fillField(record, feedback.getSuppCode(), 7, 11, "取引先"); // 取引先 (7-11)
                        TextFileUtil.fillField(record, feedback.getDepot(), 12, 13, "デポ");   // デポ (12-13)
                        TextFileUtil.fillField(record, feedback.getPlantCode(), 14, 17, "要求工場"); // 要求工場 (14-17)
                        TextFileUtil.fillField(record, item.getOrderCode(), 18, 27, "発行№"); // 発行№ (18-27)
                        String productCategory = SapObjectConverter.handleProductClass(dictDataService, item.getArticleNo());
                        if (StringUtils.isNotEmpty(productCategory)) {
                            item.setArticleNo(item.getArticleNo().substring(0, item.getArticleNo().indexOf("+")));
                        }
                        TextFileUtil.fillField(record, item.getArticleNo(), 28, 52, "部品番号"); // 部品番号 (28-52)
                        TextFileUtil.fillField(record, productCategory, 53, 53, "製品区分");                // 製品区分 (53)
                        TextFileUtil.fillField(record, feedback.getReceivingPlace(), 54, 60, "納入場所"); // 納入場所 (54-60)
                        
                        // 納入指示情報
                        TextFileUtil.fillField(record, feedback.getReceivingDate() == null ? null : DateUtils.parseDateToStrWithTz("yyyyMMdd", feedback.getReceivingDate(), tz), 61, 68, "納入指示年月日"); // 納入指示年月日 (61-68)
                        TextFileUtil.fillField(record, null, 69, 72, "納入指示時分"); // 納入指示時分 (69-72)
                        TextFileUtil.fillNumber(record, feedback.getReceivingQuantity(), 73, 80, 0);    // 納入指示数量 (73-80)
                        TextFileUtil.fillField(record, feedback.getOrderUnit(), 81, 83, "発注単位"); // 発注単位 (81-83)
                        TextFileUtil.fillField(record, "", 84, 84, "条件単位"); // 条件単位 (84)
                        
                        // 出荷実績情報
                        TextFileUtil.fillField(record, "", 85, 92, "出荷実績年月日"); // 出荷実績年月日 (85-92)
                        TextFileUtil.fillField(record, "", 93, 96, "出荷実績時分"); // 出荷実績時分 (93-96)
                        TextFileUtil.fillNumber(record, null, 97, 104, 0);   // 出荷実績数量 (97-104)
                        TextFileUtil.fillNumber(record, null, 105, 115, 3); // 出荷換算重量 (105-115,3)
                        
                        // 受入実績情報
                        TextFileUtil.fillField(record, item.getRcvDate() == null ? null : DateUtils.parseDateToStrWithTz("yyyyMMdd", item.getRcvDate(), tz), 116, 123, "受入実績年月日"); // 受入実績年月日 (116-123)
                        TextFileUtil.fillField(record, "", 124, 127, "受入実績時分"); // 受入実績時分 (124-127)
                        TextFileUtil.fillNumber(record, item.getQuantity(), 128, 135, 0);   // 受入実績数量 (128-135)
                        TextFileUtil.fillNumber(record, null, 136, 146, 3); // 受入換算重量 (136-146,3)
                        
                        // 納品書情報
                        TextFileUtil.fillField(record, feedback.getDnNo(), 147, 161, "納品書№"); // 納品書№ (147-161)
                        TextFileUtil.fillField(record, feedback.getDeliveryNoteDate() == null ? null : DateUtils.parseDateToStrWithTz("yyyyMMdd", feedback.getDeliveryNoteDate(), tz), 162, 169, "納品書発行年月日"); // 納品書発行年月日 (162-169)
                        TextFileUtil.fillField(record, feedback.getDeliveryNoteTime() == null ? null : DateUtils.parseDateToStrWithTz("HHmm", feedback.getDeliveryNoteTime(), tz), 170, 173, "納品書発行時分"); // 納品書発行時分 (170-173)
                        
                        // 作成情報
                        TextFileUtil.fillField(record, DateUtils.parseDateToStrWithTz("yyyyMMdd", feedback.getCreateTime(), tz), 174, 181, "作成年月日"); // 作成年月日 (174-181)
                        
                        // 配付先情報
                        TextFileUtil.fillField(record, "", 182, 186, "配付先_取引先"); // 配付先_取引先 (182-186)
                        TextFileUtil.fillField(record, "", 187, 188, "配付先_デポ"); // 配付先_デポ (187-188)

                        writer.write(record);
                        writer.write("\r\n");
                    }
                }
            }

            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("生成收货反馈txt文件失败", e);
            return AjaxResult.error("生成收货反馈txt文件失败：" + e.getMessage());
        }
    }

    @Override
    public void updateOrderStatus(TblFeedback feedback) {
        if (feedback == null || feedback.getDetail() == null || feedback.getDetail().isEmpty()) {
            return;
        }

        // 如果dnNo不是以ASN开头(买卖两件业务),则需要更新订单状态
        if (!feedback.getDnNo().startsWith("ASN")) {
            for (TblFeedbackItem item : feedback.getDetail()) {
                // 更新订单物料剩余数量
                tblFeedbackMapper.updateOrderAsnQuantity(item);
                // 检查并更新订单完成状态
                tblFeedbackMapper.updateOrderComplete(item.getOrderCode());
            }
        }
    }

    /**
     * 确认结算单
     *
     * @param feedIds 结算单ID列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult confirmFeedback(List<Long> feedIds) {
        if (CollectionUtils.isEmpty(feedIds)) {
            return AjaxResult.error("结算单ID列表不能为空");
        }

        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> errorList = new ArrayList<>();

        // 获取SAP接口URL
        String sapUrl = configService.selectConfigByKey(DataConstants.SAP_FEEDBACK_CONFIRM_URL);
        if (StringUtils.isEmpty(sapUrl)) {
            return AjaxResult.error("SAP结算单确认接口URL未配置");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        for (Long feedId : feedIds) {
            try {
                // 查询结算单信息
                TblFeedback feedback = selectTblFeedbackById(feedId);
                if (feedback == null) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "结算单不存在");
                    errorList.add(errorMap);
                    continue;
                }

                // 校验状态
                if (!DataConstants.FEEDBACK_STATUS_NEW.equals(feedback.getStatus())) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "只有New状态的结算单才能确认");
                    errorList.add(errorMap);
                    continue;
                }

                // 校验必填字段
                if (feedback.getInvoiceTax() == null) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "增值税总额不能为空");
                    errorList.add(errorMap);
                    continue;
                }

                if (StringUtils.isEmpty(feedback.getInvoiceNo())) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "金税发票号不能为空");
                    errorList.add(errorMap);
                    continue;
                }

                if (feedback.getInvoiceDate() == null) {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "开票日期不能为空");
                    errorList.add(errorMap);
                    continue;
                }

                // 构建SAP请求参数
                Map<String, Object> requestMap = new HashMap<>();
                requestMap.put("ZSETT", feedback.getDnNo());
                requestMap.put("BUKRS", feedback.getCompCode());
                requestMap.put("LIFNR", feedback.getSuppCode());
                requestMap.put("BLDAT_VAT", sdf.format(feedback.getInvoiceDate()));
                requestMap.put("DMBTR_VAT", feedback.getTotalAmount());
                requestMap.put("WMWST_VAT", feedback.getInvoiceTax());
                requestMap.put("WAERS", feedback.getCurrency());
                requestMap.put("XBLNR", feedback.getInvoiceNo());

                // 调用SAP接口
                HashMap<String, Object> sapResult = sapApiClient.callSapApi(sapUrl, requestMap);

                // 处理SAP返回结果
                if (sapResult != null) {
                    String msgType = (String) sapResult.get("MSTYP");
                    String message = (String) sapResult.get("MSG");

                    if ("E".equals(msgType)) {
                        // 错误消息
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("feedId", feedId);
                        errorMap.put("message", "SAP接口返回: " + message);
                        errorList.add(errorMap);
                    } else {
                        // 确认成功，更新状态
                        feedback.setStatus(DataConstants.FEEDBACK_STATUS_CONFIRMED);
                        feedback.setConfirmTime(new Date());
                        feedback.setConfirmBy(SecurityUtils.getUsername());
                        updateTblFeedbackOnly(feedback);

                        Map<String, Object> successMap = new HashMap<>();
                        successMap.put("feedId", feedId);
                        successMap.put("message", "确认成功");
                        successList.add(successMap);
                    }
                } else {
                    Map<String, Object> errorMap = new HashMap<>();
                    errorMap.put("feedId", feedId);
                    errorMap.put("message", "调用SAP接口失败");
                    errorList.add(errorMap);
                }
            } catch (Exception e) {
                log.error("确认结算单异常，feedId: {}, 错误信息: {}", feedId, e.getMessage(), e);
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("feedId", feedId);
                errorMap.put("message", "处理异常: " + e.getMessage());
                errorList.add(errorMap);
            }
        }

        // 返回处理结果
        Map<String, Object> result = new HashMap<>();
        result.put("total", feedIds.size());
        result.put("success", successList.size());
        result.put("fail", errorList.size());
        result.put("successList", successList);
        result.put("errorList", errorList);

        return AjaxResult.success(result);
    }
}
