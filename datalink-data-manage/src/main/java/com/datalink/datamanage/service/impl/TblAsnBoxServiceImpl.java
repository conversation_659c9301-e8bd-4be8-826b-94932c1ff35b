package com.datalink.datamanage.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import com.datalink.datamanage.service.ITblPalletService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.datalink.datamanage.mapper.TblAsnBoxMapper;
import com.datalink.datamanage.domain.TblAsnBox;
import com.datalink.datamanage.service.ITblAsnBoxService;

/**
 * ASN箱信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
@Service
public class TblAsnBoxServiceImpl implements ITblAsnBoxService 
{
    @Autowired
    private TblAsnBoxMapper tblAsnBoxMapper;

    @Autowired
    private ITblPalletService tblPalletService;

    /**
     * 查询ASN箱信息
     * 
     * @param boxId ASN箱信息ID
     * @return ASN箱信息
     */
    @Override
    public TblAsnBox selectTblAsnBoxById(Long boxId)
    {
        return tblAsnBoxMapper.selectTblAsnBoxById(boxId);
    }

    /**
     * 查询ASN箱信息列表
     * 
     * @param tblAsnBox ASN箱信息
     * @return ASN箱信息
     */
    @Override
    public List<TblAsnBox> selectTblAsnBoxList(TblAsnBox tblAsnBox)
    {
        return tblAsnBoxMapper.selectTblAsnBoxList(tblAsnBox);
    }

    /**
     * 根据物料ID查询ASN箱信息列表
     * 
     * @param articleId ASN物料ID
     * @return ASN箱信息集合
     */
    @Override
    public List<TblAsnBox> selectTblAsnBoxByArticleId(Long articleId)
    {
        return tblAsnBoxMapper.selectTblAsnBoxByArticleId(articleId);
    }

    /**
     * 根据ASN ID查询ASN箱信息列表
     * 
     * @param asnId ASN ID
     * @return ASN箱信息集合
     */
    @Override
    public List<TblAsnBox> selectTblAsnBoxByAsnId(Long asnId)
    {
        return tblAsnBoxMapper.selectTblAsnBoxByAsnId(asnId);
    }

    /**
     * 新增ASN箱信息
     * 
     * @param tblAsnBox ASN箱信息
     * @return 结果
     */
    @Override
    public int insertTblAsnBox(TblAsnBox tblAsnBox)
    {
        tblAsnBox.setCreateTime(DateUtils.getNowDate());
        tblAsnBox.setCreateBy(SecurityUtils.getUsername());
        return tblAsnBoxMapper.insertTblAsnBox(tblAsnBox);
    }

    /**
     * 批量新增ASN箱信息
     * 
     * @param tblAsnBoxList ASN箱信息列表
     * @return 结果
     */
    @Override
    public int batchInsertTblAsnBox(List<TblAsnBox> tblAsnBoxList)
    {
        if (tblAsnBoxList == null || tblAsnBoxList.isEmpty()) {
            return 0;
        }
        
        String currentUser = SecurityUtils.getUsername();
        for (TblAsnBox box : tblAsnBoxList) {
            box.setCreateTime(DateUtils.getNowDate());
            box.setCreateBy(currentUser);
        }
        
        return tblAsnBoxMapper.batchInsertTblAsnBox(tblAsnBoxList);
    }

    /**
     * 修改ASN箱信息
     * 
     * @param tblAsnBox ASN箱信息
     * @return 结果
     */
    @Override
    public int updateTblAsnBox(TblAsnBox tblAsnBox)
    {
        tblAsnBox.setUpdateTime(DateUtils.getNowDate());
        tblAsnBox.setUpdateBy(SecurityUtils.getUsername());
        return tblAsnBoxMapper.updateTblAsnBox(tblAsnBox);
    }

    /**
     * 批量删除ASN箱信息
     * 
     * @param boxIds 需要删除的ASN箱信息ID
     * @return 结果
     */
    @Override
    public int deleteTblAsnBoxByIds(Long[] boxIds)
    {
        return tblAsnBoxMapper.deleteTblAsnBoxByIds(boxIds);
    }

    /**
     * 删除ASN箱信息信息
     * 
     * @param boxId ASN箱信息ID
     * @return 结果
     */
    @Override
    public int deleteTblAsnBoxById(Long boxId)
    {
        return tblAsnBoxMapper.deleteTblAsnBoxById(boxId);
    }

    /**
     * 根据物料ID删除ASN箱信息
     * 
     * @param articleId ASN物料ID
     * @return 结果
     */
    @Override
    public int deleteTblAsnBoxByArticleId(Long articleId)
    {
        return tblAsnBoxMapper.deleteTblAsnBoxByArticleId(articleId);
    }

    /**
     * 根据ASN ID删除ASN箱信息
     * 
     * @param asnId ASN ID
     * @return 结果
     */
    @Override
    public int deleteTblAsnBoxByAsnId(Long asnId)
    {
        return tblAsnBoxMapper.deleteTblAsnBoxByAsnId(asnId);
    }

    /**
     * 为ASN物料生成箱信息
     *
     * @param article    ASN物料ID
     * @param plantCode  工厂编码
     * @return 结果
     */
    @Override
    public int generateBoxesForArticle(TblAsnArticle article, String plantCode)
    {
        List<TblAsnBox> boxList = new ArrayList<>();

        PalletInfoVO palletInfoVO = new PalletInfoVO();
        palletInfoVO.setPlantCode(plantCode);
        palletInfoVO.setMaterialCode(article.getArticleNo());
        List<PalletInfoVO> palletInfoVOS = tblPalletService.selectTblPalletList(palletInfoVO);
        Integer palletSnp = null;
        if (!palletInfoVOS.isEmpty()) {
            // 有托盘配置则这个article有多个托盘，每个托盘label唯一，box.qty根据托盘数量/snp计算
            if (palletInfoVOS.size() > 1) {
                throw new RuntimeException("物料" + article.getArticleNo() +"配置了多个托盘，请检查物料配置");
            }
            PalletInfoVO palletInfo = palletInfoVOS.get(0);
            palletSnp = palletInfo.getSnpQuantity();
        }
        for (int i = 1; i <= article.getBoxes().size(); i++) {
            TblAsnBox box = article.getBoxes().get(i - 1);
            box.setArticleId(article.getArticleId());
            if (palletSnp == null) {
                box.setPalletQty(null);
                // 不覆盖已有托号
            } else {
                box.setPalletQty(BigDecimal.valueOf(palletSnp));
                // 仅当未预分配托号时，按当前物料内规则赋值
                if (box.getPalletNo() == null || box.getPalletNo().isEmpty()) {
                    int palletNo = (i - 1) / palletSnp + 1;
                    box.setPalletNo(String.valueOf(palletNo));
                }
            }

            boxList.add(box);
        }
        
        return batchInsertTblAsnBox(boxList);
    }
}
