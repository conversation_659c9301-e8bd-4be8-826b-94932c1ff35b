package com.datalink.datamanage.service.impl;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.common.DataConstants;
import com.datalink.common.Util;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.*;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.domain.vo.PalletInfoVO;
import com.datalink.datamanage.mapper.TblAsnMapper;
import com.datalink.datamanage.service.ITblAsnService;
import com.datalink.datamanage.service.ITblAsnBoxService;
import com.datalink.datamanage.service.ITblMaterialService;
import com.datalink.datamanage.service.ITblOrderService;
import com.datalink.datamanage.service.ITblPalletService;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.system.domain.SysConfig;
import com.datalink.system.service.ISysConfigService;
import com.datalink.system.service.ISysDictDataService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ASNService业务层处理
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
@Service
public class TblAsnServiceImpl implements ITblAsnService
{
    private static final Logger log = LoggerFactory.getLogger(TblAsnServiceImpl.class);
    @Autowired
    private TblAsnMapper tblAsnMapper;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ITblOrderService orderService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private SapApiClient sapApiClient;

    @Autowired
    private ITblMaterialService tblMaterialService;

    @Autowired
    private ITblPalletService tblPalletService;

    @Autowired
    private ITblAsnBoxService tblAsnBoxService;

    @Value("${file.jasper-template-dir}")
    private String templatePath;

    /**
     * 查询ASN
     *
     * @param asnId ASNID
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnById(Long asnId)
    {
        TblAsn param = new TblAsn();
        param.setAsnId(asnId);
        return tblAsnMapper.selectTblAsnById(param);
    }

    /**
     * 查询ASN
     *
     * @param asnCode ASNCode
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnByAsnCode(String asnCode)
    {
        TblAsn param = new TblAsn();
        param.setAsnCode(asnCode);
        TblAsn asn = tblAsnMapper.selectTblAsnByAsnCode(param);
        if(asn == null) {
            throw new RuntimeException("ASN不存在，ASNCode: " + asnCode);
        }
        for (TblAsnItem item : asn.getDetail()) {
            for (TblAsnArticle article : item.getArticles()) {
                List<TblAsnArticle.Pallet> pallets = new ArrayList<>();
                // box按托号分组
                Map<String, List<TblAsnBox>> group = article.getBoxes().stream().collect(Collectors.groupingBy(TblAsnBox::getPalletNo));
                for (Map.Entry<String, List<TblAsnBox>> entry : group.entrySet()) {
                    // 循环拼接箱标签和托标签
                    List<TblAsnBox> boxes = entry.getValue();
                    // box.quantity求和
                    BigDecimal palletQty = boxes.stream().map(TblAsnBox::getQuantity).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    String palletNo = entry.getKey();
                    TblAsnArticle.Pallet pallet = new TblAsnArticle.Pallet();
                    if (StringUtils.isNotEmpty(palletNo)) {
//                        pallet.setLabel("ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
//                                + "%RELEASE@" + article.getDeliveryScheduleNo() + "%PALLET@" + palletNo + "%PQTY@" + palletQty + "%");
                        pallet.setLabel("ASN@" + asn.getAsnCode() + "%PALLET@" + palletNo + "%PQTY@" + palletQty + "%");
                    } else {
                        pallet.setLabel("");
                    }
                    List<TblAsnArticle.Box> boxList = new ArrayList<>();
                    for (TblAsnBox b : boxes) {
                        TblAsnArticle.Box box = new TblAsnArticle.Box();
                        box.setQty(b.getQuantity());
                        box.setBatchNo(b.getBatchNo());
                        box.setLabel("ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
                                + "%RELEASE@" + article.getDeliveryScheduleNo() + "%BATCH@" + box.getBatchNo() + "%PALLET@" + palletNo + "%BOX@" + b.getBoxNo() + "%BQTY@" + box.getQty() + "%");
                        boxList.add(box);
                    }
                    pallet.setBox(boxList);
                    pallets.add(pallet);
                }
                article.setPallet(pallets);
            }
        }
        return asn;
    }

    /**
     * 查询ASN列表
     *
     * @param tblAsn ASN
     * @return ASN
     */
    @Override
    public List<TblAsn> selectTblAsnList(TblAsn tblAsn)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的岗位编码
            String userName = loginUser.getUser().getUserName();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            tblAsn.setCreateBy(userName);
        }
        // 试制工厂（223X）员工只能看到试制工厂相关的asn
        boolean is223X = false;
        if (permissionService.hasRole("223Xuser") && !loginUser.getUser().isAdmin()) {
            is223X = true;
        }
        return tblAsnMapper.selectTblAsnList(tblAsn, is223X);
    }

    /**
     * 查询ASN及行项目列表
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    @Override
    public List<TblAsn> selectTblAsnWithItemList(TblAsn tblAsn) {
        return tblAsnMapper.selectTblAsnWithItemList(tblAsn);
    }

    /**
     * 新增ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblAsn(TblAsn tblAsn)
    {
        if (null == tblAsn.getAsnCode()){
            genAsnCode(tblAsn);
//            if (tblAsn.getDirection().equals(DataConstants.DIRECTION_OUT) && tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_TO_SEND)){
//                genBarCode(tblAsn);
//            }
        }
        tblAsn.setCreateTime(DateUtils.getNowDate());
        for (TblAsnItem item : tblAsn.getDetail()) {
            // 校验订单状态
            Map<String, Boolean> orderStatus = orderService.checkOrderStatus(item.getOrderCode());
            // 校验订单已完成时不允许新增
            if (orderStatus.get("isComplete")) {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("order.is.complete"), item.getOrderCode()));
            }
            // 校验只有确认后的订单才能新增ASN
            if (!orderStatus.get("isConfirmed")) {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("order.is.not.confirm"), item.getOrderCode()));
            }
        }
        int rows = tblAsnMapper.insertTblAsn(tblAsn);
        insertTblAsnItem(tblAsn);
        updateOrderCompleteStatus(tblAsn);
        if (!tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            sendToSap(tblAsn);
        }

        return rows;
    }

    private void updateOrderCompleteStatus(TblAsn tblAsn) {
        for (TblAsnItem item : tblAsn.getDetail()) {
            orderService.updateOrderCompleteStatus(item.getOrderCode());
        }
    }

    /**
     * 修改ASN
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblAsn(TblAsn tblAsn)
    {
//        if (tblAsn.getDirection().equals(DataConstants.DIRECTION_OUT) && tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_TO_SEND)){
//            genBarCode(tblAsn);
//        }
        if(tblAsn.getAsnId() == null){
            throw new RuntimeException("asnId不能为空");
        }
        TblAsn asn = tblAsnMapper.selectTblAsnById(tblAsn);
        if (asn == null){
            // 防止将已删除的ASN送到SAP
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.not.exists"), tblAsn.getAsnCode()));
        }
        // 判断是否已发送，防止重复提交
        if (DataConstants.KAFKA_STATUS_TO_SEND.equals(asn.getKafkaStatus())) {
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.is.sent"), tblAsn.getAsnCode()));
        }
        tblAsn.setUpdateTime(DateUtils.getNowDate());
        tblAsnMapper.reduceOrderArticleQuantityByAsnId(tblAsn.getAsnId());
        // 删除箱信息
        tblAsnBoxService.deleteTblAsnBoxByAsnId(tblAsn.getAsnId());
        tblAsnMapper.deleteTblAsnArticleByAsnId(tblAsn.getAsnId());
        tblAsnMapper.deleteTblAsnItemByAsnId(tblAsn.getAsnId());
        insertTblAsnItem(tblAsn);
        updateOrderCompleteStatus(tblAsn);
        if (!tblAsn.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            sendToSap(tblAsn);
        }
        return tblAsnMapper.updateTblAsn(tblAsn);
    }

    /**
     * 修改ASN(不包含行项目)
     *
     * @param tblAsn ASN
     * @return 结果
     */
    @Override
    public int updateTblAsnOnly(TblAsn tblAsn)
    {
        tblAsn.setUpdateTime(DateUtils.getNowDate());
        return tblAsnMapper.updateTblAsn(tblAsn);
    }

    /**
     * 批量删除ASN
     *
     * @param asnIds 需要删除的ASNID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblAsnByIds(Long[] asnIds)
    {
        for (Long asnId : asnIds){
            TblAsn asn = tblAsnMapper.selectTblAsnOnlyById(asnId);
            if (asn != null && DataConstants.KAFKA_STATUS_TO_SEND.equals(asn.getKafkaStatus())) {
                throw new RuntimeException(asn.getAsnCode() + "已发送，不允许删除");
            }
            tblAsnMapper.reduceOrderArticleQuantityByAsnId(asnId);
            List<TblAsnItem> itemList = tblAsnMapper.selectTblAsnItemByAsnId(asnId);
            // 若unsent_quantity大于0，则将order.is_complete设为N
            for (TblAsnItem item : itemList){
                orderService.updateOrderCompleteStatus(item.getOrderCode());
            }
            // 删除箱信息
            tblAsnBoxService.deleteTblAsnBoxByAsnId(asnId);
            tblAsnMapper.deleteTblAsnArticleByAsnId(asnId);
        }

        tblAsnMapper.deleteTblAsnItemByAsnIds(asnIds);
        return tblAsnMapper.deleteTblAsnByIds(asnIds);
    }

    /**
     * 删除ASN信息
     *
     * @param asnId ASNID
     * @return 结果
     */
    @Override
    public int deleteTblAsnById(Long asnId)
    {
        tblAsnMapper.deleteTblAsnItemByAsnId(asnId);
        return tblAsnMapper.deleteTblAsnById(asnId);
    }

    /**
     * 查询ASN列表(接口专用)
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    @Override
    public List<TblAsn> selectTblAsnFullList(TblAsn tblAsn) {
        return tblAsnMapper.selectTblAsnFullList(tblAsn);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId() {
        return tblAsnMapper.selectLastId();
    }

    /**
     * 查询ASN(不包含行项目)
     *
     * @param asnId ASNID
     * @return ASN
     */
    @Override
    public TblAsn selectTblAsnOnlyById(Long asnId) {
        return tblAsnMapper.selectTblAsnOnlyById(asnId);
    }

    /**
     * 查询ASN行项目列表
     *
     * @param asnId ASNID
     * @return ASN行项目集合
     */
    @Override
    public List<TblAsnItem> selectTblAsnItemByAsnId(Long asnId) {
        return tblAsnMapper.selectTblAsnItemByAsnId(asnId);
    }

    /**
     * 查询ASN行项目
     *
     * @param itemId ItemID
     * @return ASN行项目
     */
    @Override
    public TblAsnItem selectTblAsnItemByItemId(Long itemId) {
        return tblAsnMapper.selectTblAsnItemByItemId(itemId);
    }

    /**
     * 查询ASN物料列表
     *
     * @param itemId ItemID
     * @return ASN物料列表
     */
    @Override
    public List<TblAsnArticle> selectTblAsnArticleByItemId(Long itemId) {
        return tblAsnMapper.selectTblAsnArticleByItemId(itemId);
    }

    /**
     * 查询ASN行项目列表
     *
     * @param tblAsnItem TblAsnItem
     * @return ASN行项目集合
     */
    @Override
    public List<TblAsnItem> selectTblAsnItemList(TblAsnItem tblAsnItem) {
        return tblAsnMapper.selectTblAsnItemList(tblAsnItem);
    }

    /**
     * 查询ASN物料列表
     *
     * @param tblAsnArticle TblAsnArticle
     * @return ASN物料列表
     */
    @Override
    public List<TblAsnArticle> selectTblAsnArticleList(TblAsnArticle tblAsnArticle) {
        return tblAsnMapper.selectTblAsnArticleList(tblAsnArticle);
    }

    /**
     * 查询订单物料剩余
     *
     * @param orderAsnQuantity TblOrderAsnQuantity
     * @return 订单物料剩余集合
     */
    @Override
    public List<TblOrderAsnQuantity> selectTblOrderAsnQuantityList(TblOrderAsnQuantity orderAsnQuantity) {
        return tblAsnMapper.selectTblOrderAsnQuantityList(orderAsnQuantity);
    }

    @Override
    public AjaxResult printNpsSls(Long asnId, String tz) {
        String jasperPath = templatePath + "npsjsls.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        if (result == null){
            return AjaxResult.error("ASN不存在");
        }
        if (result.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            return AjaxResult.error("ASN未发送，不允许打印");
        }
        Map<String, Object> objectMap = genPrintDataForNpsSls(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public AjaxResult printProdTag(Long asnId, String tz) {
        String jasperPath = templatePath + "xpp.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        if (result == null){
            return AjaxResult.error("ASN不存在");
        }
        if (result.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            return AjaxResult.error("ASN未发送，不允许打印");
        }
        Map<String, Object> objectMap = genPrintDataForProdTag(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public AjaxResult printPalletTag(Long asnId, String tz) {
        String jasperPath = templatePath + "pallet.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        if (result == null){
            return AjaxResult.error("ASN不存在");
        }
        if (result.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            return AjaxResult.error("ASN未发送，不允许打印");
        }

        Map<String, Object> objectMap = genPrintDataForPalletTag(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public AjaxResult printPickingList(long asnId, String tz) {
        String jasperPath = templatePath + "pick_list.jrxml";
        TblAsn result = this.selectTblAsnForPrint(asnId);
        if (result == null){
            return AjaxResult.error("ASN不存在");
        }
        if (result.getKafkaStatus().equals(DataConstants.KAFKA_STATUS_NO_SENT)){
            return AjaxResult.error("ASN未发送，不允许打印");
        }
        Map<String, Object> objectMap = genPrintDataForPickingList(result, tz);
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), (Map) objectMap.get("header"), (List<?>) objectMap.get("data"));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public TblAsn selectTblAsnForPrint(Long asnId) {
        TblAsn asn = new TblAsn();
        asn.setAsnId(asnId);
        return tblAsnMapper.selectTblAsnForPrint(asn);
    }

    /**
     * 新增ASN行项目信息
     *
     * @param tblAsn ASN对象
     */
    public void insertTblAsnItem(TblAsn tblAsn)
    {
        // 根据订单号排序
        List<TblAsnItem> tblAsnItemList = tblAsn.getDetail();
        tblAsnItemList.sort(Comparator.comparing(TblAsnItem::getOrderCode));
        Long asnId = tblAsn.getAsnId();
        if (StringUtils.isNotNull(tblAsnItemList))
        {
            List<TblAsnItem> list = new ArrayList<TblAsnItem>();
            for (TblAsnItem tblAsnItem : tblAsnItemList)
            {
                tblAsnItem.setAsnId(asnId);
                list.add(tblAsnItem);
            }
            if (!list.isEmpty())
            {
                tblAsnMapper.batchTblAsnItem(list);

                // 先批量插入各订单的物料行，并建立 物料号->物料列表 分组 以及 物料实例->父Item 映射
                Map<String, List<TblAsnArticle>> materialToArticles = new LinkedHashMap<>();
                Map<TblAsnArticle, TblAsnItem> articleToItem = new HashMap<>();

                for (TblAsnItem item : tblAsnItemList){
                    List<TblAsnArticle> articles = new ArrayList<>();
                    for (TblAsnArticle article: item.getArticles()){
                        article.setItemId(item.getItemId());
                        // 校验物料是否存在
                        TblMaterial tblMaterial = tblMaterialService.selectTblMaterialByCodeAndPlant(article.getArticleNo(), item.getPlantCode());
                        if(tblMaterial == null || tblMaterial.getPlants().isEmpty()){
                            throw new RuntimeException(StringUtils.format(MessageUtils.message("material.config.not.exists"), item.getPlantCode(), article.getArticleNo()));
                        }
                        articles.add(article);
                        // 建立分组与映射
                        materialToArticles.computeIfAbsent(article.getArticleNo(), k -> new ArrayList<>()).add(article);
                        articleToItem.put(article, item);
                    }
                    if(!articles.isEmpty()){
                        tblAsnMapper.batchTblAsnArticle(articles);
                    }
                }

                // 全局托号计数器：确保不同物料间托号连续（并按物料分组顺序依次分配）
                AtomicInteger globalAssignedPalletCount = new AtomicInteger(0);

                // 按物料分组分配托号并生成箱记录（完成一个物料后再处理下一个物料）
                createBoxesForArticlesGroupedByMaterial(materialToArticles, articleToItem, globalAssignedPalletCount);
            }

            tblAsnMapper.addOrderArticleQuantityByAsnId(tblAsn.getAsnId());
        }
    }

    private void genAsnCode(TblAsn tblAsn){
//        String asnCode = "ASN"+tblAsn.getCompCode()+tblAsn.getSuppCode();
        String asnCode = "ASN"+tblAsn.getSuppCode();
        String configKey = DataConstants.ASN_CODE_PARAM+tblAsn.getSuppCode();
        String currentCode = configService.selectConfigByKeyWithoutRedis(configKey);
        boolean isExist = true;
        if (StringUtils.isEmpty(currentCode)){
            currentCode = "0";
            isExist = false;
        }
        while(currentCode.length() < 5){
            currentCode = "0"+currentCode;
        }
        asnCode = asnCode+currentCode;
        SysConfig update = new SysConfig();
        update.setConfigKey(configKey);
        update.setConfigValue(String.valueOf(Integer.parseInt(currentCode)+1));
        int result = 0;
        if(!isExist){
            result = configService.insertConfigWithoutRedis(update);
        }else{
            result = configService.updateConfigValueByKeyWithoutRedis(update);
        }
        if (result != 1){
            log.error("ASN编号生成失败，{}", tblAsn.getSuppCode());
            throw new RuntimeException(StringUtils.format(MessageUtils.message("asn.code.gen.error")));
        }
        tblAsn.setAsnCode(asnCode);
        for (int i = 0; i < tblAsn.getDetail().size(); i++){
            tblAsn.getDetail().get(i).setDnNo(asnCode+"-"+(i+1));
        }
    }

    private String getBarcodeStart(TblAsn tblAsn){
        String start = String.valueOf(tblAsn.getSuppCode().charAt(0));
        switch(start){
            case "1":
                return "5";
            case "2":
                return "6";
            default:
                return start;
        }
    }

    private void genBarCode(TblAsn tblAsn){
        String prefix = getBarcodeStart(tblAsn)+tblAsn.getSuppCode().substring(tblAsn.getSuppCode().length()-3);
        String barcodeKey = DataConstants.BAR_CODE_PARAM+"."+tblAsn.getCompCode();
        int currentCodeSeq = Integer.parseInt(configService.selectConfigByKey(barcodeKey));
        for (TblAsnItem item: tblAsn.getDetail()){
            for (TblAsnArticle article: item.getArticles()){
                StringBuilder currentCode = new StringBuilder(String.valueOf(currentCodeSeq));
                while(currentCode.length() < 6){
                    currentCode.insert(0, "0");
                }
                String startWith = prefix+ currentCode;
                currentCodeSeq = article.getPackQty().intValue() + currentCodeSeq;
                StringBuilder endCode = new StringBuilder(String.valueOf(currentCodeSeq - 1));
                while(endCode.length() < 6){
                    endCode.insert(0, "0");
                }
                String endWith = prefix + endCode;
                article.setStartWith(startWith);
                article.setEndWith(endWith);
            }
        }
        SysConfig update = new SysConfig();
        update.setConfigKey(barcodeKey);
        update.setConfigValue(String.valueOf(currentCodeSeq));
        configService.updateConfigValueByKey(update);
    }

    private HashMap sendToSap(TblAsn tblAsn){
        List<TblOrder> orderList = new ArrayList<>();
        Set<String> orderCodeList = tblAsn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            orderList.addAll(orderService.selectTblOrderWithItemList(search));
        }
        
        try {
            // 直接获取SAP请求体
            Map<String, Object> requestBody = SapObjectConverter.convertToSapAsn(tblAsn, orderList);
            
            // 调用SAP接口
            String sapUrl = configService.selectConfigByKey(DataConstants.SAP_ASN_URL);
            if (StringUtils.isEmpty(sapUrl)) {
                throw new RuntimeException("SAP订单确认接口URL未配置");
            }
            HashMap<String, Object> result = sapApiClient.callSapApi(sapUrl, requestBody);
            
            log.info("Asn {} send to sap result:{}", tblAsn.getAsnCode(), result);
            
            // 检查返回结果
            if (result != null) {
                // 检查是否有错误信息
                if (result.containsKey("MSTYP") && "E".equals(result.get("MSTYP"))) {
                    String errorMsg = (String) result.get("MSG");
                    throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), errorMsg != null ? errorMsg : "SAP接口异常"));
                }
                if (result.containsKey("MSTYP") && "S".equals(result.get("MSTYP"))) {
                    String docNo = (String) result.get("VBELN");
                    if (StringUtils.isNotEmpty(docNo)) {
                        tblAsn.setDocNo(docNo); // SAP返回的交货单号
                        tblAsnMapper.updateTblAsn(tblAsn);
                    }
//                    else {
//                        throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), "SAP接口返回的单据号为空"));
//                    }
                }
            } else {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("sap.asn.error"), "SAP接口返回为空"));
            }
            
            return result;
        } catch (RuntimeException e) {
            log.error("发送ASN到SAP时发生错误", e);
            throw e;
        }
    }

    private Map<String, Object> genPrintData(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, TblOrder> orderMap = new HashMap<>();
        Set<String> orderCodeList = asn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            orderMap.put(orderCode, orderService.selectTblOrderWithItemList(search).get(0));
        }
        Map<String, Object> result = new HashMap<>();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("compCode", asn.getCompCode());
        headerMap.put("plantCode", asn.getDetail().get(0).getPlantCode());
        headerMap.put("compName", orderMap.get(asn.getDetail().get(0).getOrderCode()).getCompName());
        headerMap.put("asnCode", asn.getAsnCode());
        headerMap.put("deliveryDate", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd", timeZone));
        headerMap.put("deliveryTime", DateFormatUtils.format(asn.getDeliveryDate(), "HH:mm", timeZone));
        headerMap.put("suppCode", asn.getSuppCode());
        headerMap.put("suppName", asn.getSuppName());
        headerMap.put("depot", orderMap.get(asn.getDetail().get(0).getOrderCode()).getDetail().get(0).getDepot());
        headerMap.put("deliveryDateTime", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd HH:mm", timeZone));
        headerMap.put("unloadingNo", asn.getDetail().get(0).getUnloadingNo());
        headerMap.put("createDate", DateFormatUtils.format(asn.getCreateTime(), "yyyy/MM/dd"));
        StringBuilder qrSb = new StringBuilder();
        List<Map<String, Object>> dataMapList = new ArrayList<>();
        int lineCode = 1;
        for (TblAsnItem item:asn.getDetail()){
            for (TblAsnArticle article: item.getArticles()){
                qrSb.append(Util.fillAfter(item.getOrderCode(),10, ' '));
                qrSb.append(Util.fillAfter(article.getArticleNo(), 25, ' '));
                qrSb.append(Util.fillBefore(article.getQuantity().toString(), 10, '0'));
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("orderCode", item.getOrderCode());
                String articleNo = article.getArticleNo();
                String productClass = SapObjectConverter.handleProductClass(dictDataService, articleNo);
                if (StringUtils.isNotEmpty(productClass)) {
                    articleNo = article.getArticleNo().substring(0, article.getArticleNo().indexOf("+"));
                }
                dataMap.put("productClass", productClass);
                dataMap.put("articleNo", articleNo);
                dataMap.put("quantity", article.getQuantity());
                dataMap.put("unit", article.getUnit());
                dataMapList.add(dataMap);
            }
        }

        headerMap.put("qrCode", Util.fillAfter(qrSb.toString(), 900, ' '));
        result.put("header", headerMap);
        result.put("data", dataMapList);
        return result;
    }

    private Map<String, Object> genPrintDataForPickingList(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, TblOrder> orderMap = new HashMap<>();
        Set<String> orderCodeList = asn.getDetail().stream().map(TblAsnItem::getOrderCode).collect(Collectors.toSet());
        for (String orderCode : orderCodeList)
        {
            TblOrder search = new TblOrder();
            search.setOrderCode(orderCode);
            List<TblOrder> orders = orderService.selectTblOrderWithItemList(search);
            if (!orders.isEmpty()) {
                orderMap.put(orderCode, orders.get(0));
            }
        }
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息 - 现在suppName已经从数据库中获取了正确的值
        headerMap.put("suppCode", asn.getSuppCode());
        headerMap.put("suppName", asn.getSuppName()); // 这里已经是从sys_dept表关联获取的dept_name
        headerMap.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd", timeZone));

        // 获取当前登录用户作为打印人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String printedBy = loginUser != null ? loginUser.getUser().getNickName() : "System";
        headerMap.put("printedBy", printedBy);



        List<Map<String, Object>> dataMapList = new ArrayList<>();
        int lineCode = 1;

        for (TblAsnItem item : asn.getDetail()){
            TblOrder order = orderMap.get(item.getOrderCode());
            for (TblAsnArticle article : item.getArticles()){
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("asnno", asn.getAsnCode());
                dataMap.put("orderNo", item.getOrderCode());
                dataMap.put("orderLineNo", article.getOrderLineNo());
                dataMap.put("releaseNo", article.getDeliveryScheduleNo());
                dataMap.put("partNo", article.getArticleNo());
                // 这里的articleName已经是从tbl_material表关联获取的Material_Name，如果没有则使用原来的Article_Name
                dataMap.put("partDescription", article.getArticleName());

                // 从订单中获取到期日期
                String dueDate = "";
                if (order != null && order.getDetail() != null) {
                    for (TblOrderItem orderItem : order.getDetail()) {
                        if (orderItem.getItemNo().equals(article.getOrderLineNo())) {
                            if (orderItem.getDeliveryDate() != null) {
                                dueDate = DateFormatUtils.format(orderItem.getDeliveryDate(), "yyyy-MM-dd", timeZone);
                            }
                            break;
                        }
                    }
                }
                dataMap.put("dueDate", dueDate);

                dataMap.put("shipQty", article.getQuantity());
                dataMap.put("unit", article.getUnit());
                dataMapList.add(dataMap);
            }
        }

        result.put("header", headerMap);
        result.put("data", dataMapList);
        return result;
    }

    private Map<String, Object> genPrintDataForNpsSls(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息
        headerMap.put("asnno", asn.getAsnCode());
        headerMap.put("sapasnNo", asn.getDocNo() != null ? asn.getDocNo() : "");
        headerMap.put("supplierName", asn.getSuppName() != null ? asn.getSuppName() : "");
        headerMap.put("deliveryDate", asn.getDeliveryDate() != null ?
            DateFormatUtils.format(asn.getDeliveryDate(), "yyyy/MM/dd", timeZone) : "");

        // 公司信息暂时留空
        headerMap.put("companyName", "");
        headerMap.put("companyAddress", "");
        headerMap.put("c_Phone", "");
        headerMap.put("c_FAX", "");
        headerMap.put("phone", "");
        headerMap.put("fax", "");

        // 工厂信息
        if (asn.getDetail() != null && !asn.getDetail().isEmpty()) {
            TblAsnItem firstItem = asn.getDetail().get(0);
            headerMap.put("factoryCode", firstItem.getPlantCode() != null ? firstItem.getPlantCode() : "");
            headerMap.put("deliveryAddress", firstItem.getRcvLocNo() != null ? firstItem.getRcvLocNo() : "");
        } else {
            headerMap.put("factoryCode", "");
            headerMap.put("deliveryAddress", "");
        }

        // 获取当前登录用户作为打印人
        String printedBy = "System";
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null && loginUser.getUser() != null) {
                printedBy = loginUser.getUser().getNickName();
            }
        } catch (Exception e) {
            // 如果获取用户信息失败，使用默认值
        }
        headerMap.put("userLoginName", printedBy);

        // 生成物料明细数据
        List<Map<String, Object>> allDataList = new ArrayList<>();
        int lineCode = 1;

        if (asn.getDetail() != null) {
            for (TblAsnItem item : asn.getDetail()){
                if (item.getArticles() != null) {
                    for (TblAsnArticle article : item.getArticles()){
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("lineNo", String.valueOf(lineCode++));
                dataMap.put("partNo", article.getArticleNo());
                dataMap.put("partDescription", article.getArticleName() != null ? article.getArticleName() : "");
                dataMap.put("orderNo", item.getOrderCode());
                dataMap.put("orderLineNo", article.getOrderLineNo() != null ? article.getOrderLineNo() : "");
                dataMap.put("releaseNo", article.getDeliveryScheduleNo() != null ? article.getDeliveryScheduleNo() : "");
                dataMap.put("shipQty", article.getQuantity());
                        dataMap.put("remark", ""); // 备注暂时留空
                        allDataList.add(dataMap);
                    }
                }
            }
        }

        // 按每页5行进行分页处理
        List<List<Map<String, Object>>> pagedData = new ArrayList<>();
        for (int i = 0; i < allDataList.size(); i += 5) {
            int endIndex = Math.min(i + 5, allDataList.size());
            List<Map<String, Object>> pageData = new ArrayList<>(allDataList.subList(i, endIndex));

            // 如果不足5行，补充空行
            while (pageData.size() < 5) {
                Map<String, Object> emptyRow = new HashMap<>();
                emptyRow.put("lineNo", "");
                emptyRow.put("partNo", "");
                emptyRow.put("partDescription", "");
                emptyRow.put("orderNo", "");
                emptyRow.put("orderLineNo", "");
                emptyRow.put("releaseNo", "");
                emptyRow.put("shipQty", "");
                emptyRow.put("remark", "");
                pageData.add(emptyRow);
            }
            pagedData.add(pageData);
        }

        headerMap.put("asnList", pagedData);
        result.put("header", headerMap);
        result.put("data", allDataList); // JasperReports需要的数据源
        return result;
    }

    private Map<String, Object> genPrintDataForProdTag(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息（如果需要的话）
        headerMap.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd", timeZone));

        // 生成现品票数据列表
        List<Map<String, Object>> allDataList = new ArrayList<>();

        if (asn.getDetail() != null) {
            for (TblAsnItem item : asn.getDetail()) {
                if (item.getArticles() != null) {
                    for (TblAsnArticle article : item.getArticles()) {
                        // 从箱信息表获取箱数据
                        List<TblAsnBox> boxes = tblAsnBoxService.selectTblAsnBoxByArticleId(article.getArticleId());
                        if (boxes == null || boxes.isEmpty()) {
                            // 如果没有箱信息，则使用原有逻辑作为兜底
                            boxes = generateDefaultBoxes(article);
                        }
                        // 为每个箱子生成一个现品票
                        // 先对boxes根据boxNo转换成数字后进行排序
                        boxes.sort(Comparator.comparingInt(box -> Integer.parseInt(box.getBoxNo())));
                        for (TblAsnBox box : boxes) {
                            BigDecimal currentBoxQty = box.getQuantity();
                            BigDecimal snp = article.getQtyPerPack() != null ? article.getQtyPerPack() : BigDecimal.ONE;

                            // 判断是否为尾数SNP
                            boolean tailSNP = currentBoxQty.compareTo(snp) < 0;

                            Map<String, Object> dataMap = new HashMap<>();

                            // 基本物料信息
                            dataMap.put("partNo", article.getArticleNo() != null ? article.getArticleNo() : "");
                            dataMap.put("partDescription", article.getArticleName() != null ? article.getArticleName() : "");
                            dataMap.put("batch", box.getBatchNo() != null ? box.getBatchNo() : "");

                            // 供应商信息
                            dataMap.put("supplierCode", asn.getSuppCode() != null ? asn.getSuppCode() : "");
                            dataMap.put("supplierName", asn.getSuppName() != null ? asn.getSuppName() : "");

                            // 数量和SNP信息
                            dataMap.put("shipQty", article.getQuantity());
                            dataMap.put("snp", currentBoxQty);
                            dataMap.put("tailSNP", tailSNP);

                            // 订单信息
                            dataMap.put("orderNo", item.getOrderCode() != null ? item.getOrderCode() : "");
                            dataMap.put("orderLineNo", article.getOrderLineNo() != null ? article.getOrderLineNo() : "");
                            dataMap.put("releaseNo", article.getDeliveryScheduleNo() != null ? article.getDeliveryScheduleNo() : "");
                            dataMap.put("dueDate", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy-MM-dd", timeZone));

                            dataMap.put("label", box.getBoxNo() + "/" + boxes.size());

                            // 托盘信息从箱信息中获取
                            String palletNo = box.getPalletNo() != null ? box.getPalletNo() : "";
                            if (!palletNo.isEmpty()) {
                                dataMap.put("palletNo", palletNo);
                            }

                            // 生成QR码数据
                            String qrCodeData = "ASN@" + asn.getAsnCode() + "%PO@" + item.getOrderCode() + "%ITEM@" + article.getOrderLineNo()
                                    + "%RELEASE@" + article.getDeliveryScheduleNo() + "%BATCH@" + box.getBatchNo()
                                    + "%PALLET@" + palletNo + "%BOX@" + box.getBoxNo() + "%BQTY@" + currentBoxQty + "%";
                            dataMap.put("qrCodeData", qrCodeData);

                            allDataList.add(dataMap);
                        }

                    }
                }
            }
        }

        result.put("header", headerMap);
        result.put("data", allDataList);
        return result;
    }

    private Map<String, Object> genPrintDataForPalletTag(TblAsn asn, String tz){
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();

        // 设置头部信息
        headerMap.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd", timeZone));

        // 生成托盘标签数据列表
        List<Map<String, Object>> allDataList = new ArrayList<>();

        if (asn.getDetail() != null) {
            // 按零件号分组收集所有箱信息
            Map<String, List<TblAsnBox>> articleBoxMap = new HashMap<>();
            Map<String, TblAsnArticle> articleInfoMap = new HashMap<>();
            Map<String, TblAsnItem> itemInfoMap = new HashMap<>();
            
            // 收集所有零件下的箱信息
            for (TblAsnItem item : asn.getDetail()) {
                if (item.getArticles() != null) {
                    for (TblAsnArticle article : item.getArticles()) {
                        String articleKey = article.getArticleNo();
                        
                        // 从箱信息表获取箱数据
                        List<TblAsnBox> boxes = article.getBoxes();
                        if (boxes == null || boxes.isEmpty()) {
                            // 如果没有箱信息，则使用原有逻辑作为兜底
                            boxes = generateDefaultBoxes(article);
                        }
                        
                        // 先对boxes根据boxNo转换成数字后进行排序
                        boxes.sort(Comparator.comparingInt(box -> Integer.parseInt(box.getBoxNo())));
                        
                        // 按零件号分组收集箱信息
                        articleBoxMap.computeIfAbsent(articleKey, k -> new ArrayList<>()).addAll(boxes);
                        
                        // 保存零件和行项目信息（用于后续生成标签）
                        if (!articleInfoMap.containsKey(articleKey)) {
                            articleInfoMap.put(articleKey, article);
                            itemInfoMap.put(articleKey, item);
                        }
                    }
                }
            }
            
            // 计算所有零件下的托盘总数
            int totalPalletCount = 0;
            for (List<TblAsnBox> boxes : articleBoxMap.values()) {
                Set<String> palletNoSet = boxes.stream()
                    .map(TblAsnBox::getPalletNo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
                totalPalletCount += palletNoSet.size();
            }
            
            // 为每个零件生成托标签
            for (Map.Entry<String, List<TblAsnBox>> entry : articleBoxMap.entrySet()) {
                String articleNo = entry.getKey();
                List<TblAsnBox> boxes = entry.getValue();
                TblAsnArticle article = articleInfoMap.get(articleNo);
                TblAsnItem item = itemInfoMap.get(articleNo);
                
                // 生成该零件的托标签
                generatePalletLabelsForArticle(boxes, asn, item, article, totalPalletCount, allDataList);
            }
        }

        result.put("header", headerMap);
        // 如果allDataList中有值重复的元素，则删除重复的元素，确保托盘标签不重复
        allDataList = allDataList.stream().distinct().collect(Collectors.toList());
        result.put("data", allDataList);
        return result;
    }

    /**
     * 按物料分组为所有文章生成箱记录并分配托号（先排完一个零件再排下一个）
     */
    private void createBoxesForArticlesGroupedByMaterial(Map<String, List<TblAsnArticle>> materialToArticles,
                                                         Map<TblAsnArticle, TblAsnItem> articleToItem,
                                                         AtomicInteger globalAssignedPalletCount) {
        for (Map.Entry<String, List<TblAsnArticle>> entry : materialToArticles.entrySet()) {
            List<TblAsnArticle> groupedArticles = entry.getValue();
            if (groupedArticles == null || groupedArticles.isEmpty()) {
                continue;
            }

            // 使用首个物料的工厂获取托SNP配置（要求同一ASN同一物料配置一致）
            TblAsnArticle firstArticle = groupedArticles.get(0);
            TblAsnItem firstItem = articleToItem.get(firstArticle);
            PalletInfoVO palletInfoVO = new PalletInfoVO();
            palletInfoVO.setPlantCode(firstItem.getPlantCode());
            palletInfoVO.setMaterialCode(firstArticle.getArticleNo());
            List<PalletInfoVO> palletInfoVOS = tblPalletService.selectTblPalletList(palletInfoVO);
            if (!palletInfoVOS.isEmpty() && palletInfoVOS.get(0).getSnpQuantity() != null && palletInfoVOS.get(0).getSnpQuantity() > 0) {
//                throw new RuntimeException("工厂" + firstItem.getPlantCode() + "物料" + firstArticle.getArticleNo() + "没有有效托盘SNP配置");
                int palletSnp = palletInfoVOS.get(0).getSnpQuantity();

                // 校验同一物料在ASN内的SNP配置一致（不同工厂配置不一致则报错）
                for (TblAsnArticle a : groupedArticles) {
                    TblAsnItem item = articleToItem.get(a);
                    PalletInfoVO vo = new PalletInfoVO();
                    vo.setPlantCode(item.getPlantCode());
                    vo.setMaterialCode(a.getArticleNo());
                    List<PalletInfoVO> vos = tblPalletService.selectTblPalletList(vo);
                    if (vos.isEmpty() || vos.get(0).getSnpQuantity() == null || vos.get(0).getSnpQuantity() <= 0 || !vos.get(0).getSnpQuantity().equals(palletSnp)) {
                        throw new RuntimeException("同一ASN内物料" + a.getArticleNo() + "的托盘SNP配置不一致，请检查");
                    }
                }

                // 逐箱分配托号（先按当前物料全部分配，再递增全局托号偏移）
                int globalOffset = globalAssignedPalletCount.get();
                int cumulativeBoxes = 0;
                for (TblAsnArticle article : groupedArticles) {
                    TblAsnItem item = articleToItem.get(article);
                    // 确保存在箱数据
                    if (article.getBoxes() == null || article.getBoxes().isEmpty()) {
                        article.setBoxes(generateDefaultBoxes(article));
                    }
                    for (int i = 0; i < article.getBoxes().size(); i++) {
                        TblAsnBox box = article.getBoxes().get(i);
                        box.setPalletQty(BigDecimal.valueOf(palletSnp));
                        int localPalletNo = (cumulativeBoxes) / palletSnp + 1;
                        int globalPalletNo = globalOffset + localPalletNo;
                        box.setPalletNo(String.valueOf(globalPalletNo));
                        cumulativeBoxes++;
                    }
                }

                // 当前物料占用的托数
                int totalPalletsForThisMaterial = (cumulativeBoxes + palletSnp - 1) / palletSnp;
                globalAssignedPalletCount.addAndGet(totalPalletsForThisMaterial);
            }

            // 持久化箱记录（保持每个文章对应的工厂调用）
            for (TblAsnArticle article : groupedArticles) {
                TblAsnItem item = articleToItem.get(article);
                tblAsnBoxService.generateBoxesForArticle(article, item.getPlantCode());
            }
        }
    }

    /**
     * 生成默认箱信息（兜底逻辑）
     *
     * @param article 物料信息
     * @return 箱信息列表
     */
    private List<TblAsnBox> generateDefaultBoxes(TblAsnArticle article) {
        List<TblAsnBox> boxes = new ArrayList<>();
        if (article.getPackQty() != null && article.getQtyPerPack() != null) {
            int packQty = article.getPackQty().intValue();
            int qtyPerPack = article.getQtyPerPack().intValue();

            for (int i = 1; i <= packQty; i++) {
                TblAsnBox box = new TblAsnBox();
                box.setArticleId(article.getArticleId());
                box.setBoxNo(String.valueOf(i));
                box.setBatchNo(article.getBatchNo());
                box.setQuantity(new BigDecimal(qtyPerPack));
                boxes.add(box);
            }
        }
        return boxes;
    }

    /**
     * 为指定零件生成托标签数据（按零件分组，支持跨订单）
     *
     * @param boxes         箱信息列表
     * @param asn           ASN信息
     * @param item          ASN行项目信息（代表零件）
     * @param article       ASN物料信息
     * @param totalPalletCount 所有零件的托盘总数
     * @param allDataList   数据列表
     */
    private void generatePalletLabelsForArticle(List<TblAsnBox> boxes, TblAsn asn,
                                               TblAsnItem item, TblAsnArticle article, int totalPalletCount, List<Map<String, Object>> allDataList) {
        // 按托盘号分组箱信息
        Map<String, List<TblAsnBox>> palletBoxMap = new HashMap<>();

        for (TblAsnBox box : boxes) {
            if (box.getPalletQty() == null) {
                throw new RuntimeException(StringUtils.format(MessageUtils.message("pallet.info.not.configured"), item.getPlantCode(), article.getArticleNo()));
            }
            String palletNo = box.getPalletNo() != null ? box.getPalletNo() : "1"; // 默认托盘号为1
            palletBoxMap.computeIfAbsent(palletNo, k -> new ArrayList<>()).add(box);
        }

        // 收集该零件涉及的所有订单信息
        Set<String> orderCodes = new HashSet<>();
        Set<String> orderLineNos = new HashSet<>();
        for (TblAsnBox box : boxes) {
            // 从ASN中查找该箱对应的订单信息
            for (TblAsnItem asnItem : asn.getDetail()) {
                if (asnItem.getArticles() != null) {
                    for (TblAsnArticle asnArticle : asnItem.getArticles()) {
                        if (asnArticle.getArticleNo().equals(article.getArticleNo()) && 
                            asnArticle.getBoxes() != null) {
                            // 检查该箱是否属于这个物料
                            boolean boxBelongsToArticle = asnArticle.getBoxes().stream()
                                .anyMatch(b -> b.getBoxId() != null && b.getBoxId().equals(box.getBoxId()));
                            if (boxBelongsToArticle) {
                                if (asnItem.getOrderCode() != null) {
                                    orderCodes.add(asnItem.getOrderCode());
                                }
                                if (asnArticle.getOrderLineNo() != null) {
                                    orderLineNos.add(asnArticle.getOrderLineNo());
                                }
                            }
                        }
                    }
                }
            }
        }

        // 为每个托盘生成标签
        int palletIndex = 1;
        for (Map.Entry<String, List<TblAsnBox>> entry : palletBoxMap.entrySet()) {
            String palletNo = entry.getKey();
            List<TblAsnBox> palletBoxes = entry.getValue();

            // 计算托盘总数量
            BigDecimal palletQty = palletBoxes.stream()
                    .map(TblAsnBox::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 判断是否为尾托盘
            boolean tailTag = palletBoxes.size() < palletBoxes.get(0).getPalletQty().intValue();

            Map<String, Object> dataMap = new HashMap<>();

            // 基本物料信息
            dataMap.put("partNo", article.getArticleNo() != null ? article.getArticleNo() : "");
            dataMap.put("partDescription", article.getArticleName() != null ? article.getArticleName() : "");

            // 供应商信息
            dataMap.put("supplierCode", asn.getSuppCode() != null ? asn.getSuppCode() : "");
            dataMap.put("supplierName", asn.getSuppName() != null ? asn.getSuppName() : "");

            // 托盘信息
            if (tailTag) {
                // 尾托盘显示实际数量
                dataMap.put("tailTag", true);
                dataMap.put("tailSNPQty", palletQty);
            } else {
                // 正常托盘显示当前托盘的箱子数量和包装数
                dataMap.put("tailTag", false);
                dataMap.put("pallet", palletBoxes.get(0).getPalletQty());
                dataMap.put("snp", palletQty);
            }

            // 交货日期
            dataMap.put("dueDate", DateFormatUtils.format(asn.getDeliveryDate(), "yyyy-MM-dd"));

            // 订单信息汇总（跨订单显示）
            dataMap.put("orderNo", String.join(",", orderCodes));
            dataMap.put("orderLineNo", String.join(",", orderLineNos));

            // 工厂和地址信息
            dataMap.put("factoryCode", item.getPlantCode() != null ? item.getPlantCode() : "");
            dataMap.put("deliveryAddress", item.getRcvLocNo() != null ? item.getRcvLocNo() : "");

            // 标签编号
            dataMap.put("label", palletNo + "/" + totalPalletCount);

            // 生成QR码数据
            String qrCodeData = "ASN@" + asn.getAsnCode() + "%PALLET@" + palletNo + "%PQTY@" + palletQty + "%";
            dataMap.put("qrCodeData", qrCodeData);

            allDataList.add(dataMap);
            palletIndex++;
        }
    }

}
