package com.datalink.datamanage.service.impl;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.sql.*;
import java.util.Random;

public class accountCreator {



        private static final String DB_URL = "*****************************************************************************"; // 替换为你的数据库 URL
        private static final String DB_USER = "root";       // 替换为你的数据库用户名
        private static final String DB_PASSWORD = "123456";   // 替换为你的数据库密码
        private static final String TABLE_NAME = "sys_user_account_1";     // 替换为你的表名
        private static final String ID_COLUMN = "user_id";        // 替换为你的主键列名
        private static final String PASSWORD_COLUMN = "password";  // 替换为需要加密的密码列名
        private static final String PLAIN_PASSWORD_COLUMN = "plain_password"; // 存储明文密码的列名

        private static final int PASSWORD_LENGTH = 14; // 设置密码长度

        public static void main(String[] args) {
            // 加载MySQL JDBC驱动
            try {
                Class.forName("com.mysql.cj.jdbc.Driver");
            } catch (ClassNotFoundException e) {
                System.err.println("找不到MySQL JDBC驱动: " + e.getMessage());
                e.printStackTrace();
                return;
            }

            BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
            Random random = new Random();

            System.out.println("连接数据库");

            try (Connection connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                 Statement statement = connection.createStatement()) {

                System.out.println("处理开始!");
                // 1. 查询所有用户
                String selectQuery = "SELECT " + ID_COLUMN + " FROM " + TABLE_NAME;
                ResultSet resultSet = statement.executeQuery(selectQuery);

                // 收集所有ID，避免在遍历ResultSet时执行更新操作导致的问题
                java.util.List<Integer> userIds = new java.util.ArrayList<>();
                while (resultSet.next()) {
                    int id = resultSet.getInt(ID_COLUMN);
                    userIds.add(id);
                }
                
                // 关闭ResultSet
                resultSet.close();

                // 处理每个用户
                for (int id : userIds) {
                    // 2. 生成 14 位随机密码
                    String plainPassword = generateRandomPassword(PASSWORD_LENGTH, random);

                    // 3. 使用 BCryptPasswordEncoder 加密
                    String hashedPassword = passwordEncoder.encode(plainPassword);

                    // 4. 更新数据库，同时保存明文和密文
                    String updateQuery = "UPDATE " + TABLE_NAME + " SET " +
                            PASSWORD_COLUMN + " = '" + hashedPassword + "', " +
                            PLAIN_PASSWORD_COLUMN + " = '" + plainPassword + "' " +
                            "WHERE " + ID_COLUMN + " = " + id;
                    statement.executeUpdate(updateQuery);

                    System.out.println("已处理 ID: " + id + ", 明文密码: " + plainPassword);
                }

                System.out.println("处理完成!");

            } catch (SQLException e) {
                e.printStackTrace();
            }
        }

        // 生成随机密码的方法
        private static String generateRandomPassword(int length, Random random) {
            String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            StringBuilder sb = new StringBuilder(length);
            for (int i = 0; i < length; i++) {
                int randomIndex = random.nextInt(characters.length());
                sb.append(characters.charAt(randomIndex));
            }
            return sb.toString();
        }

}