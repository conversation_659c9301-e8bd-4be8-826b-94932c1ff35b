package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.domain.TblMaterial;
import com.datalink.datamanage.domain.TblMaterialPlant;
import com.datalink.datamanage.mapper.TblMaterialMapper;
import com.datalink.datamanage.mapper.TblMaterialPlantMapper;
import com.datalink.datamanage.service.ITblMaterialService;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 物料信息Service业务层处理
 */
@Service
public class TblMaterialServiceImpl implements ITblMaterialService {
    @Autowired
    private TblMaterialMapper tblMaterialMapper;

    @Autowired
    private TblMaterialPlantMapper tblMaterialPlantMapper;

    /**
     * 查询物料信息
     *
     * @param materialId 物料信息ID
     * @return 物料信息
     */
    @Override
    public TblMaterial selectTblMaterialById(Long materialId) {
        return tblMaterialMapper.selectTblMaterialById(materialId);
    }

    /**
     * 查询物料信息列表
     *
     * @param tblMaterial 物料信息
     * @return 物料信息
     */
    @Override
    public List<TblMaterial> selectTblMaterialList(TblMaterial tblMaterial) {
        return tblMaterialMapper.selectTblMaterialList(tblMaterial);
    }

    /**
     * 根据物料编号查询物料信息
     *
     * @param materialCode 物料编号
     * @return 物料信息
     */
    @Override
    public TblMaterial selectTblMaterialByCode(String materialCode) {
        return tblMaterialMapper.selectTblMaterialByCode(materialCode);
    }

    /**
     * 根据物料编号和工厂编号查询物料信息
     *
     * @param materialCode 物料编号
     * @param plantCode 工厂编号
     * @return 物料信息
     */
    @Override
    public TblMaterial selectTblMaterialByCodeAndPlant(String materialCode, String plantCode) {
        return tblMaterialMapper.selectTblMaterialByCodeAndPlant(materialCode, plantCode);
    }

    /**
     * 新增物料信息
     *
     * @param tblMaterial 物料信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertTblMaterial(TblMaterial tblMaterial) {
        tblMaterial.setCreateTime(DateUtils.getNowDate());
        tblMaterial.setDelFlag("0");
        int rows = tblMaterialMapper.insertTblMaterial(tblMaterial);
        insertTblMaterialPlant(tblMaterial);
        return rows;
    }

    /**
     * 修改物料信息
     *
     * @param tblMaterial 物料信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateTblMaterial(TblMaterial tblMaterial) {
        tblMaterial.setUpdateTime(DateUtils.getNowDate());
        if (!Collections.isEmpty(tblMaterial.getPlants())) {
            // SAP更新物料时只传更新部分的信息，所以如果工厂没有修改报文里就没有工厂信息，此时不需要删除工厂信息
            tblMaterialPlantMapper.deleteTblMaterialPlantByMaterialId(tblMaterial.getMaterialId());
            insertTblMaterialPlant(tblMaterial);
        }
        return tblMaterialMapper.updateTblMaterial(tblMaterial);
    }

    /**
     * 批量删除物料信息
     *
     * @param materialIds 需要删除的物料信息ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTblMaterialByIds(Long[] materialIds) {
        for (Long materialId : materialIds) {
            tblMaterialPlantMapper.deleteTblMaterialPlantByMaterialId(materialId);
        }
        return tblMaterialMapper.deleteTblMaterialByIds(materialIds);
    }

    /**
     * 删除物料信息信息
     *
     * @param materialId 物料信息ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteTblMaterialById(Long materialId) {
        tblMaterialPlantMapper.deleteTblMaterialPlantByMaterialId(materialId);
        return tblMaterialMapper.deleteTblMaterialById(materialId);
    }

    /**
     * 新增物料工厂信息
     *
     * @param tblMaterial 物料信息
     */
    public void insertTblMaterialPlant(TblMaterial tblMaterial) {
        List<TblMaterialPlant> plants = tblMaterial.getPlants();
        if (plants != null && !plants.isEmpty()) {
            for (TblMaterialPlant plant : plants) {
                plant.setMaterialId(tblMaterial.getMaterialId());
                plant.setCreateTime(DateUtils.getNowDate());
                plant.setCreateBy(tblMaterial.getCreateBy());
                plant.setDelFlag("0");
            }
            tblMaterialPlantMapper.batchInsertTblMaterialPlant(plants);
        }
    }
} 