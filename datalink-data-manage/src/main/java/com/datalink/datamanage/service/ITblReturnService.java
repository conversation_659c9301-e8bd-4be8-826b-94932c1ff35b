package com.datalink.datamanage.service;

import java.util.List;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.TblReturn;
import com.datalink.datamanage.domain.TblReturnBox;

/**
 * 退货主表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface ITblReturnService 
{
    /**
     * 查询退货主表
     * 
     * @param returnId 退货主表主键
     * @return 退货主表
     */
    public TblReturn selectTblReturnByReturnId(Long returnId);

    /**
     * 根据退货单号查询退货主表
     * 
     * @param returnNo 退货单号
     * @return 退货主表
     */
    public TblReturn selectTblReturnByReturnNo(String returnNo);

    /**
     * 查询退货主表列表
     * 
     * @param tblReturn 退货主表
     * @return 退货主表集合
     */
    public List<TblReturn> selectTblReturnList(TblReturn tblReturn);

    /**
     * 查询退货主表列表（包含箱子信息）
     * 
     * @param tblReturn 退货主表
     * @return 退货主表集合
     */
    public List<TblReturn> selectTblReturnWithBoxList(TblReturn tblReturn);

    /**
     * 新增退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    public int insertTblReturn(TblReturn tblReturn);

    /**
     * 修改退货主表
     * 
     * @param tblReturn 退货主表
     * @return 结果
     */
    public int updateTblReturn(TblReturn tblReturn);

    /**
     * 批量删除退货主表
     * 
     * @param returnIds 需要删除的退货主表主键集合
     * @return 结果
     */
    public int deleteTblReturnByReturnIds(Long[] returnIds);

    /**
     * 删除退货主表信息
     * 
     * @param returnId 退货主表主键
     * @return 结果
     */
    public int deleteTblReturnByReturnId(Long returnId);

    /**
     * 接收WMS退货信息
     * 
     * @param returnReceiveData 退货接收数据
     * @return 结果
     */
    public AjaxResult receiveReturnFromWms(String returnReceiveData);

    /**
     * 修改退货批次号
     *
     * @param returnId 退货单id
     * @param boxList  箱子列表
     * @return 结果
     */
    public AjaxResult updateReturnBatchNo(long returnId, List<TblReturnBox> boxList);

    /**
     * 打印退货现品票
     *
     * @param returnId 退货单id
     * @param tz       时区
     * @return 结果
     */
    public AjaxResult printReturnTag(long returnId, String tz);

    /**
     * 回传标签信息给WMS
     *
     * @param returnId 退货单id
     * @return 结果
     */
    public AjaxResult feedbackToWms(long returnId);

}
