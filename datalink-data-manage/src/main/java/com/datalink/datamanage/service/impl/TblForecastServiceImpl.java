package com.datalink.datamanage.service.impl;

import com.datalink.common.DataConstants;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.SapApiClient;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;
import com.datalink.datamanage.mapper.TblForecastMapper;
import com.datalink.datamanage.service.ITblForecastService;
import com.datalink.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;
import java.text.SimpleDateFormat;

/**
 * 预测Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
@Service
public class TblForecastServiceImpl implements ITblForecastService
{
    private static final Logger logger = LoggerFactory.getLogger(TblForecastServiceImpl.class);

    @Autowired
    private TblForecastMapper tblForecastMapper;

    @Autowired
    private SapApiClient sapApiClient;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询预测
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    @Override
    public TblForecast selectTblForecastById(Long forecastId)
    {
        return tblForecastMapper.selectTblForecastById(forecastId);
    }

    /**
     * 查询预测列表
     *
     * @param tblForecast 预测
     * @return 预测
     */
    @Override
    public List<TblForecast> selectTblForecastList(TblForecast tblForecast)
    {
        return tblForecastMapper.selectTblForecastList(tblForecast);
    }

    /**
     * 新增预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblForecast(TblForecast tblForecast)
    {
        tblForecast.setCreateTime(DateUtils.getNowDate());
        // 设置默认状态为New
        if (StringUtils.isEmpty(tblForecast.getStatus())) {
            tblForecast.setStatus(DataConstants.FORECAST_STATUS_NEW);
        }
        // 设置默认下载状态为New
        if (StringUtils.isEmpty(tblForecast.getDownloadStatus())) {
            tblForecast.setDownloadStatus(DataConstants.FORECAST_STATUS_NEW);
        }
        int rows = tblForecastMapper.insertTblForecast(tblForecast);
        insertTblForecastItem(tblForecast);
        return rows;
    }

    /**
     * 修改预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblForecast(TblForecast tblForecast)
    {
        tblForecast.setUpdateTime(DateUtils.getNowDate());
        tblForecastMapper.deleteTblForecastItemByForecastId(tblForecast.getForecastId());
        insertTblForecastItem(tblForecast);
        return tblForecastMapper.updateTblForecast(tblForecast);
    }

    @Override
    public int updateTblForecastOnly(TblForecast tblForecast) {
        tblForecast.setUpdateTime(DateUtils.getNowDate());
        return tblForecastMapper.updateTblForecast(tblForecast);
    }

    /**
     * 批量删除预测
     *
     * @param forecastIds 需要删除的预测ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblForecastByIds(Long[] forecastIds)
    {
        tblForecastMapper.deleteTblForecastItemByForecastIds(forecastIds);
        return tblForecastMapper.deleteTblForecastByIds(forecastIds);
    }

    /**
     * 删除预测信息
     *
     * @param forecastId 预测ID
     * @return 结果
     */
    @Override
    public int deleteTblForecastById(Long forecastId)
    {
        tblForecastMapper.deleteTblForecastItemByForecastId(forecastId);
        return tblForecastMapper.deleteTblForecastById(forecastId);
    }

    /**
     * 新增预测行项目信息
     *
     * @param tblForecast 预测对象
     */
    public void insertTblForecastItem(TblForecast tblForecast)
    {
        List<TblForecastItem> tblForecastItemList = tblForecast.getDetail();
        Long forecastId = tblForecast.getForecastId();
        if (StringUtils.isNotNull(tblForecastItemList))
        {
            List<TblForecastItem> list = new ArrayList<TblForecastItem>();
            for (TblForecastItem tblForecastItem : tblForecastItemList)
            {
                tblForecastItem.setForecastId(forecastId);
                list.add(tblForecastItem);
            }
            if (list.size() > 0)
            {
                tblForecastMapper.batchTblForecastItem(list);
            }
        }
    }

    /**
     * 查询预测列表（包括预测行项目）--接口专用
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    @Override
    public List<TblForecast> selectTblForecastFullList(TblForecast tblForecast){
        return tblForecastMapper.selectTblForecastFullList(tblForecast);
    }

    @Override
    public List<TblForecast> selectTblForecastWithItemList(TblForecast tblForecast) {
        return tblForecastMapper.selectTblForecastWithItemList(tblForecast);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblForecastMapper.selectLastId();
    }

    @Override
    public List<TblForecastItem> selectTblForecastItemList(TblForecastItem forecastItem) {
        return tblForecastMapper.selectTblForecastItemList(forecastItem);
    }

    @Override
    public String selectMaxVersionByForecastCode(String forecastCode) {
        return tblForecastMapper.selectMaxVersionByForecastCode(forecastCode);
    }

    @Override
    public List<TblForecast> selectTblForecastLatestVersionList(TblForecast tblForecast, String tz) {
        // 查询条件中的时间需要转换为 UTC
        convertSearchTimeToUTC(tblForecast, tz);
        
        // 执行查询
        List<TblForecast> forecasts = tblForecastMapper.selectTblForecastLatestVersionList(tblForecast);
        
        // 将查询结果中的时间从UTC转换为用户时区
        convertResultTimesToUserTimezone(forecasts, tz);
        
        return forecasts;
    }
    
    /**
     * 将查询条件中的时间转换为UTC时区
     */
    private void convertSearchTimeToUTC(TblForecast tblForecast, String tz) {
        if (tblForecast == null || tblForecast.getParams() == null) return;
        if (tz == null) tz = "GMT+8";

        Map<String, Object> params = tblForecast.getParams();
        ZoneId userZone = ZoneId.of(tz);
        
        // 转换创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (params.containsKey("createTimeBegin")) {
            String createTimeBegin = (String) params.get("createTimeBegin");
            if (StringUtils.isNotEmpty(createTimeBegin)) {
                LocalDateTime localDateTime = LocalDateTime.parse(createTimeBegin, formatter);
                ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
                params.put("createTimeBegin", utcZonedDateTime.format(formatter));
            }
        }
        if (params.containsKey("createTimeEnd")) {
            String createTimeEnd = (String) params.get("createTimeEnd");
            if (StringUtils.isNotEmpty(createTimeEnd)) {
                LocalDateTime localDateTime = LocalDateTime.parse(createTimeEnd, formatter);
                ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
                params.put("createTimeEnd", utcZonedDateTime.format(formatter));
            }
        }
    }
    
    /**
     * 将查询结果中的时间从UTC转换为用户时区
     */
    private void convertResultTimesToUserTimezone(List<TblForecast> forecasts, String tz) {
        if (forecasts == null || forecasts.isEmpty()) return;
        if (tz == null) tz = "GMT+8";
        
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(timeZone);
        
        for (TblForecast forecast : forecasts) {
            // 转换创建时间
            if (forecast.getCreateTime() != null) {
                forecast.setCreateTime(DateUtils.parseDate(sdf.format(forecast.getCreateTime())));
            }
            
            // 转换更新时间
            if (forecast.getUpdateTime() != null) {
                forecast.setUpdateTime(DateUtils.parseDate(sdf.format(forecast.getUpdateTime())));
            }
            
            // 转换最近下载时间
            if (forecast.getLastDownloadTime() != null) {
                forecast.setLastDownloadTime(DateUtils.parseDate(sdf.format(forecast.getLastDownloadTime())));
            }
        }
    }

    @Override
    public List<TblForecast> selectTblForecastLatestVersionWithItemList(TblForecast tblForecast) {
        return tblForecastMapper.selectTblForecastLatestVersionWithItemList(tblForecast);
    }

    /**
     * 查询预测(不抱行行项目)
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    @Override
    public TblForecast selectTblForecastOnlyById(Long forecastId) {
        TblForecast param = new TblForecast();
        param.setForecastId(forecastId);
        return tblForecastMapper.selectTblForecastOnlyById(param);
    }

    /**
     * 预测确认
     *
     * @param forecastIds 预测ID列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmForecasts(List<Long> forecastIds) {
        try {
            logger.info("开始预测确认，预测ID列表: {}", forecastIds.toString());

            // 1. 查询需要确认的预测数据
            List<TblForecast> forecasts = new ArrayList<>();
            for (Long forecastId : forecastIds) {
                TblForecast forecast = selectTblForecastById(forecastId);
                if (forecast != null) {
                    if  (!DataConstants.FORECAST_STATUS_NEW.equals(forecast.getStatus())) {
                        logger.error("预测ID {} 的状态不是New，无法确认", forecastId);
                        throw new RuntimeException("预测号 " + forecast.getForecastCode() + " 的状态不是New，无法确认");
                    }
                    forecasts.add(forecast);
                }
            }

            if (forecasts.isEmpty()) {
                throw new RuntimeException("未找到需要确认的预测数据");
            }

            // 2. 构建SAP接口请求数据
            List<Map<String, Object>> sapData = new ArrayList<>();
            for (TblForecast forecast : forecasts) {
                if (forecast.getDetail() != null && !forecast.getDetail().isEmpty()) {
                    for (TblForecastItem item : forecast.getDetail()) {
                        Map<String, Object> sapItem = new HashMap<>();
                        sapItem.put("ZFNO", forecast.getForecastCode());
                        sapItem.put("ZFPOS", item.getItemNo() != null ? item.getItemNo() : "10");
                        sapData.add(sapItem);
                    }
                }
            }

            if (sapData.isEmpty()) {
                throw new RuntimeException("预测数据中没有行项目，无法确认");
            }

            // 3. 构建SAP请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("DATA", sapData);

            // 4. 获取SAP预测确认接口URL
            String sapUrl = configService.selectConfigByKey(DataConstants.SAP_FORECAST_CONFIRM_URL);
            if (StringUtils.isEmpty(sapUrl)) {
                throw new RuntimeException("SAP预测确认接口URL未配置");
            }

            // 5. 调用SAP接口
            logger.info("调用SAP预测确认接口，URL: {}, 数据: {}", sapUrl, requestBody);
            HashMap<String, Object> sapResult = sapApiClient.callSapApi(sapUrl, requestBody);

            // 6. 检查SAP接口返回结果
            if (sapResult == null) {
                throw new RuntimeException("SAP接口返回结果为空");
            }

            String mstyp = (String) sapResult.get("MSTYP");
            String msg = (String) sapResult.get("MSG");

            logger.info("SAP接口返回结果: MSTYP={}, MSG={}", mstyp, msg);

            // 7. 判断SAP接口是否成功
            if (!"S".equals(mstyp)) {
                throw new RuntimeException("SAP接口返回: " + msg);
            }

            // 8. SAP接口成功，更新数据库状态
            int result = tblForecastMapper.batchUpdateForecastStatus(forecastIds, DataConstants.FORECAST_STATUS_CONFIRMED);
            logger.info("预测确认完成，更新了 {} 条记录", result);

            return result;

        } catch (Exception e) {
            logger.error("预测确认失败: {}", e.getMessage(), e);
            throw new RuntimeException("预测确认失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TblForecast> selectTblForecastGroupByForecastId(Long forecastId) {
        return tblForecastMapper.selectTblForecastGroupByForecastId(forecastId);
    }

    @Override
    public List<TblForecast> selectTblForecastGroupWithItemsByForecastId(Long forecastId) {
        return tblForecastMapper.selectTblForecastGroupWithItemsByForecastId(forecastId);
    }
}
