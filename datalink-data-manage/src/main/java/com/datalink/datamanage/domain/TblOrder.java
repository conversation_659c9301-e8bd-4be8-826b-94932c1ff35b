package com.datalink.datamanage.domain;

import com.datalink.common.DataType;
import com.datalink.common.annotation.Excel;
import com.datalink.common.annotation.ExcelFieldOverride;
import com.datalink.common.annotation.Excels;
import com.datalink.common.utils.DateUtils;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单对象 tbl_order
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
@ExcelFieldOverride({
        @ExcelFieldOverride.FieldOverride(fieldName = "suppCode", name = "供应商代码", sort = 5),
        @ExcelFieldOverride.FieldOverride(fieldName = "suppName", name = "供应商名称", sort = 6),
        @ExcelFieldOverride.FieldOverride(fieldName = "compCode", name = "采购商代码", sort = 7),
        @ExcelFieldOverride.FieldOverride(fieldName = "createTime", isShow = false, sort = 16),
        @ExcelFieldOverride.FieldOverride(fieldName = "plantCode", isShow = false, sort = 17)
})
public class TblOrder extends BaseHeadEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long orderId;

    /** 采购组 */
//    @Excel(name = "采购组")
    @JsonAlias("plannerno")
    @NotEmpty(message = "采购组不能为空")
    @Size(max = 40, message = "采购组超长")
    private String plannerNo;

    /** 采购组描述 */
//    @Excel(name = "采购组描述")
    @JsonAlias("plannername")
//    @NotEmpty(message = "采购组描述不能为空")
    @Size(max = 40, message = "采购组描述超长")
    private String plannerName;

    /** 时间窗开始 */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
//    @Excel(name = "时间窗开始", width = 30, dateFormat = "HH:mm:ss")
    @JsonAlias("timebegin")
    private Date timeBegin;

    /** 时间窗结束 */
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
//    @Excel(name = "时间窗结束", width = 30, dateFormat = "HH:mm:ss")
    @JsonAlias("timeend")
    private Date timeEnd;

    /** 订单编号 */
    @Excel(name = "订单号", sort = 1)
    @JsonAlias("ordercode")
    @ApiModelProperty(value = "订单编号")
    @NotEmpty(message = "订单编号不能为空")
    @Size(max = 40, message = "订单编号超长")
    private String orderCode;

    //客户编号
    private String customerCode;

    //送达方 要元
    private String requester;

    //公司名称
    private String compName;

    /** 订单行项目信息 */
    @Valid
    @NotEmpty(message = "订单行项目不能为空")
    @Excels({
            @Excel(name = "行号", targetAttr = "itemNo", sort = 2),
            @Excel(name = "下达号", targetAttr = "deliveryScheduleNo", sort = 3),
            @Excel(name = "零件号", targetAttr = "articleNo", sort = 9),
            @Excel(name = "零件描述", targetAttr = "articleName", sort = 10),
            @Excel(name = "单位", targetAttr = "unit", sort = 11),
            @Excel(name = "数量", targetAttr = "quantity", cellType = Excel.ColumnType.NUMERIC, sort = 12),
            @Excel(name = "单位包装数量", targetAttr = "qtyPerPack", cellType = Excel.ColumnType.NUMERIC, sort = 13),
            @Excel(name = "收货仓库", targetAttr = "stockLoc", sort = 14),
            @Excel(name = "交货日期", targetAttr = "deliveryDate", cellType = Excel.ColumnType.NUMERIC, sort = 15, dateFormat = "yyyy-MM-dd")
    })
    private List<TblOrderItem> detail;

    /** 收发标志 */
    private String direction;

    /** Kafka发送状态 */
    private String kafkaStatus;

    private String isRead;

    private String isComplete;

    private Date sapUpdateTime;


    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;
    private Date firstReceiveTime;

    /** 订单状态 */
    @ApiModelProperty(value = "订单状态")
    private String status;

    /** 采购凭证类型 */
    @ApiModelProperty(value = "订单类型")
    private String purDocType;

    /** 删除标识 */
    private String delIden;

    /** 创建人 */
    @Excel(name = "下单员", sort = 8)
    private String creator;

    /** 货币 */
    private String currencyCode;

    /** 订单日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单日期", dateFormat = "yyyy-MM-dd", sort = 4)
    private Date docDate;

    /** 下载状态 */
//    @Excel(name = "下载状态")
    @ApiModelProperty(value = "下载状态")
    private String downloadStatus;

    /** 最近下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "最近下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近下载时间")
    private Date lastDownloadTime;

    public void setOrderId(Long orderId)
    {
        this.orderId = orderId;
    }

    public Long getOrderId()
    {
        return orderId;
    }
    public void setPlannerNo(String plannerNo)
    {
        this.plannerNo = plannerNo;
    }

    public String getPlannerNo()
    {
        return plannerNo;
    }
    public void setPlannerName(String plannerName)
    {
        this.plannerName = plannerName;
    }

    public String getPlannerName()
    {
        return plannerName;
    }
    public void setTimeBegin(Date timeBegin)
    {
        this.timeBegin = timeBegin;
    }

    public Date getTimeBegin()
    {
        return timeBegin;
    }
    public void setTimeEnd(Date timeEnd)
    {
        this.timeEnd = timeEnd;
    }

    public Date getTimeEnd()
    {
        return timeEnd;
    }
    public void setOrderCode(String orderCode)
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode()
    {
        return orderCode;
    }

    public List<TblOrderItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblOrderItem> detail)
    {
        this.detail = detail;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getKafkaStatus() {
        return kafkaStatus;
    }

    public void setKafkaStatus(String kafkaStatus) {
        this.kafkaStatus = kafkaStatus;
    }

    public String getIsRead() {
        return isRead;
    }

    public void setIsRead(String isRead) {
        this.isRead = isRead;
    }

    public String getIsComplete() {
        return isComplete;
    }

    public void setIsComplete(String isComplete) {
        this.isComplete = isComplete;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getRequester() {
        return requester;
    }

    public void setRequester(String requester) {
        this.requester = requester;
    }

    public String getCompName() {
        return compName;
    }

    public void setCompName(String compName) {
        this.compName = compName;
    }

    public Date getSapUpdateTime() {
        return sapUpdateTime;
    }

    public void setSapUpdateTime(Date sapUpdateTime) {
        this.sapUpdateTime = sapUpdateTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getFirstReceiveTime() {
        return firstReceiveTime;
    }

    public void setFirstReceiveTime(Date firstReceiveTime) {
        this.firstReceiveTime = firstReceiveTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPurDocType() {
        return purDocType;
    }

    public void setPurDocType(String purDocType) {
        this.purDocType = purDocType;
    }

    public String getDelIden() {
        return delIden;
    }

    public void setDelIden(String delIden) {
        this.delIden = delIden;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Date getDocDate() {
        return docDate;
    }

    public void setDocDate(Date docDate) {
        this.docDate = docDate;
    }

    public String getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(String downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public Date getLastDownloadTime() {
        return lastDownloadTime;
    }

    public void setLastDownloadTime(Date lastDownloadTime) {
        this.lastDownloadTime = lastDownloadTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("plannerNo", getPlannerNo())
            .append("plannerName", getPlannerName())
            .append("timeBegin", getTimeBegin())
            .append("timeEnd", getTimeEnd())
            .append("orderCode", getOrderCode())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("sapUpdateTime", getSapUpdateTime())
            .append("receiveTime", getReceiveTime())
            .append("firstReceiveTime", getFirstReceiveTime())
            .append("tblOrderItemList", getDetail())
            .append("downloadStatus", getDownloadStatus())
            .append("lastDownloadTime", getLastDownloadTime())
            .toString();
    }

    @Override
    public DataType getObjectType() {
        return DataType.PO_TYPE;
    }

    @JsonIgnore
    public Map<String, Object> buildHeaderMap(){
        Map<String, Object> header = Maps.newHashMap();
        header.put("orderCode", orderCode);
        header.put("compCode", compCode);
        header.put("plantCode", plantCode);
        header.put("plantName", plantName);
        header.put("suppCode", suppCode);
        header.put("suppName", suppName);
        header.put("plannerNo", plannerNo);
        header.put("plannerName", plannerName);
        header.put("timeBegin", null == timeBegin?"":DateUtils.parseDateToStr("HH:mm:ss", timeBegin));
        header.put("timeEnd", null == timeEnd?"":DateUtils.parseDateToStr("HH:mm:ss", timeEnd));
        return header;
    }
}
