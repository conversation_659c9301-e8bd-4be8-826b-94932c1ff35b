package com.datalink.datamanage.domain;

import java.util.Date;
import java.util.List;

import com.datalink.common.DataType;
import com.datalink.common.annotation.ExcelFieldOverride;
import com.datalink.common.annotation.Excels;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 预测对象 tbl_forecast
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
//TODO: 校验
@ApiModel(value = "预测信息")
@ExcelFieldOverride({
        @ExcelFieldOverride.FieldOverride(fieldName = "createTime", name = "发送日期", sort = 1, dateFormat = "yyyy-MM-dd HH:mm:ss"),
        @ExcelFieldOverride.FieldOverride(fieldName = "plantCode", name = "工厂", sort = 2),
        @ExcelFieldOverride.FieldOverride(fieldName = "suppCode", name = "供应商代码", sort = 5),
        @ExcelFieldOverride.FieldOverride(fieldName = "suppName", name = "供应商名称", sort = 6),
        @ExcelFieldOverride.FieldOverride(fieldName = "compCode", isShow = false, sort = 11)
})
public class TblForecast extends BaseHeadEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 预测ID */
    @ApiModelProperty(value = "预测ID")
    private Long forecastId;

    /** 预测编号 */
//    @Excel(name = "预测编号")
    @JsonAlias("forecastcode")
    @ApiModelProperty(value = "预测编号")
    private String forecastCode;

    /** 预测版本号 */
//    @Excel(name = "预测版本号")
    @ApiModelProperty(value = "预测版本号", hidden = true)
    private String version;

    /** 收发标志 */
    @ApiModelProperty(value = "收发标志", hidden = true)
    private String direction;

    /** KafKa发送状态 */
    @ApiModelProperty(value = "KafKa发送状态", hidden = true)
    private String kafkaStatus;

    /** 预测状态 */
//    @Excel(name = "预测状态")
    @ApiModelProperty(value = "预测状态")
    private String status;
    
    /** 下载状态 */
//    @Excel(name = "下载状态")
    @ApiModelProperty(value = "下载状态")
    private String downloadStatus;
    
    /** 最近下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "最近下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近下载时间")
    private Date lastDownloadTime;

    /** 预测行项目信息 */
    @ApiModelProperty(value = "预测行项目信息")
    @Excels({
            @Excel(name = "零件号", targetAttr = "articleNo", sort = 3),
            @Excel(name = "零件描述", targetAttr = "articleName", sort = 4),
            @Excel(name = "类型", targetAttr = "proType", sort = 7),
            @Excel(name = "数量", targetAttr = "quantity", cellType = Excel.ColumnType.NUMERIC, sort = 8),
            @Excel(name = "单位", targetAttr = "unit", sort = 9),
            @Excel(name = "起始日期", targetAttr = "deliveryDate", dateFormat = "yyyy-MM-dd", sort = 10)
    })
    private List<TblForecastItem> detail;

    public void setForecastId(Long forecastId)
    {
        this.forecastId = forecastId;
    }

    public Long getForecastId()
    {
        return forecastId;
    }
    public void setForecastCode(String forecastCode)
    {
        this.forecastCode = forecastCode;
    }

    public String getForecastCode()
    {
        return forecastCode;
    }
    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getVersion()
    {
        return version;
    }

    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    
    public void setDownloadStatus(String downloadStatus)
    {
        this.downloadStatus = downloadStatus;
    }

    public String getDownloadStatus()
    {
        return downloadStatus;
    }
    
    public void setLastDownloadTime(Date lastDownloadTime)
    {
        this.lastDownloadTime = lastDownloadTime;
    }

    public Date getLastDownloadTime()
    {
        return lastDownloadTime;
    }

    public List<TblForecastItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblForecastItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("forecastId", getForecastId())
            .append("forecastCode", getForecastCode())
            .append("version", getVersion())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("status", getStatus())
            .append("downloadStatus", getDownloadStatus())
            .append("lastDownloadTime", getLastDownloadTime())
            .append("tblForecastItemList", getDetail())
            .toString();
    }

    @Override
    public DataType getObjectType() {
        return DataType.FORECAST_TYPE;
    }
}
