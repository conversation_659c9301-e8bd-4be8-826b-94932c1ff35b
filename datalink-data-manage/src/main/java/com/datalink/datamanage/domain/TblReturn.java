package com.datalink.datamanage.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 退货主表对象 tbl_return
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@ApiModel("退货主表")
public class TblReturn extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 退货ID */
    private Long returnId;

    /** 退货单号 */
    @Excel(name = "退货单号")
    @ApiModelProperty(value = "退货单号")
    private String returnNo;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    @ApiModelProperty(value = "供应商代码")
    private String suppCode;

    /** 状态：New-新建，Processing-处理中，Completed-已完成 */
    @Excel(name = "状态", readConverterExp = "New=新建,Processing=处理中,Completed=已完成")
    @ApiModelProperty(value = "状态")
    private String status;

    /** 退货箱信息 */
    @ApiModelProperty(value = "退货箱信息")
    private List<TblReturnBox> boxList;

    public void setReturnId(Long returnId) 
    {
        this.returnId = returnId;
    }

    public Long getReturnId() 
    {
        return returnId;
    }
    public void setReturnNo(String returnNo) 
    {
        this.returnNo = returnNo;
    }

    public String getReturnNo() 
    {
        return returnNo;
    }
    public void setSuppCode(String suppCode) 
    {
        this.suppCode = suppCode;
    }

    public String getSuppCode() 
    {
        return suppCode;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public List<TblReturnBox> getBoxList()
    {
        return boxList;
    }

    public void setBoxList(List<TblReturnBox> boxList)
    {
        this.boxList = boxList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("returnId", getReturnId())
            .append("returnNo", getReturnNo())
            .append("suppCode", getSuppCode())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("remark", getRemark())
            .toString();
    }
}
