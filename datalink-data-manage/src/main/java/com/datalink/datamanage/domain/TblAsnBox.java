package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ASN箱信息对象 tbl_asn_box
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public class TblAsnBox extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 箱ID */
    private Long boxId;

    /** ASN物料ID */
    @Excel(name = "ASN物料ID")
    @NotNull(message = "ASN物料ID不能为空")
    @ApiModelProperty(value = "ASN物料ID")
    private Long articleId;

    /** 箱号 */
    @Excel(name = "箱号")
    @JsonAlias("boxno")
    @NotEmpty(message = "箱号不能为空")
    @ApiModelProperty(value = "箱号")
    @Size(max = 50, message = "箱号超长")
    private String boxNo;

    /** 批次号 */
    @Excel(name = "批次号")
    @JsonAlias("batchno")
    @Size(max = 50, message = "批次号超长")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 数量 */
    @Excel(name = "数量")
    @NotNull(message = "数量不能为空")
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /** 托号 */
    @Excel(name = "托号")
    @JsonAlias("palletno")
    @Size(max = 50, message = "托号超长")
    @ApiModelProperty(value = "托号")
    private String palletNo;

    /** 托数 */
    @Excel(name = "托数")
    @JsonAlias("palletqty")
    @ApiModelProperty(value = "托数")
    private BigDecimal palletQty;

    public void setBoxId(Long boxId) 
    {
        this.boxId = boxId;
    }

    public Long getBoxId() 
    {
        return boxId;
    }
    public void setArticleId(Long articleId) 
    {
        this.articleId = articleId;
    }

    public Long getArticleId() 
    {
        return articleId;
    }
    public void setBoxNo(String boxNo) 
    {
        this.boxNo = boxNo;
    }

    public String getBoxNo() 
    {
        return boxNo;
    }
    public void setBatchNo(String batchNo) 
    {
        this.batchNo = batchNo;
    }

    public String getBatchNo() 
    {
        return batchNo;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setPalletNo(String palletNo) 
    {
        this.palletNo = palletNo;
    }

    public String getPalletNo() 
    {
        return palletNo;
    }
    public void setPalletQty(BigDecimal palletQty) 
    {
        this.palletQty = palletQty;
    }

    public BigDecimal getPalletQty() 
    {
        return palletQty;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("boxId", getBoxId())
            .append("articleId", getArticleId())
            .append("boxNo", getBoxNo())
            .append("batchNo", getBatchNo())
            .append("quantity", getQuantity())
            .append("palletNo", getPalletNo())
            .append("palletQty", getPalletQty())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
