package com.datalink.datamanage.domain;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * ASN行项目对象 tbl_asn_item
 * 
 * <AUTHOR>
 * @date 2021-08-04
 */
public class TblAsnItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ASN行项目ID */
    private Long itemId;

    /** 文档编号 */
    @Excel(name = "文档编号")
    @ApiModelProperty(value = "文档编号")
    private String docNo;

    /** 送货单编号 */
    @Excel(name = "送货单编号")
    @ApiModelProperty(value = "送货单编号")
    @JsonAlias("dnno")
    @NotEmpty(message = "送货单编号不能为空")
    @Size(max = 30, message = "送货单编号超长")
    private String dnNo;

    /** 采购订单编号 */
    @Excel(name = "采购订单编号")
    @JsonAlias("ordercode")
    @ApiModelProperty(value = "采购订单编号")
    @NotEmpty(message = "采购订单编号不能为空")
    @Size(max = 30, message = "采购订单编号超长")
    private String orderCode;

    /** 工厂编号 */
    @Excel(name = "工厂编号")
    @JsonAlias("plantcode")
    @NotEmpty(message = "工厂代码不能为空")
    @ApiModelProperty(value = "工厂代码")
    @Size(max = 40, message = "工厂代码超长")
    private String plantCode;

    /** 工厂名称 */
    @Excel(name = "工厂名称")
    @JsonAlias("plantname")
    @NotEmpty(message = "工厂名称不能为空")
    @ApiModelProperty(value = "工厂名称")
    @Size(max = 40, message = "工厂名称超长")
    private String plantName;

    /** 卸货点编号 */
    @Excel(name = "卸货点编号")
    @JsonAlias("unloadingno")
    @NotEmpty(message = "卸货点编号不能为空")
    @ApiModelProperty(value = "卸货点编号")
    @Size(max = 30, message = "卸货点编号超长")
    private String unloadingNo;

    /** 卸货点名称 */
    @Excel(name = "卸货点名称")
    @JsonAlias("unloadingname")
    @NotEmpty(message = "卸货点名称不能为空")
    @ApiModelProperty(value = "卸货点名称")
    @Size(max = 256, message = "卸货点名称超长")
    private String unloadingName;

    /** 工位器具发出地点编号 */
    @Excel(name = "工位器具发出地点编号")
    @JsonAlias("sendlocno")
    @Size(max = 30, message = "工位器具发出地点编号超长")
    private String sendLocNo;

    /** 工位器具发出地点名称 */
    @Excel(name = "工位器具发出地点名称")
    @JsonAlias("sendlocname")
    @Size(max = 256, message = "工位器具发出地点名称超长")
    private String sendLocName;

    /** 工位器具接收地点编号 */
    @Excel(name = "工位器具接收地点编号")
    @JsonAlias("rcvlocno")
    @Size(max = 30, message = "工位器具接收地点编号超长")
    @ApiModelProperty(value = "交货库位")
    private String rcvLocNo;

    /** 工位器具接收地点名称 */
    @Excel(name = "工位器具接收地点名称")
    @JsonAlias("rcvlocname")
    @Size(max = 256, message = "工位器具接收地点名称超长")
    private String rcvLocName;

    /** ASN ID */
    @Excel(name = "ASN ID")
    private Long asnId;

    /** ASN物料信息 */
    @Size(min=1, message = "物料信息不能为空")
    private List<TblAsnArticle> articles;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setDocNo(String docNo) 
    {
        this.docNo = docNo;
    }

    public String getDocNo() 
    {
        return docNo;
    }
    public void setDnNo(String dnNo) 
    {
        this.dnNo = dnNo;
    }

    public String getDnNo() 
    {
        return dnNo;
    }
    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }
    public void setPlantCode(String plantCode) 
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode() 
    {
        return plantCode;
    }
    public void setPlantName(String plantName) 
    {
        this.plantName = plantName;
    }

    public String getPlantName() 
    {
        return plantName;
    }
    public void setUnloadingNo(String unloadingNo) 
    {
        this.unloadingNo = unloadingNo;
    }

    public String getUnloadingNo() 
    {
        return unloadingNo;
    }
    public void setUnloadingName(String unloadingName) 
    {
        this.unloadingName = unloadingName;
    }

    public String getUnloadingName() 
    {
        return unloadingName;
    }
    public void setSendLocNo(String sendLocNo) 
    {
        this.sendLocNo = sendLocNo;
    }

    public String getSendLocNo() 
    {
        return sendLocNo;
    }
    public void setSendLocName(String sendLocName) 
    {
        this.sendLocName = sendLocName;
    }

    public String getSendLocName() 
    {
        return sendLocName;
    }
    public void setRcvLocNo(String rcvLocNo) 
    {
        this.rcvLocNo = rcvLocNo;
    }

    public String getRcvLocNo() 
    {
        return rcvLocNo;
    }
    public void setRcvLocName(String rcvLocName) 
    {
        this.rcvLocName = rcvLocName;
    }

    public String getRcvLocName() 
    {
        return rcvLocName;
    }
    public void setAsnId(Long asnId) 
    {
        this.asnId = asnId;
    }

    public Long getAsnId() 
    {
        return asnId;
    }

    public List<TblAsnArticle> getArticles()
    {
        return articles;
    }

    public void setArticles(List<TblAsnArticle> articles)
    {
        this.articles = articles;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("docNo", getDocNo())
            .append("dnNo", getDnNo())
            .append("orderCode", getOrderCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("unloadingNo", getUnloadingNo())
            .append("unloadingName", getUnloadingName())
            .append("sendLocNo", getSendLocNo())
            .append("sendLocName", getSendLocName())
            .append("rcvLocNo", getRcvLocNo())
            .append("rcvLocName", getRcvLocName())
            .append("asnId", getAsnId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("tblAsnArticleList", getArticles())
            .toString();
    }
}
