package com.datalink.datamanage.domain;

import com.datalink.common.core.domain.BaseEntity;

import java.math.BigDecimal;

public class TblOrderAsnQuantity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String orderLineNo;
    private String deliveryScheduleNo;
    private BigDecimal quantity;
    private BigDecimal unsentQuantity;
    private String compCode;
    private String orderCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderLineNo() {
        return orderLineNo;
    }

    public void setOrderLineNo(String orderLineNo) {
        this.orderLineNo = orderLineNo;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnsentQuantity() {
        return unsentQuantity;
    }

    public void setUnsentQuantity(BigDecimal unsentQuantity) {
        this.unsentQuantity = unsentQuantity;
    }

    public String getCompCode() {
        return compCode;
    }

    public void setCompCode(String compCode) {
        this.compCode = compCode;
    }

    public String getOrderCode() {
        return orderCode;
    }

    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    public String getDeliveryScheduleNo() {
        return deliveryScheduleNo;
    }

    public void setDeliveryScheduleNo(String deliveryScheduleNo) {
        this.deliveryScheduleNo = deliveryScheduleNo;
    }
}
