package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 预测行项目对象 tbl_forecast_item
 * 
 * <AUTHOR>
 * @date 2021-06-23
 */
//TODO: 校验
public class TblForecastItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 预测行项目ID */
    private Long itemId;

    /** 行号 */
    private String itemNo;

    /** 物料编码 */
    @Excel(name = "物料编码")
    @JsonAlias("articleno")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @JsonAlias("articlename")
    private String articleName;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("deliverydate")
    private Date deliveryDate;

    /** 交货数量 */
    @Excel(name = "交货数量")
    private BigDecimal quantity;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 期间类型 */
    @Excel(name = "期间类型")
    @JsonAlias("durtype")
    private String durType;

    /** 计划类型 */
    @Excel(name = "计划类型")
    @JsonAlias("protype")
    private String proType;

    /** 采购计划协议号 */
    @Excel(name = "采购计划协议号")
    private String poddet;

    /** 预测ID */
    @Excel(name = "预测ID")
    private Long forecastId;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setArticleNo(String articleNo) 
    {
        this.articleNo = articleNo;
    }

    public String getItemNo() {
        return itemNo;
    }

    public void setItemNo(String itemNo) {
        this.itemNo = itemNo;
    }

    public String getArticleNo()
    {
        return articleNo;
    }
    public void setArticleName(String articleName)
    {
        this.articleName = articleName;
    }

    public String getArticleName()
    {
        return articleName;
    }
    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setDurType(String durType) 
    {
        this.durType = durType;
    }

    public String getDurType() 
    {
        return durType;
    }
    public void setProType(String proType) 
    {
        this.proType = proType;
    }

    public String getProType() 
    {
        return proType;
    }
    public void setPoddet(String poddet) 
    {
        this.poddet = poddet;
    }

    public String getPoddet() 
    {
        return poddet;
    }
    public void setForecastId(Long forecastId) 
    {
        this.forecastId = forecastId;
    }

    public Long getForecastId() 
    {
        return forecastId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("itemNo", getItemNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("deliveryDate", getDeliveryDate())
            .append("quantity", getQuantity())
            .append("unit", getUnit())
            .append("durType", getDurType())
            .append("proType", getProType())
            .append("poddet", getPoddet())
            .append("forecastId", getForecastId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
