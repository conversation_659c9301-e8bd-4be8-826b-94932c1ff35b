package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 物料工厂信息对象 tbl_material_plant
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TblMaterialPlant extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 物料工厂ID */
    private Long materialPlantId;

    /** 物料ID */
    private Long materialId;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    private String plantCode;

    /** 包装数量 */
    @Excel(name = "包装数量")
    private BigDecimal packQuantity;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
    
    /** 物料信息 */
    private TblMaterial material;
} 