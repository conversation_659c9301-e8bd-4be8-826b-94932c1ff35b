package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 订单行项目对象 tbl_order_item
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
@JsonNaming()
// TODO:增加校验
public class TblOrderItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行项目ID */
    private Long itemId;

    /** 行号 */
    @Excel(name = "行号")
    @JsonAlias("itemno")
    @NotEmpty(message = "行号不能为空")
    @Size(max = 10, message = "行号超长")
    private String itemNo;

    /** 采购凭证类型 */
    @Excel(name = "采购凭证类型")
    @JsonAlias("purdoctype")
    private String purDocType;

    /** 项目类别 */
    @Excel(name = "项目类别")
    @JsonAlias("itemtype")
    private String itemType;

    /** 采购订单头 */
    @Excel(name = "采购订单头")
    private String text;

    /** 采购凭证中的删除标识 */
    @Excel(name = "采购凭证中的删除标识")
    @JsonAlias("deliden")
    private String delIden;

    /** 采购物料描述 */
    @Excel(name = "采购物料描述")
    @JsonAlias("shorttext")
    private String shortText;

    /** 旧物料号 */
    @Excel(name = "旧物料号")
    @JsonAlias("oldarticleno")
    private String oldArticleNo;

    /** 物料号 */
    @Excel(name = "物料号")
    @JsonAlias("articleno")
    @ApiModelProperty(value = "零件号")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @JsonAlias("articlename")
    @ApiModelProperty(value = "零件名称")
    private String articleName;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("deliverydate")
    @ApiModelProperty(value = "交货日期")
    private Date deliveryDate;

    /** 数量 */
    @Excel(name = "数量")
    @JsonAlias("quantity")
    @ApiModelProperty(value = "交货数量")
    private BigDecimal quantity;

    /** 单位 */
    @Excel(name = "单位")
    @JsonAlias("unit")
    @ApiModelProperty(value = "单位")
    private String unit;

    /** 箱型 */
    @Excel(name = "箱型")
    @JsonAlias("workbinno")
    private String workbinNo;

    /** 箱型描述 */
    @Excel(name = "箱型描述")
    @JsonAlias("workbinname")
    private String workbinName;

    /** 标准包装数量 */
    @Excel(name = "标准包装数量")
    @JsonAlias("qtyperpack")
    private BigDecimal qtyPerPack;

    /** Dock */
    @Excel(name = "Dock")
    @JsonAlias("unloadingno")
    private String unloadingNo;

    /** Dock名称 */
    @Excel(name = "Dock名称")
    @JsonAlias("unloadingname")
    private String unloadingName;

    /** 状态 */
    @Excel(name = "状态")
    private String state;

    /** 采购价格 */
    @Excel(name = "采购价格")
    @JsonAlias("netprice")
    private BigDecimal netPrice;

    /** 价格单位 */
    @Excel(name = "价格单位")
    @JsonAlias("priceunit")
    private String priceUnit;

    /** 采购订单货币的订单净值 */
    @Excel(name = "采购订单货币的订单净值")
    @JsonAlias("ordernetworth")
    private BigDecimal orderNetWorth;

    /** 货币 */
    @Excel(name = "货币")
    @JsonAlias("currencycode")
    private String currencyCode;

    /** 库存地点 */
    @Excel(name = "库存地点")
    @JsonAlias("stockloc")
    @ApiModelProperty(value = "库存地点")
    private String stockLoc;

    /** 库存地点描述 */
    @Excel(name = "库存地点描述")
    @JsonAlias("locdes")
    private String locDes;

    /** 库存地点位置 */
    @Excel(name = "库存地点位置")
    @JsonAlias("locadd")
    private String locAdd;

    /** 收货人姓名 */
    @Excel(name = "收货人姓名")
    @JsonAlias("rcvname")
    private String rcvName;

    /** 收货人电话 */
    @Excel(name = "收货人电话")
    @JsonAlias("rcvtel")
    private String rcvTel;

    /** 检验策略 */
    @Excel(name = "检验策略")
    @JsonAlias("inspestrategy")
    private String inspeStrategy;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    @JsonAlias("zipcode")
    private String zipCode;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 国家代码 */
    @Excel(name = "国家代码")
    @JsonAlias("countrycode")
    private String countryCode;

    /** 地址时区 */
    @Excel(name = "地址时区")
    @JsonAlias("addtimezone")
    private String addTimeZone;

    /** 街道2 */
    @Excel(name = "街道2")
    private String street2;

    /** 街道3 */
    @Excel(name = "街道3")
    private String street3;

    /** 街道4 */
    @Excel(name = "街道4")
    private String street4;

    //客户订单号
    private String customerOrderCode;

    //客户订单行号
    private String customerOrderLineCode;

    //客户交货日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date customerDeliveryDate;

    //生产方式
    private String productType;

    //收货类型
    private String rcvType;

    //采购类型
    private String purchaseType;

    //depot
    private String depot;

    //安保区分 特印
    private String secure;

    //客户物料号
    private String customerArticleNo;

    //部品识别
    private String articleType;

    //分割回数
    private int deliverySplit;

    /** 订单ID */
    private Long orderId;

    /** 交货计划行号 */
    @ApiModelProperty(value = "下达号")
    private String deliveryScheduleNo;

    /** 工厂代码 */
    private String plantCode;

    //订单类型
    private String orderType;

    /** SO订单数量 */
    @Excel(name = "SO订单数量")
    @JsonAlias("soOrderQuantity")
    private BigDecimal soOrderQuantity;

    /** 客户PO号 */
    @Excel(name = "客户PO号")
    @JsonAlias("customerPoNo")
    private String customerPoNo;

    /** Rank NO */
    @Excel(name = "Rank NO")
    @JsonAlias("rankNo")
    private String rankNo;

    /** 箱包数量 */
    @Excel(name = "箱包数量")
    @JsonAlias("boxPackageQuantity")
    private String boxPackageQuantity;

    /** 供应商编码03 */
    @Excel(name = "供应商编码03")
    @JsonAlias("supplierCode03")
    private String supplierCode03;

    /** 供应商编码04 */
    @Excel(name = "供应商编码04")
    @JsonAlias("supplierCode04")
    private String supplierCode04;

    /** PO№&RAN */
    @Excel(name = "PO№&RAN")
    @JsonAlias("poRan")
    private String poRan;

    /** SO创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "SO创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("soCreateDate")
    private Date soCreateDate;

    /** 发货工厂02 */
    @Excel(name = "发货工厂02")
    @JsonAlias("shipmentPlant02")
    private String shipmentPlant02;

    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }
    public void setItemNo(String itemNo)
    {
        this.itemNo = itemNo;
    }

    public String getItemNo()
    {
        return itemNo;
    }
    public void setPurDocType(String purDocType)
    {
        this.purDocType = purDocType;
    }

    public String getPurDocType()
    {
        return purDocType;
    }
    public void setItemType(String itemType)
    {
        this.itemType = itemType;
    }

    public String getItemType()
    {
        return itemType;
    }
    public void setText(String text)
    {
        this.text = text;
    }

    public String getText()
    {
        return text;
    }
    public void setDelIden(String delIden)
    {
        this.delIden = delIden;
    }

    public String getDelIden()
    {
        return delIden;
    }
    public void setShortText(String shortText)
    {
        this.shortText = shortText;
    }

    public String getShortText()
    {
        return shortText;
    }
    public void setOldArticleNo(String oldArticleNo)
    {
        this.oldArticleNo = oldArticleNo;
    }

    public String getOldArticleNo()
    {
        return oldArticleNo;
    }
    public void setArticleNo(String articleNo)
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo()
    {
        return articleNo;
    }
    public void setArticleName(String articleName)
    {
        this.articleName = articleName;
    }

    public String getArticleName()
    {
        return articleName;
    }
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }
    public void setWorkbinNo(String workbinNo)
    {
        this.workbinNo = workbinNo;
    }

    public String getWorkbinNo()
    {
        return workbinNo;
    }
    public void setWorkbinName(String workbinName)
    {
        this.workbinName = workbinName;
    }

    public String getWorkbinName()
    {
        return workbinName;
    }
    public void setQtyPerPack(BigDecimal qtyPerPack)
    {
        this.qtyPerPack = qtyPerPack;
    }

    public BigDecimal getQtyPerPack()
    {
        return qtyPerPack;
    }
    public void setUnloadingNo(String unloadingNo)
    {
        this.unloadingNo = unloadingNo;
    }

    public String getUnloadingNo()
    {
        return unloadingNo;
    }
    public void setUnloadingName(String unloadingName)
    {
        this.unloadingName = unloadingName;
    }

    public String getUnloadingName()
    {
        return unloadingName;
    }
    public void setState(String state)
    {
        this.state = state;
    }

    public String getState()
    {
        return state;
    }
    public void setNetPrice(BigDecimal netPrice)
    {
        this.netPrice = netPrice;
    }

    public BigDecimal getNetPrice()
    {
        return netPrice;
    }
    public void setPriceUnit(String priceUnit)
    {
        this.priceUnit = priceUnit;
    }

    public String getPriceUnit()
    {
        return priceUnit;
    }
    public void setOrderNetWorth(BigDecimal orderNetWorth)
    {
        this.orderNetWorth = orderNetWorth;
    }

    public BigDecimal getOrderNetWorth()
    {
        return orderNetWorth;
    }
    public void setCurrencyCode(String currencyCode)
    {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyCode()
    {
        return currencyCode;
    }
    public void setStockLoc(String stockLoc)
    {
        this.stockLoc = stockLoc;
    }

    public String getStockLoc()
    {
        return stockLoc;
    }
    public void setLocDes(String locDes)
    {
        this.locDes = locDes;
    }

    public String getLocDes()
    {
        return locDes;
    }
    public void setLocAdd(String locAdd)
    {
        this.locAdd = locAdd;
    }

    public String getLocAdd()
    {
        return locAdd;
    }
    public void setRcvName(String rcvName)
    {
        this.rcvName = rcvName;
    }

    public String getRcvName()
    {
        return rcvName;
    }
    public void setRcvTel(String rcvTel)
    {
        this.rcvTel = rcvTel;
    }

    public String getRcvTel()
    {
        return rcvTel;
    }
    public void setInspeStrategy(String inspeStrategy)
    {
        this.inspeStrategy = inspeStrategy;
    }

    public String getInspeStrategy()
    {
        return inspeStrategy;
    }
    public void setZipCode(String zipCode)
    {
        this.zipCode = zipCode;
    }

    public String getZipCode()
    {
        return zipCode;
    }
    public void setCity(String city)
    {
        this.city = city;
    }

    public String getCity()
    {
        return city;
    }
    public void setCountryCode(String countryCode)
    {
        this.countryCode = countryCode;
    }

    public String getCountryCode()
    {
        return countryCode;
    }
    public void setAddTimeZone(String addTimeZone)
    {
        this.addTimeZone = addTimeZone;
    }

    public String getAddTimeZone()
    {
        return addTimeZone;
    }
    public void setStreet2(String street2)
    {
        this.street2 = street2;
    }

    public String getStreet2()
    {
        return street2;
    }
    public void setStreet3(String street3)
    {
        this.street3 = street3;
    }

    public String getStreet3()
    {
        return street3;
    }
    public void setStreet4(String street4)
    {
        this.street4 = street4;
    }

    public String getStreet4()
    {
        return street4;
    }
    public void setOrderId(Long orderId)
    {
        this.orderId = orderId;
    }

    public Long getOrderId()
    {
        return orderId;
    }

    public String getCustomerOrderCode() {
        return customerOrderCode;
    }

    public void setCustomerOrderCode(String customerOrderCode) {
        this.customerOrderCode = customerOrderCode;
    }

    public String getCustomerOrderLineCode() {
        return customerOrderLineCode;
    }

    public void setCustomerOrderLineCode(String customerOrderLineCode) {
        this.customerOrderLineCode = customerOrderLineCode;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getRcvType() {
        return rcvType;
    }

    public void setRcvType(String rcvType) {
        this.rcvType = rcvType;
    }

    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getDepot() {
        return depot;
    }

    public void setDepot(String depot) {
        this.depot = depot;
    }

    public Date getCustomerDeliveryDate() {
        return customerDeliveryDate;
    }

    public void setCustomerDeliveryDate(Date customerDeliveryDate) {
        this.customerDeliveryDate = customerDeliveryDate;
    }

    public String getSecure() {
        return secure;
    }

    public void setSecure(String secure) {
        this.secure = secure;
    }

    public String getCustomerArticleNo() {
        return customerArticleNo;
    }

    public void setCustomerArticleNo(String customerArticleNo) {
        this.customerArticleNo = customerArticleNo;
    }

    public String getArticleType() {
        return articleType;
    }

    public void setArticleType(String articleType) {
        this.articleType = articleType;
    }

    public int getDeliverySplit() {
        return deliverySplit;
    }

    public void setDeliverySplit(int deliverySplit) {
        this.deliverySplit = deliverySplit;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getSoOrderQuantity() {
        return soOrderQuantity;
    }

    public void setSoOrderQuantity(BigDecimal soOrderQuantity) {
        this.soOrderQuantity = soOrderQuantity;
    }

    public String getCustomerPoNo() {
        return customerPoNo;
    }

    public void setCustomerPoNo(String customerPoNo) {
        this.customerPoNo = customerPoNo;
    }

    public String getRankNo() {
        return rankNo;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public String getBoxPackageQuantity() {
        return boxPackageQuantity;
    }

    public void setBoxPackageQuantity(String boxPackageQuantity) {
        this.boxPackageQuantity = boxPackageQuantity;
    }

    public String getSupplierCode03() {
        return supplierCode03;
    }

    public void setSupplierCode03(String supplierCode03) {
        this.supplierCode03 = supplierCode03;
    }

    public String getSupplierCode04() {
        return supplierCode04;
    }

    public void setSupplierCode04(String supplierCode04) {
        this.supplierCode04 = supplierCode04;
    }

    public String getPoRan() {
        return poRan;
    }

    public void setPoRan(String poRan) {
        this.poRan = poRan;
    }

    public Date getSoCreateDate() {
        return soCreateDate;
    }

    public void setSoCreateDate(Date soCreateDate) {
        this.soCreateDate = soCreateDate;
    }

    public String getShipmentPlant02() {
        return shipmentPlant02;
    }

    public void setShipmentPlant02(String shipmentPlant02) {
        this.shipmentPlant02 = shipmentPlant02;
    }

    public String getDeliveryScheduleNo() {
        return deliveryScheduleNo;
    }

    public void setDeliveryScheduleNo(String deliveryScheduleNo) {
        this.deliveryScheduleNo = deliveryScheduleNo;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("itemNo", getItemNo())
            .append("purDocType", getPurDocType())
            .append("itemType", getItemType())
            .append("text", getText())
            .append("delIden", getDelIden())
            .append("shortText", getShortText())
            .append("oldArticleNo", getOldArticleNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("deliveryDate", getDeliveryDate())
            .append("quantity", getQuantity())
            .append("unit", getUnit())
            .append("workbinNo", getWorkbinNo())
            .append("workbinName", getWorkbinName())
            .append("qtyPerPack", getQtyPerPack())
            .append("unloadingNo", getUnloadingNo())
            .append("unloadingName", getUnloadingName())
            .append("state", getState())
            .append("netPrice", getNetPrice())
            .append("priceUnit", getPriceUnit())
            .append("orderNetWorth", getOrderNetWorth())
            .append("currencyCode", getCurrencyCode())
            .append("stockLoc", getStockLoc())
            .append("locDes", getLocDes())
            .append("locAdd", getLocAdd())
            .append("rcvName", getRcvName())
            .append("rcvTel", getRcvTel())
            .append("inspeStrategy", getInspeStrategy())
            .append("zipCode", getZipCode())
            .append("city", getCity())
            .append("countryCode", getCountryCode())
            .append("addTimeZone", getAddTimeZone())
            .append("street2", getStreet2())
            .append("street3", getStreet3())
            .append("street4", getStreet4())
            .append("orderId", getOrderId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("soOrderQuantity", getSoOrderQuantity())
            .append("customerPoNo", getCustomerPoNo())
            .append("rankNo", getRankNo())
            .append("boxPackageQuantity", getBoxPackageQuantity())
            .append("supplierCode03", getSupplierCode03())
            .append("supplierCode04", getSupplierCode04())
            .append("poRan", getPoRan())
            .append("soCreateDate", getSoCreateDate())
            .append("shipmentPlant02", getShipmentPlant02())
            .toString();
    }
}
