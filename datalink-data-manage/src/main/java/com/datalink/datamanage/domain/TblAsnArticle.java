package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ASN物料对象 tbl_asn_article
 * 
 * <AUTHOR>
 * @date 2021-08-04
 */
public class TblAsnArticle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ASN物料ID */
    private Long articleId;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docNo;

    /** 送货单编号 */
//    @Excel(name = "送货单编号")
//    private String dnNo;

    /** 物料编号 */
    @Excel(name = "物料编号")
    @JsonAlias("articleno")
    @NotEmpty(message = "物料编码不能为空")
    @ApiModelProperty(value = "物料编码")
    @Size(max = 30, message = "物料编码超长")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @JsonAlias("articlename")
    @ApiModelProperty(value = "物料名称")
    @Size(max = 256, message = "物料名称超长")
    private String articleName;

    /** 交货数量 */
    @Excel(name = "交货数量")
    @NotNull(message = "交货数量不能为空")
    @ApiModelProperty(value = "交货数量")
    private BigDecimal quantity;

    /** 单位 */
    @Excel(name = "单位")
    @Size(max = 30, message = "单位超长")
    @ApiModelProperty(value = "单位")
    private String unit;

    /** 批次号 */
    @Excel(name = "批次号")
    @JsonAlias("batchno")
    @Size(max = 40, message = "批次号超长")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 订单行号 */
    @Excel(name = "订单行号")
    @JsonAlias("orderlineno")
    @Size(max = 10, message = "订单行号超长")
    @ApiModelProperty(value = "订单行号")
    private String orderLineNo;

    /** 每箱数量 */
    @Excel(name = "每箱数量")
    @JsonAlias("qtyperpack")
    @NotNull(message = "每箱数量不能为空")
    @ApiModelProperty(value = "每箱数量")
    private BigDecimal qtyPerPack;

    /** 箱数 */
    @Excel(name = "箱数")
    @JsonAlias("packqty")
    @NotNull(message = "箱数不能为空")
    @ApiModelProperty(value = "箱数")
    private BigDecimal packQty;

    /** 非标 */
    @Excel(name = "非标")
    @JsonAlias("nonstd")
    @NotNull(message = "非标不能为空")
    @Size(max = 10, message = "非标超长")
    private String nonStd;

    /** 条码开始 */
    @Excel(name = "条码开始")
    @JsonAlias("startwith")
    @NotNull(message = "条码开始不能为空")
    @Size(max = 40, message = "条码开始超长")
    private String startWith;

    /** 条码结束 */
    @Excel(name = "条码结束")
    @JsonAlias("endwith")
    @Size(max = 40, message = "条码结束超长")
    private String endWith;

    /** Asn行项目ID */
    @Excel(name = "Asn行项目ID")
    private Long itemId;

    private String deliveryScheduleNo;

    /** 箱信息列表 */
    private List<TblAsnBox> boxes;

    private List<Pallet> pallet;

    @Setter
    @Getter
    public static class Pallet {
        private String label;
        private List<Box> box;

    }

    @Setter
    @Getter
    public static class Box {
        private String label;
        private BigDecimal qty;
        private String batchNo;
    }

    public List<Pallet> getPallet() {
        return pallet;
    }

    public void setPallet(List<Pallet> pallet) {
        this.pallet = pallet;
    }

    public List<TblAsnBox> getBoxes() {
        return boxes;
    }

    public void setBoxes(List<TblAsnBox> boxes) {
        this.boxes = boxes;
    }

    public void setArticleId(Long articleId)
    {
        this.articleId = articleId;
    }

    public Long getArticleId() 
    {
        return articleId;
    }
    public void setDocNo(String docNo) 
    {
        this.docNo = docNo;
    }

    public String getDocNo() 
    {
        return docNo;
    }
//    public void setDnNo(String dnNo)
//    {
//        this.dnNo = dnNo;
//    }
//
//    public String getDnNo()
//    {
//        return dnNo;
//    }
    public void setArticleNo(String articleNo) 
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo() 
    {
        return articleNo;
    }
    public void setArticleName(String articleName) 
    {
        this.articleName = articleName;
    }

    public String getArticleName() 
    {
        return articleName;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setBatchNo(String batchNo) 
    {
        this.batchNo = batchNo;
    }

    public String getBatchNo() 
    {
        return batchNo;
    }
    public void setOrderLineNo(String orderLineNo) 
    {
        this.orderLineNo = orderLineNo;
    }

    public String getOrderLineNo() 
    {
        return orderLineNo;
    }
    public void setQtyPerPack(BigDecimal qtyPerPack) 
    {
        this.qtyPerPack = qtyPerPack;
    }

    public BigDecimal getQtyPerPack() 
    {
        return qtyPerPack;
    }
    public void setPackQty(BigDecimal packQty) 
    {
        this.packQty = packQty;
    }

    public BigDecimal getPackQty() 
    {
        return packQty;
    }
    public void setNonStd(String nonStd) 
    {
        this.nonStd = nonStd;
    }

    public String getNonStd() 
    {
        return nonStd;
    }
    public void setStartWith(String startWith) 
    {
        this.startWith = startWith;
    }

    public String getStartWith() 
    {
        return startWith;
    }
    public void setEndWith(String endWith) 
    {
        this.endWith = endWith;
    }

    public String getEndWith() 
    {
        return endWith;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }

    public String getDeliveryScheduleNo() {
        return deliveryScheduleNo;
    }

    public void setDeliveryScheduleNo(String deliveryScheduleNo) {
        this.deliveryScheduleNo = deliveryScheduleNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("articleId", getArticleId())
            .append("docNo", getDocNo())
//            .append("dnNo", getDnNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("quantity", getQuantity())
            .append("unit", getUnit())
            .append("batchNo", getBatchNo())
            .append("orderLineNo", getOrderLineNo())
            .append("qtyPerPack", getQtyPerPack())
            .append("packQty", getPackQty())
            .append("nonStd", getNonStd())
            .append("startWith", getStartWith())
            .append("endWith", getEndWith())
            .append("itemId", getItemId())
            .append("deliveryScheduleNo", getDeliveryScheduleNo())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
