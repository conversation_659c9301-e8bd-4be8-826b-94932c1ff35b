package com.datalink.datamanage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 退货箱表对象 tbl_return_box
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@ApiModel("退货箱表")
public class TblReturnBox extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 箱子ID */
    private Long boxId;

    /** 退货ID */
    @Excel(name = "退货ID")
    @ApiModelProperty(value = "退货ID")
    private Long returnId;

    /** ASN编号 */
    @Excel(name = "ASN编号")
    @ApiModelProperty(value = "ASN编号")
    private String asnCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    /** 行号 */
    @Excel(name = "行号")
    @ApiModelProperty(value = "行号")
    private String itemNo;

    /** 下达号 */
    @Excel(name = "下达号")
    @ApiModelProperty(value = "下达号")
    private String releaseNo;

    /** 原批次号 */
    @Excel(name = "原批次号")
    @ApiModelProperty(value = "原批次号")
    private String originalBatchNo;

    /** 新批次号 */
    @Excel(name = "新批次号")
    @ApiModelProperty(value = "新批次号")
    private String newBatchNo;

    /** 原标签 */
    @Excel(name = "原标签")
    @ApiModelProperty(value = "原标签")
    private String originalLabel;

    /** 新标签 */
    @Excel(name = "新标签")
    @ApiModelProperty(value = "新标签")
    private String newLabel;

    /** 箱子序号 */
    @Excel(name = "箱子序号")
    @ApiModelProperty(value = "箱子序号")
    private Integer boxIndex;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Double qty;

    /** 状态：New-新建，Modified-已修改，Completed-已完成 */
    @Excel(name = "状态", readConverterExp = "New=新建,Modified=已修改,Completed=已完成")
    @ApiModelProperty(value = "状态")
    private String status;

    public void setBoxId(Long boxId) 
    {
        this.boxId = boxId;
    }

    public Long getBoxId() 
    {
        return boxId;
    }
    public void setReturnId(Long returnId)
    {
        this.returnId = returnId;
    }

    public Long getReturnId()
    {
        return returnId;
    }
    public void setAsnCode(String asnCode) 
    {
        this.asnCode = asnCode;
    }

    public String getAsnCode() 
    {
        return asnCode;
    }
    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }
    public void setItemNo(String itemNo) 
    {
        this.itemNo = itemNo;
    }

    public String getItemNo() 
    {
        return itemNo;
    }
    public void setReleaseNo(String releaseNo) 
    {
        this.releaseNo = releaseNo;
    }

    public String getReleaseNo() 
    {
        return releaseNo;
    }
    public void setOriginalBatchNo(String originalBatchNo) 
    {
        this.originalBatchNo = originalBatchNo;
    }

    public String getOriginalBatchNo() 
    {
        return originalBatchNo;
    }
    public void setNewBatchNo(String newBatchNo) 
    {
        this.newBatchNo = newBatchNo;
    }

    public String getNewBatchNo() 
    {
        return newBatchNo;
    }
    public void setOriginalLabel(String originalLabel) 
    {
        this.originalLabel = originalLabel;
    }

    public String getOriginalLabel() 
    {
        return originalLabel;
    }
    public void setNewLabel(String newLabel) 
    {
        this.newLabel = newLabel;
    }

    public String getNewLabel() 
    {
        return newLabel;
    }
    public void setBoxIndex(Integer boxIndex) 
    {
        this.boxIndex = boxIndex;
    }

    public Integer getBoxIndex() 
    {
        return boxIndex;
    }
    public void setQty(Double qty)
    {
        this.qty = qty;
    }

    public Double getQty()
    {
        return qty;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("boxId", getBoxId())
            .append("returnId", getReturnId())
            .append("asnCode", getAsnCode())
            .append("orderCode", getOrderCode())
            .append("itemNo", getItemNo())
            .append("releaseNo", getReleaseNo())
            .append("originalBatchNo", getOriginalBatchNo())
            .append("newBatchNo", getNewBatchNo())
            .append("originalLabel", getOriginalLabel())
            .append("newLabel", getNewLabel())
            .append("boxIndex", getBoxIndex())
            .append("qty", getQty())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
