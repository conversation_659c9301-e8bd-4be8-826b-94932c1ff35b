package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * flatOrder对象 view_order_with_item
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
public class FlatOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String compCode;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    private String plantCode;

    /** 工厂名称 */
    @Excel(name = "工厂名称")
    private String plantName;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String suppName;

    /** 采购组 */
    @Excel(name = "采购组")
    private String plannerNo;

    /** 采购组描述 */
    @Excel(name = "采购组描述")
    private String plannerName;

    /** 时间窗开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间窗开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date timeBegin;

    /** 时间窗结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间窗结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date timeEnd;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    /** 是否已阅 */
    @Excel(name = "是否已阅")
    private String isRead;

    /** 是否完成 */
    @Excel(name = "是否完成")
    private String isComplete;

    /** 收发标志 */
    @Excel(name = "收发标志")
    private String direction;

    /** KafKa发送状态 */
    @Excel(name = "KafKa发送状态")
    private String kafkaStatus;

    /** 客户代码 */
    @Excel(name = "客户代码")
    private String customerCode;

    /** 送达方 要元 */
    @Excel(name = "送达方 要元")
    private String requester;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String compName;

    /** 行项目ID */
    @Excel(name = "行项目ID")
    private Long itemId;

    /** 行号 */
    @Excel(name = "行号")
    @ApiModelProperty(value = "行号")
    private String itemNo;

    /** 采购凭证类型 */
    @Excel(name = "采购凭证类型")
    private String purDocType;

    /** 项目类别 */
    @Excel(name = "项目类别")
    private String itemType;

    /** 采购订单头 */
    @Excel(name = "采购订单头")
    private String text;

    /** 采购凭证中的删除标识 */
    @Excel(name = "采购凭证中的删除标识")
    private String delIden;

    /** 采购物料描述 */
    @Excel(name = "采购物料描述")
    private String shortText;

    /** 旧物料号 */
    @Excel(name = "旧物料号")
    private String oldArticleNo;

    /** 物料号 */
    @Excel(name = "物料号")
    @ApiModelProperty(value = "零件号")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @ApiModelProperty(value = "零件描述")
    private String articleName;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "UTC")
    @Excel(name = "交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "交货日期")
    private String deliveryDate;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "订单数量")
    private BigDecimal quantity;

    /** 单位 */
    @Excel(name = "单位")
    @ApiModelProperty(value = "单位")
    private String unit;

    /** 箱型 */
    @Excel(name = "箱型")
    private String workbinNo;

    /** 箱型描述 */
    @Excel(name = "箱型描述")
    private String workbinName;

    /** 标准包装数量 */
    @Excel(name = "标准包装数量")
    private BigDecimal qtyPerPack;

    /** Dock */
    @Excel(name = "Dock")
    private String unloadingNo;

    /** Dock名称 */
    @Excel(name = "Dock名称")
    private String unloadingName;

    /** 状态 */
    @Excel(name = "状态")
    private String state;

    /** 采购价格 */
    @Excel(name = "采购价格")
    private BigDecimal netPrice;

    /** 价格单位 */
    @Excel(name = "价格单位")
    private String priceUnit;

    /** 采购订单货币的订单净值 */
    @Excel(name = "采购订单货币的订单净值")
    private BigDecimal orderNetWorth;

    /** 货币 */
    @Excel(name = "货币")
    private String currencyCode;

    /** 库存地点 */
    @Excel(name = "库存地点")
    private String stockLoc;

    /** 库存地点描述 */
    @Excel(name = "库存地点描述")
    private String locDes;

    /** 库存地点位置 */
    @Excel(name = "库存地点位置")
    private String locAdd;

    /** 收货人姓名 */
    @Excel(name = "收货人姓名")
    private String rcvName;

    /** 收货人电话 */
    @Excel(name = "收货人电话")
    private String rcvTel;

    /** 检验策略 */
    @Excel(name = "检验策略")
    private String inspeStrategy;

    /** 邮政编码 */
    @Excel(name = "邮政编码")
    private String zipCode;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 国家代码 */
    @Excel(name = "国家代码")
    private String countryCode;

    /** 地址时区 */
    @Excel(name = "地址时区")
    private String addTimeZone;

    /** 街道2 */
    @Excel(name = "街道2")
    private String street2;

    /** 街道3 */
    @Excel(name = "街道3")
    private String street3;

    /** 街道4 */
    @Excel(name = "街道4")
    private String street4;

    /** 客户订单编号 */
    @Excel(name = "客户订单编号")
    private String customerOrderCode;

    /** 客户订单行号 */
    @Excel(name = "客户订单行号")
    private String customerOrderLineCode;

    /** 客户交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "客户交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date customerDeliveryDate;

    /** 生产方式 */
    @Excel(name = "生产方式")
    private String productType;

    /** 收货方式 */
    @Excel(name = "收货方式")
    private String rcvType;

    /** 采购方式 */
    @Excel(name = "采购方式")
    private String purchaseType;

    /** Depot */
    @Excel(name = "Depot")
    private String depot;

    /** 客户物料号 */
    @Excel(name = "客户物料号")
    private String customerArticleNo;

    /** 重要安保区分 */
    @Excel(name = "重要安保区分")
    private String secure;

    /** 部品番号识别 */
    @Excel(name = "部品番号识别")
    private String articleType;

    private String is223XUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sapUpdateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发送时间")
    private Date receiveTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "初次发送时间")
    private Date firstReceiveTime;

    /** 订单状态 */
    @ApiModelProperty(value = "订单状态")
    private String status;

    /** 下载状态 */
    @Excel(name = "下载状态")
    @ApiModelProperty(value = "下载状态")
    private String downloadStatus;

    /** 最近下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最近下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近下载时间")
    private Date lastDownloadTime;

    @ApiModelProperty(value = "下达号")
    private String deliveryScheduleNo;

    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    public void setCompCode(String compCode) 
    {
        this.compCode = compCode;
    }

    public String getCompCode() 
    {
        return compCode;
    }
    public void setPlantCode(String plantCode) 
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode() 
    {
        return plantCode;
    }
    public void setPlantName(String plantName) 
    {
        this.plantName = plantName;
    }

    public String getPlantName() 
    {
        return plantName;
    }
    public void setSuppCode(String suppCode) 
    {
        this.suppCode = suppCode;
    }

    public String getSuppCode() 
    {
        return suppCode;
    }
    public void setSuppName(String suppName) 
    {
        this.suppName = suppName;
    }

    public String getSuppName() 
    {
        return suppName;
    }
    public void setPlannerNo(String plannerNo) 
    {
        this.plannerNo = plannerNo;
    }

    public String getPlannerNo() 
    {
        return plannerNo;
    }
    public void setPlannerName(String plannerName) 
    {
        this.plannerName = plannerName;
    }

    public String getPlannerName() 
    {
        return plannerName;
    }
    public void setTimeBegin(Date timeBegin) 
    {
        this.timeBegin = timeBegin;
    }

    public Date getTimeBegin() 
    {
        return timeBegin;
    }
    public void setTimeEnd(Date timeEnd) 
    {
        this.timeEnd = timeEnd;
    }

    public Date getTimeEnd() 
    {
        return timeEnd;
    }
    public void setOrderCode(String orderCode) 
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode() 
    {
        return orderCode;
    }
    public void setIsRead(String isRead) 
    {
        this.isRead = isRead;
    }

    public String getIsRead() 
    {
        return isRead;
    }
    public void setIsComplete(String isComplete) 
    {
        this.isComplete = isComplete;
    }

    public String getIsComplete() 
    {
        return isComplete;
    }
    public void setDirection(String direction) 
    {
        this.direction = direction;
    }

    public String getDirection() 
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus) 
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus() 
    {
        return kafkaStatus;
    }
    public void setCustomerCode(String customerCode) 
    {
        this.customerCode = customerCode;
    }

    public String getCustomerCode() 
    {
        return customerCode;
    }
    public void setRequester(String requester) 
    {
        this.requester = requester;
    }

    public String getRequester() 
    {
        return requester;
    }
    public void setCompName(String compName) 
    {
        this.compName = compName;
    }

    public String getCompName() 
    {
        return compName;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setItemNo(String itemNo) 
    {
        this.itemNo = itemNo;
    }

    public String getItemNo() 
    {
        return itemNo;
    }
    public void setPurDocType(String purDocType) 
    {
        this.purDocType = purDocType;
    }

    public String getPurDocType() 
    {
        return purDocType;
    }
    public void setItemType(String itemType) 
    {
        this.itemType = itemType;
    }

    public String getItemType() 
    {
        return itemType;
    }
    public void setText(String text) 
    {
        this.text = text;
    }

    public String getText() 
    {
        return text;
    }
    public void setDelIden(String delIden) 
    {
        this.delIden = delIden;
    }

    public String getDelIden() 
    {
        return delIden;
    }
    public void setShortText(String shortText) 
    {
        this.shortText = shortText;
    }

    public String getShortText() 
    {
        return shortText;
    }
    public void setOldArticleNo(String oldArticleNo) 
    {
        this.oldArticleNo = oldArticleNo;
    }

    public String getOldArticleNo() 
    {
        return oldArticleNo;
    }
    public void setArticleNo(String articleNo) 
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo() 
    {
        return articleNo;
    }
    public void setArticleName(String articleName) 
    {
        this.articleName = articleName;
    }

    public String getArticleName() 
    {
        return articleName;
    }
    public void setDeliveryDate(String deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public String getDeliveryDate()
    {
        return deliveryDate;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setWorkbinNo(String workbinNo) 
    {
        this.workbinNo = workbinNo;
    }

    public String getWorkbinNo() 
    {
        return workbinNo;
    }
    public void setWorkbinName(String workbinName) 
    {
        this.workbinName = workbinName;
    }

    public String getWorkbinName() 
    {
        return workbinName;
    }
    public void setQtyPerPack(BigDecimal qtyPerPack) 
    {
        this.qtyPerPack = qtyPerPack;
    }

    public BigDecimal getQtyPerPack() 
    {
        return qtyPerPack;
    }
    public void setUnloadingNo(String unloadingNo) 
    {
        this.unloadingNo = unloadingNo;
    }

    public String getUnloadingNo() 
    {
        return unloadingNo;
    }
    public void setUnloadingName(String unloadingName) 
    {
        this.unloadingName = unloadingName;
    }

    public String getUnloadingName() 
    {
        return unloadingName;
    }
    public void setState(String state) 
    {
        this.state = state;
    }

    public String getState() 
    {
        return state;
    }
    public void setNetPrice(BigDecimal netPrice) 
    {
        this.netPrice = netPrice;
    }

    public BigDecimal getNetPrice() 
    {
        return netPrice;
    }
    public void setPriceUnit(String priceUnit) 
    {
        this.priceUnit = priceUnit;
    }

    public String getPriceUnit() 
    {
        return priceUnit;
    }
    public void setOrderNetWorth(BigDecimal orderNetWorth) 
    {
        this.orderNetWorth = orderNetWorth;
    }

    public BigDecimal getOrderNetWorth() 
    {
        return orderNetWorth;
    }
    public void setCurrencyCode(String currencyCode) 
    {
        this.currencyCode = currencyCode;
    }

    public String getCurrencyCode() 
    {
        return currencyCode;
    }
    public void setStockLoc(String stockLoc) 
    {
        this.stockLoc = stockLoc;
    }

    public String getStockLoc() 
    {
        return stockLoc;
    }
    public void setLocDes(String locDes) 
    {
        this.locDes = locDes;
    }

    public String getLocDes() 
    {
        return locDes;
    }
    public void setLocAdd(String locAdd) 
    {
        this.locAdd = locAdd;
    }

    public String getLocAdd() 
    {
        return locAdd;
    }
    public void setRcvName(String rcvName) 
    {
        this.rcvName = rcvName;
    }

    public String getRcvName() 
    {
        return rcvName;
    }
    public void setRcvTel(String rcvTel) 
    {
        this.rcvTel = rcvTel;
    }

    public String getRcvTel() 
    {
        return rcvTel;
    }
    public void setInspeStrategy(String inspeStrategy) 
    {
        this.inspeStrategy = inspeStrategy;
    }

    public String getInspeStrategy() 
    {
        return inspeStrategy;
    }
    public void setZipCode(String zipCode) 
    {
        this.zipCode = zipCode;
    }

    public String getZipCode() 
    {
        return zipCode;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setCountryCode(String countryCode) 
    {
        this.countryCode = countryCode;
    }

    public String getCountryCode() 
    {
        return countryCode;
    }
    public void setAddTimeZone(String addTimeZone) 
    {
        this.addTimeZone = addTimeZone;
    }

    public String getAddTimeZone() 
    {
        return addTimeZone;
    }
    public void setStreet2(String street2) 
    {
        this.street2 = street2;
    }

    public String getStreet2() 
    {
        return street2;
    }
    public void setStreet3(String street3) 
    {
        this.street3 = street3;
    }

    public String getStreet3() 
    {
        return street3;
    }
    public void setStreet4(String street4) 
    {
        this.street4 = street4;
    }

    public String getStreet4() 
    {
        return street4;
    }
    public void setCustomerOrderCode(String customerOrderCode) 
    {
        this.customerOrderCode = customerOrderCode;
    }

    public String getCustomerOrderCode() 
    {
        return customerOrderCode;
    }
    public void setCustomerOrderLineCode(String customerOrderLineCode) 
    {
        this.customerOrderLineCode = customerOrderLineCode;
    }

    public String getCustomerOrderLineCode() 
    {
        return customerOrderLineCode;
    }
    public void setCustomerDeliveryDate(Date customerDeliveryDate) 
    {
        this.customerDeliveryDate = customerDeliveryDate;
    }

    public Date getCustomerDeliveryDate() 
    {
        return customerDeliveryDate;
    }
    public void setProductType(String productType) 
    {
        this.productType = productType;
    }

    public String getProductType() 
    {
        return productType;
    }
    public void setRcvType(String rcvType) 
    {
        this.rcvType = rcvType;
    }

    public String getRcvType() 
    {
        return rcvType;
    }
    public void setPurchaseType(String purchaseType) 
    {
        this.purchaseType = purchaseType;
    }

    public String getPurchaseType() 
    {
        return purchaseType;
    }
    public void setDepot(String depot) 
    {
        this.depot = depot;
    }

    public String getDepot() 
    {
        return depot;
    }
    public void setCustomerArticleNo(String customerArticleNo) 
    {
        this.customerArticleNo = customerArticleNo;
    }

    public String getCustomerArticleNo() 
    {
        return customerArticleNo;
    }
    public void setSecure(String secure) 
    {
        this.secure = secure;
    }

    public String getSecure() 
    {
        return secure;
    }
    public void setArticleType(String articleType) 
    {
        this.articleType = articleType;
    }

    public String getArticleType() 
    {
        return articleType;
    }

    public String getIs223XUser() {
        return is223XUser;
    }

    public void setIs223XUser(String is223XUser) {
        this.is223XUser = is223XUser;
    }

    public Date getSapUpdateTime() {
        return sapUpdateTime;
    }

    public void setSapUpdateTime(Date sapUpdateTime) {
        this.sapUpdateTime = sapUpdateTime;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(String downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public Date getLastDownloadTime() {
        return lastDownloadTime;
    }

    public void setLastDownloadTime(Date lastDownloadTime) {
        this.lastDownloadTime = lastDownloadTime;
    }

    public String getDeliveryScheduleNo() {
        return deliveryScheduleNo;
    }

    public void setDeliveryScheduleNo(String deliveryScheduleNo) {
        this.deliveryScheduleNo = deliveryScheduleNo;
    }

    public Date getFirstReceiveTime() {
        return firstReceiveTime;
    }

    public void setFirstReceiveTime(Date firstReceiveTime) {
        this.firstReceiveTime = firstReceiveTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("plannerNo", getPlannerNo())
            .append("plannerName", getPlannerName())
            .append("timeBegin", getTimeBegin())
            .append("timeEnd", getTimeEnd())
            .append("orderCode", getOrderCode())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("isRead", getIsRead())
            .append("isComplete", getIsComplete())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("customerCode", getCustomerCode())
            .append("requester", getRequester())
            .append("compName", getCompName())
            .append("itemId", getItemId())
            .append("itemNo", getItemNo())
            .append("purDocType", getPurDocType())
            .append("itemType", getItemType())
            .append("text", getText())
            .append("delIden", getDelIden())
            .append("shortText", getShortText())
            .append("oldArticleNo", getOldArticleNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("deliveryDate", getDeliveryDate())
            .append("quantity", getQuantity())
            .append("unit", getUnit())
            .append("workbinNo", getWorkbinNo())
            .append("workbinName", getWorkbinName())
            .append("qtyPerPack", getQtyPerPack())
            .append("unloadingNo", getUnloadingNo())
            .append("unloadingName", getUnloadingName())
            .append("state", getState())
            .append("netPrice", getNetPrice())
            .append("priceUnit", getPriceUnit())
            .append("orderNetWorth", getOrderNetWorth())
            .append("currencyCode", getCurrencyCode())
            .append("stockLoc", getStockLoc())
            .append("locDes", getLocDes())
            .append("locAdd", getLocAdd())
            .append("rcvName", getRcvName())
            .append("rcvTel", getRcvTel())
            .append("inspeStrategy", getInspeStrategy())
            .append("zipCode", getZipCode())
            .append("city", getCity())
            .append("countryCode", getCountryCode())
            .append("addTimeZone", getAddTimeZone())
            .append("street2", getStreet2())
            .append("street3", getStreet3())
            .append("street4", getStreet4())
            .append("customerOrderCode", getCustomerOrderCode())
            .append("customerOrderLineCode", getCustomerOrderLineCode())
            .append("customerDeliveryDate", getCustomerDeliveryDate())
            .append("productType", getProductType())
            .append("rcvType", getRcvType())
            .append("purchaseType", getPurchaseType())
            .append("depot", getDepot())
            .append("customerArticleNo", getCustomerArticleNo())
            .append("secure", getSecure())
            .append("articleType", getArticleType())
            .append("is223XUser", getIs223XUser())
            .append("sapUpdateTime", getSapUpdateTime())
            .append("receiveTime", getReceiveTime())
            .toString();
    }
}
