package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

public class BaseHeadEntity extends BaseEntity {
    /** 公司代码 */
    @Excel(name = "公司代码")
    @JsonAlias("compcode")
    @ApiModelProperty(value = "公司代码")
    @NotEmpty(message = "公司代码不能为空")
    @Size(max = 10, message = "公司代码超长")
    protected String compCode;

    /** 工厂 */
    @Excel(name = "工厂")
    @JsonAlias("plantcode")
    @NotEmpty(message = "工厂代码不能为空")
    @Size(max = 40, message = "工厂代码超长")
    @ApiModelProperty(value = "工厂代码")
    protected String plantCode;

    /** 工厂名称 */
//    @Excel(name = "工厂名称")
    @JsonAlias("plantname")
//    @NotEmpty(message = "工厂名称不能为空")
    @Size(max = 40, message = "工厂名称超长")
    protected String plantName;

    /** 供应商 */
    @Excel(name = "供应商")
    @JsonAlias("suppcode")
    @NotEmpty(message = "供应商代码不能为空")
    @Size(max = 40, message = "供应商代码超长")
    @ApiModelProperty(value = "供应商代码")
    protected String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    @JsonAlias("suppname")
    @NotEmpty(message = "供应商名称不能为空")
    @Size(max = 40, message = "供应商名称超长")
    protected String suppName;

    public String getCompCode() {
        return compCode;
    }

    public void setCompCode(String compCode) {
        this.compCode = compCode;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getPlantName() {
        return plantName;
    }

    public void setPlantName(String plantName) {
        this.plantName = plantName;
    }

    public String getSuppCode() {
        return suppCode;
    }

    public void setSuppCode(String suppCode) {
        this.suppCode = suppCode;
    }

    public String getSuppName() {
        return suppName;
    }

    public void setSuppName(String suppName) {
        this.suppName = suppName;
    }
}
