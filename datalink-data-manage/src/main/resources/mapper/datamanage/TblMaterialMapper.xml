<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblMaterialMapper">
    
    <resultMap type="TblMaterial" id="TblMaterialResult">
        <result property="materialId"    column="Material_ID"    />
        <result property="materialCode"    column="Material_Code"    />
        <result property="materialName"    column="Material_Name"    />
        <result property="baseUnit"    column="Base_Unit"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="delFlag"    column="Del_Flag"    />
        <collection property="plants" notNullColumn="sub_Material_Plant_ID" javaType="java.util.List" resultMap="TblMaterialPlantResult" />
    </resultMap>
    
    <resultMap type="TblMaterialPlant" id="TblMaterialPlantResult">
        <result property="materialPlantId"    column="sub_Material_Plant_ID"    />
        <result property="materialId"    column="sub_Material_ID"    />
        <result property="plantCode"    column="sub_Plant_Code"    />
        <result property="packQuantity"    column="sub_Pack_Quantity"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
        <result property="delFlag"    column="sub_Del_Flag"    />
    </resultMap>

    <sql id="selectTblMaterialVo">
        select Material_ID, Material_Code, Material_Name, Base_Unit, Create_Time, Create_By, Update_Time, Update_By, Del_Flag from tbl_material
    </sql>
    
    <sql id="selectTblMaterialPlantVo">
        select Material_Plant_ID as sub_Material_Plant_ID, Material_ID as sub_Material_ID, Plant_Code as sub_Plant_Code, Pack_Quantity as sub_Pack_Quantity, Create_Time as sub_Create_Time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By, Del_Flag as sub_Del_Flag from tbl_material_plant
    </sql>

    <select id="selectTblMaterialList" parameterType="TblMaterial" resultMap="TblMaterialResult">
        <include refid="selectTblMaterialVo"/>
        <where>
            Del_Flag = '0'
            <if test="materialCode != null  and materialCode != ''"> and Material_Code like concat('%', #{materialCode}, '%')</if>
            <if test="materialName != null  and materialName != ''"> and Material_Name like concat('%', #{materialName}, '%')</if>
            <if test="baseUnit != null  and baseUnit != ''"> and Base_Unit = #{baseUnit}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and Create_Time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
    </select>
    
    <select id="selectTblMaterialById" parameterType="Long" resultMap="TblMaterialResult">
        select a.Material_ID, a.Material_Code, a.Material_Name, a.Base_Unit, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Del_Flag,
               b.Material_Plant_ID as sub_Material_Plant_ID, b.Material_ID as sub_Material_ID, b.Plant_Code as sub_Plant_Code, b.Pack_Quantity as sub_Pack_Quantity, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Del_Flag as sub_Del_Flag
        from tbl_material a
        left join tbl_material_plant b on b.Material_ID = a.Material_ID and b.Del_Flag = '0'
        where a.Material_ID = #{materialId} and a.Del_Flag = '0'
    </select>
    
    <select id="selectTblMaterialByCode" parameterType="String" resultMap="TblMaterialResult">
        select a.Material_ID, a.Material_Code, a.Material_Name, a.Base_Unit, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Del_Flag,
               b.Material_Plant_ID as sub_Material_Plant_ID, b.Material_ID as sub_Material_ID, b.Plant_Code as sub_Plant_Code, b.Pack_Quantity as sub_Pack_Quantity, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Del_Flag as sub_Del_Flag
        from tbl_material a
        left join tbl_material_plant b on b.Material_ID = a.Material_ID and b.Del_Flag = '0'
        where a.Material_Code = #{materialCode} and a.Del_Flag = '0'
    </select>

    <select id="selectTblMaterialByCodeAndPlant" parameterType="String" resultMap="TblMaterialResult">
        select a.Material_ID, a.Material_Code, a.Material_Name, a.Base_Unit, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Del_Flag,
               b.Material_Plant_ID as sub_Material_Plant_ID, b.Material_ID as sub_Material_ID, b.Plant_Code as sub_Plant_Code, b.Pack_Quantity as sub_Pack_Quantity, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Del_Flag as sub_Del_Flag
        from tbl_material a
        left join tbl_material_plant b on b.Material_ID = a.Material_ID and b.Del_Flag = '0'
        where a.Material_Code = #{materialCode} and b.Plant_Code = #{plantCode} and a.Del_Flag = '0'
    </select>
    
    <insert id="insertTblMaterial" parameterType="TblMaterial" useGeneratedKeys="true" keyProperty="materialId">
        insert into tbl_material
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialCode != null">Material_Code,</if>
            <if test="materialName != null">Material_Name,</if>
            <if test="baseUnit != null">Base_Unit,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="delFlag != null">Del_Flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialCode != null">#{materialCode},</if>
            <if test="materialName != null">#{materialName},</if>
            <if test="baseUnit != null">#{baseUnit},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>
    
    <update id="updateTblMaterial" parameterType="TblMaterial">
        update tbl_material
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialCode != null">Material_Code = #{materialCode},</if>
            <if test="materialName != null">Material_Name = #{materialName},</if>
            <if test="baseUnit != null">Base_Unit = #{baseUnit},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
        </trim>
        where Material_ID = #{materialId}
    </update>
    
    <update id="deleteTblMaterialById" parameterType="Long">
        update tbl_material set Del_Flag = '2' where Material_ID = #{materialId}
    </update>
    
    <update id="deleteTblMaterialByIds" parameterType="String">
        update tbl_material set Del_Flag = '2' where Material_ID in 
        <foreach item="materialId" collection="array" open="(" separator="," close=")">
            #{materialId}
        </foreach>
    </update>
</mapper> 