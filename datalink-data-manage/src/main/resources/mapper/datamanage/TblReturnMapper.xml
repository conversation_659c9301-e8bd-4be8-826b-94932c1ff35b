<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblReturnMapper">
    
    <resultMap type="TblReturn" id="TblReturnResult">
        <result property="returnId"    column="Return_ID"    />
        <result property="returnNo"    column="Return_No"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="status"      column="Status"       />
        <result property="createTime"  column="Create_Time"  />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"  column="Update_Time"  />
        <result property="updateBy"    column="Update_By"    />
        <result property="remark"      column="Remark"       />
    </resultMap>

    <resultMap id="TblReturnTblReturnBoxResult" type="TblReturn" extends="TblReturnResult">
        <collection property="boxList" notNullColumn="sub_Box_ID" javaType="java.util.List" resultMap="TblReturnBoxResult" />
    </resultMap>

    <resultMap type="TblReturnBox" id="TblReturnBoxResult">
        <result property="boxId"            column="sub_Box_ID"            />
        <result property="returnId"         column="sub_Return_ID"         />
        <result property="asnCode"          column="sub_Asn_Code"          />
        <result property="orderCode"        column="sub_Order_Code"        />
        <result property="itemNo"           column="sub_Item_No"           />
        <result property="releaseNo"        column="sub_Release_No"        />
        <result property="originalBatchNo"  column="sub_Original_Batch_No" />
        <result property="newBatchNo"       column="sub_New_Batch_No"      />
        <result property="originalLabel"    column="sub_Original_Label"    />
        <result property="newLabel"         column="sub_New_Label"         />
        <result property="boxIndex"         column="sub_Box_Index"         />
        <result property="qty"              column="sub_Qty"               />
        <result property="status"           column="sub_Status"            />
        <result property="createTime"       column="sub_Create_Time"       />
        <result property="createBy"         column="sub_Create_By"         />
        <result property="updateTime"       column="sub_Update_Time"       />
        <result property="updateBy"         column="sub_Update_By"         />
    </resultMap>

    <sql id="selectTblReturnVo">
        select Return_ID, Return_No, Supp_Code, Status, Create_Time, Create_By, Update_Time, Update_By, Remark from tbl_return
    </sql>

    <select id="selectTblReturnList" parameterType="TblReturn" resultMap="TblReturnResult">
        <include refid="selectTblReturnVo"/>
        <where>  
            <if test="returnNo != null  and returnNo != ''"> and Return_No like concat('%', #{returnNo}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code = #{suppCode}</if>
            <if test="status != null  and status != ''"> and Status = #{status}</if>
        </where>
        order by Create_Time desc
    </select>
    
    <select id="selectTblReturnByReturnId" parameterType="Long" resultMap="TblReturnResult">
        <include refid="selectTblReturnVo"/>
        where Return_ID = #{returnId}
    </select>

    <select id="selectTblReturnByReturnNo" parameterType="String" resultMap="TblReturnResult">
        <include refid="selectTblReturnVo"/>
        where Return_No = #{returnNo}
    </select>

    <select id="selectTblReturnWithBoxList" parameterType="TblReturn" resultMap="TblReturnTblReturnBoxResult">
        select a.Return_ID, a.Return_No, a.Supp_Code, a.Status, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Remark,
               b.Box_ID as sub_Box_ID, b.Return_ID as sub_Return_ID, b.Asn_Code as sub_Asn_Code, b.Order_Code as sub_Order_Code,
               b.Item_No as sub_Item_No, b.Release_No as sub_Release_No, b.Original_Batch_No as sub_Original_Batch_No,
               b.New_Batch_No as sub_New_Batch_No, b.Original_Label as sub_Original_Label, b.New_Label as sub_New_Label,
               b.Box_Index as sub_Box_Index, b.Qty as sub_Qty, b.Status as sub_Status,
               b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_return a
        left join tbl_return_box b on b.Return_ID = a.Return_ID
        <where>
            <if test="returnId != null"> and a.Return_ID = #{returnId}</if>
            <if test="returnNo != null  and returnNo != ''"> and a.Return_No like concat('%', #{returnNo}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and a.Supp_Code = #{suppCode}</if>
            <if test="status != null  and status != ''"> and a.Status = #{status}</if>
            <if test="params.createTimeBegin != null and params.createTimeBegin != '' and params.createTimeEnd != null and params.createTimeEnd != ''"> and a.Create_Time between #{params.createTimeBegin} and #{params.createTimeEnd}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by a.Create_Time desc, b.Asn_Code, b.Order_Code, b.Item_No, b.Release_No, b.Box_Index asc
    </select>
        
    <insert id="insertTblReturn" parameterType="TblReturn" useGeneratedKeys="true" keyProperty="returnId">
        insert into tbl_return
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="returnNo != null and returnNo != ''">Return_No,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="status != null">Status,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="remark != null">Remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="returnNo != null and returnNo != ''">#{returnNo},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblReturn" parameterType="TblReturn">
        update tbl_return
        <trim prefix="SET" suffixOverrides=",">
            <if test="returnNo != null and returnNo != ''">Return_No = #{returnNo},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="status != null">Status = #{status},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="remark != null">Remark = #{remark},</if>
        </trim>
        where Return_ID = #{returnId}
    </update>

    <delete id="deleteTblReturnByReturnId" parameterType="Long">
        delete from tbl_return where Return_ID = #{returnId}
    </delete>

    <delete id="deleteTblReturnByReturnIds" parameterType="String">
        delete from tbl_return where Return_ID in 
        <foreach item="returnId" collection="array" open="(" separator="," close=")">
            #{returnId}
        </foreach>
    </delete>
</mapper>
