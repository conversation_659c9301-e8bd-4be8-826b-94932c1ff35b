<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblForecastMapper">

    <resultMap type="TblForecast" id="TblForecastResult">
        <result property="forecastId"    column="Forecast_ID"    />
        <result property="forecastCode"    column="Forecast_Code"    />
        <result property="version"    column="Version"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
        <result property="status"    column="Status"    />
        <result property="downloadStatus"    column="Download_Status"    />
        <result property="lastDownloadTime"    column="Last_Download_Time"    />
    </resultMap>

    <resultMap id="TblForecastTblForecastItemResult" type="TblForecast" extends="TblForecastResult">
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblForecastItemResult" />
    </resultMap>

    <resultMap id="TblForecastTblForecastItemApiResult" type="TblApiForecast" extends="TblForecastResult">
        <collection property="detail" ofType="com.datalink.api.domain.TblApiForecastItem" select="selectTblApiForecastItemByForecastId" column="Forecast_ID"/>
    </resultMap>

    <resultMap type="TblForecastItem" id="TblForecastItemResult">
        <result property="itemId"    column="sub_Item_ID"    />
        <result property="itemNo"    column="sub_Item_No"    />
        <result property="articleNo"    column="sub_Article_No"    />
        <result property="articleName"    column="sub_Article_Name"    />
        <result property="deliveryDate"    column="sub_Delivery_Date"    />
        <result property="quantity"    column="sub_Quantity"    />
        <result property="unit"    column="sub_Unit"    />
        <result property="durType"    column="sub_Dur_Type"    />
        <result property="proType"    column="sub_Pro_Type"    />
        <result property="poddet"    column="sub_Poddet"    />
        <result property="forecastId"    column="sub_Forecast_ID"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
    </resultMap>

    <sql id="selectTblForecastVo">
        select Forecast_ID, Forecast_Code, Version, Comp_Code, Plant_Code, Plant_Name, Supp_Code, d.dept_name as Supp_Name, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, Direction, Kafka_Status, a.Status, Download_Status, Last_Download_Time from tbl_forecast a
        left join sys_dept d on d.supplier_code = a.Supp_Code
    </sql>

    <sql id="selectTblForecastItemVo">
        select Item_ID as sub_Item_ID, Article_No as sub_Article_No, m.Material_Name as sub_Article_Name, Delivery_Date as sub_Delivery_Date, Quantity as sub_Quantity, Unit as sub_Unit, Dur_Type as sub_Dur_Type, Pro_Type as sub_Pro_Type, Poddet as sub_Poddet, Forecast_ID as sub_Forecast_ID, i.Create_Time as sub_Create_Time, i.Create_By as sub_Create_By, i.Update_Time as sub_Update_Time, i.Update_By as sub_Update_By from tbl_forecast_item i
        left join tbl_material m on i.Article_No = m.Material_Code
    </sql>

    <resultMap id="TblApiForecastItemResult" type="TblApiForecastItem" extends="TblForecastItemResult">
    </resultMap>

    <select id="selectTblForecastList" parameterType="TblForecast" resultMap="TblForecastResult">
        <include refid="selectTblForecastVo"/>
        <where>
            <if test="forecastCode != null  and forecastCode != ''"> and Forecast_Code like concat('%', #{forecastCode}, '%')</if>
            <if test="version != null  and version != ''"> and Version like concat('%', #{version}, '%')</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <if test="status != null  and status != ''"> and Status = #{status}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectTblApiForecastItemByForecastId" parameterType="Long" resultMap="TblApiForecastItemResult">
        <include refid="selectTblForecastItemVo"/>
        where Forecast_ID = #{forecastId}
    </select>

    <select id="selectTblForecastItemList" parameterType="TblForecastItem" resultMap="TblForecastItemResult">
        <include refid="selectTblForecastItemVo"/>
        <where>
            <if test="articleNo != null  and articleNo != ''"> and Article_No like concat('%', #{articleNo}, '%')</if>
            <if test="articleName != null  and articleName != ''"> and Aritcle_Name like concat('%', #{articleName}, '%')</if>
            <if test="deliveryDate != null "> and Delivery_Date = #{deliveryDate}</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit = #{unit}</if>
            <if test="durType != null  and durType != ''"> and Dur_Type = #{durType}</if>
            <if test="proType != null  and proType != ''"> and Pro_Type = #{proType}</if>
            <if test="poddet != null  and poddet != ''"> and Poddet = #{poddet}</if>
            <if test="forecastId != null "> and Forecast_ID = #{forecastId}</if>
        </where>
    </select>

    <select id="selectTblForecastFullList" parameterType="TblForecast" resultMap="TblForecastTblForecastItemApiResult">
        <include refid="selectTblForecastVo"/>
        <where>
            Direction = 'I'
            <if test="params.cursor !=null">and Forecast_ID > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and Forecast_ID >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and Create_Time > #{params.time}</if>
            <if test="params.timeInclude !=null">and Create_Time >= #{params.timeInclude}</if>
        </where>
        order by Forecast_ID asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>

    <select id="selectTblForecastOnlyById" parameterType="TblForecast" resultMap="TblForecastResult">
        <include refid="selectTblForecastVo"/>
        where Forecast_ID = #{forecastId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectTblForecastById" parameterType="Long" resultMap="TblForecastTblForecastItemResult">
        select a.Forecast_ID, a.Forecast_Code, a.Version, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, d.dept_name as Supp_Name, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status, a.Status,
            b.Item_ID as sub_Item_ID, b.Item_No as sub_Item_No, b.Article_No as sub_Article_No, m.Material_Name as sub_Article_Name, b.Delivery_Date as sub_Delivery_Date, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Dur_Type as sub_Dur_Type, b.Pro_Type as sub_Pro_Type, b.Poddet as sub_Poddet, b.Forecast_ID as sub_Forecast_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_forecast a
        left join tbl_forecast_item b on b.Forecast_ID = a.Forecast_ID
        left join sys_dept d on a.Supp_Code = d.supplier_code
        left join tbl_material m on b.Article_No = m.Material_Code
        where a.Forecast_ID = #{forecastId}
    </select>

    <select id="selectTblForecastWithItemList" parameterType="TblForecast" resultMap="TblForecastTblForecastItemResult">
        select a.Forecast_ID, a.Forecast_Code, a.Version, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status, a.Status,
               b.Item_ID as sub_Item_ID, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Delivery_Date as sub_Delivery_Date, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Dur_Type as sub_Dur_Type, b.Pro_Type as sub_Pro_Type, b.Poddet as sub_Poddet, b.Forecast_ID as sub_Forecast_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_forecast a
                 left join tbl_forecast_item b on b.Forecast_ID = a.Forecast_ID
        <where>
            <if test="forecastCode != null  and forecastCode != ''"> and a.Forecast_Code like concat('%', #{forecastCode}, '%')</if>
            <if test="version != null  and version != ''"> and a.Version like concat('%', #{version}, '%')</if>
            <if test="compCode != null  and compCode != ''"> and a.Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and a.Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and a.Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and a.Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and a.Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and a.Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and a.Kafka_Status = #{kafkaStatus}</if>
            <if test="status != null  and status != ''"> and a.Status = #{status}</if>
        </where>
    </select>

    <insert id="insertTblForecast" parameterType="TblForecast" useGeneratedKeys="true" keyProperty="forecastId">
        insert into tbl_forecast
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="forecastCode != null and forecastCode != ''">Forecast_Code,</if>
            <if test="version != null and version != ''">Version,</if>
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code,</if>
            <if test="plantName != null and plantName != ''">Plant_Name,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status,</if>
            <if test="status != null and status != ''">Status,</if>
            <if test="downloadStatus != null and downloadStatus != ''">Download_Status,</if>
            <if test="lastDownloadTime != null">Last_Download_Time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="forecastCode != null and forecastCode != ''">#{forecastCode},</if>
            <if test="version != null and version != ''">#{version},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="downloadStatus != null and downloadStatus != ''">#{downloadStatus},</if>
            <if test="lastDownloadTime != null">#{lastDownloadTime},</if>
         </trim>
    </insert>

    <update id="updateTblForecast" parameterType="TblForecast">
        update tbl_forecast
        <trim prefix="SET" suffixOverrides=",">
            <if test="forecastCode != null and forecastCode != ''">Forecast_Code = #{forecastCode},</if>
            <if test="version != null and version != ''">Version = #{version},</if>
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status = #{kafkaStatus},</if>
            <if test="status != null and status != ''">Status = #{status},</if>
            <if test="downloadStatus != null and downloadStatus != ''">Download_Status = #{downloadStatus},</if>
            <if test="lastDownloadTime != null">Last_Download_Time = #{lastDownloadTime},</if>
        </trim>
        where Forecast_ID = #{forecastId}
    </update>

    <delete id="deleteTblForecastById" parameterType="Long">
        delete from tbl_forecast where Forecast_ID = #{forecastId}
    </delete>

    <delete id="deleteTblForecastByIds" parameterType="String">
        delete from tbl_forecast where Forecast_ID in
        <foreach item="forecastId" collection="array" open="(" separator="," close=")">
            #{forecastId}
        </foreach>
    </delete>

    <delete id="deleteTblForecastItemByForecastIds" parameterType="String">
        delete from tbl_forecast_item where Forecast_ID in
        <foreach item="forecastId" collection="array" open="(" separator="," close=")">
            #{forecastId}
        </foreach>
    </delete>

    <delete id="deleteTblForecastItemByForecastId" parameterType="Long">
        delete from tbl_forecast_item where Forecast_ID = #{forecastId}
    </delete>

    <insert id="batchTblForecastItem">
        insert into tbl_forecast_item( Item_No, Article_No, Article_Name, Delivery_Date, Quantity, Unit, Dur_Type, Pro_Type, Poddet, Forecast_ID, Create_Time, Create_By, Update_Time, Update_By) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.itemNo}, #{item.articleNo}, #{item.articleName}, #{item.deliveryDate}, #{item.quantity}, #{item.unit}, #{item.durType}, #{item.proType}, #{item.poddet}, #{item.forecastId}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectLastId" resultType="Long">
        select max(Forecast_ID) from tbl_forecast;
    </select>

    <select id="selectMaxVersionByForecastCode" parameterType="String" resultType="String">
        select max(Version) from tbl_forecast where Forecast_Code = #{forecastCode}
    </select>

    <select id="selectTblForecastLatestVersionList" parameterType="TblForecast" resultMap="TblForecastResult">
        <include refid="selectTblForecastVo"/>
        where (Forecast_Code, Version) in (
            select Forecast_Code, max(Version)
            from tbl_forecast
            group by Forecast_Code
        )
        <if test="forecastCode != null and forecastCode != ''"> and Forecast_Code like concat('%', #{forecastCode}, '%')</if>
        <if test="compCode != null and compCode != ''"> and Comp_Code like concat('%', #{compCode}, '%')</if>
        <if test="plantCode != null and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
        <if test="plantName != null and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
        <if test="suppCode != null and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
        <if test="suppName != null and suppName != ''"> and d.dept_name like concat('%', #{suppName}, '%')</if>
        <if test="direction != null and direction != ''"> and Direction = #{direction}</if>
        <if test="kafkaStatus != null and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
        <if test="status != null and status != ''"> and a.Status = #{status}</if>
        <if test="params.createTimeBegin != null and params.createTimeBegin != '' and params.createTimeEnd != null and params.createTimeEnd != ''"> and a.Create_Time between #{params.createTimeBegin} and #{params.createTimeEnd}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by Forecast_Code, Version desc
    </select>

    <select id="selectTblForecastLatestVersionWithItemList" parameterType="TblForecast" resultMap="TblForecastTblForecastItemResult">
        select a.Forecast_ID, a.Forecast_Code, a.Version, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status, a.Status, a.Download_Status, a.Last_Download_Time,
               b.Item_ID as sub_Item_ID, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Delivery_Date as sub_Delivery_Date, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Dur_Type as sub_Dur_Type, b.Pro_Type as sub_Pro_Type, b.Poddet as sub_Poddet, b.Forecast_ID as sub_Forecast_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_forecast a
        left join tbl_forecast_item b on b.Forecast_ID = a.Forecast_ID
        where (a.Forecast_Code, a.Version) in (
            select Forecast_Code, max(Version)
            from tbl_forecast
            group by Forecast_Code
        )
        <if test="forecastCode != null and forecastCode != ''"> and a.Forecast_Code like concat('%', #{forecastCode}, '%')</if>
        <if test="compCode != null and compCode != ''"> and a.Comp_Code like concat('%', #{compCode}, '%')</if>
        <if test="plantCode != null and plantCode != ''"> and a.Plant_Code like concat('%', #{plantCode}, '%')</if>
        <if test="plantName != null and plantName != ''"> and a.Plant_Name like concat('%', #{plantName}, '%')</if>
        <if test="suppCode != null and suppCode != ''"> and a.Supp_Code like concat('%', #{suppCode}, '%')</if>
        <if test="suppName != null and suppName != ''"> and a.Supp_Name like concat('%', #{suppName}, '%')</if>
        <if test="direction != null and direction != ''"> and a.Direction = #{direction}</if>
        <if test="kafkaStatus != null and kafkaStatus != ''"> and a.Kafka_Status = #{kafkaStatus}</if>
        <if test="status != null and status != ''"> and a.Status = #{status}</if>
        <if test="downloadStatus != null and downloadStatus != ''"> and a.Download_Status = #{downloadStatus}</if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by a.Forecast_Code, a.Version desc
    </select>

    <update id="batchUpdateForecastStatus">
        update tbl_forecast set Status = #{status}, Update_Time = now()
        where Forecast_ID in
        <foreach item="forecastId" collection="forecastIds" open="(" separator="," close=")">
            #{forecastId}
        </foreach>
    </update>
</mapper>