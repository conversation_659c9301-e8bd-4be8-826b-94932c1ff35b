<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblAsnBoxMapper">
    
    <resultMap type="TblAsnBox" id="TblAsnBoxResult">
        <result property="boxId"    column="Box_ID"    />
        <result property="articleId"    column="Article_ID"    />
        <result property="boxNo"    column="Box_No"    />
        <result property="batchNo"    column="Batch_No"    />
        <result property="quantity"    column="Quantity"    />
        <result property="palletNo"    column="Pallet_No"    />
        <result property="palletQty"    column="Pallet_Qty"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
    </resultMap>

    <sql id="selectTblAsnBoxVo">
        select Box_ID, Article_ID, Box_No, Batch_No, Quantity, Pallet_No, Pallet_Qty, Create_Time, Create_By, Update_Time, Update_By from tbl_asn_box
    </sql>

    <select id="selectTblAsnBoxList" parameterType="TblAsnBox" resultMap="TblAsnBoxResult">
        <include refid="selectTblAsnBoxVo"/>
        <where>  
            <if test="articleId != null "> and Article_ID = #{articleId}</if>
            <if test="boxNo != null  and boxNo != ''"> and Box_No like concat('%', #{boxNo}, '%')</if>
            <if test="batchNo != null  and batchNo != ''"> and Batch_No like concat('%', #{batchNo}, '%')</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="palletNo != null  and palletNo != ''"> and Pallet_No like concat('%', #{palletNo}, '%')</if>
            <if test="palletQty != null "> and Pallet_Qty = #{palletQty}</if>
        </where>
    </select>
    
    <select id="selectTblAsnBoxById" parameterType="Long" resultMap="TblAsnBoxResult">
        <include refid="selectTblAsnBoxVo"/>
        where Box_ID = #{boxId}
    </select>

    <select id="selectTblAsnBoxByArticleId" parameterType="Long" resultMap="TblAsnBoxResult">
        <include refid="selectTblAsnBoxVo"/>
        where Article_ID = #{articleId}
        order by CAST(Box_No AS UNSIGNED)
    </select>

    <select id="selectTblAsnBoxByAsnId" parameterType="Long" resultMap="TblAsnBoxResult">
        <include refid="selectTblAsnBoxVo"/> b
        inner join tbl_asn_article a on b.Article_ID = a.Article_ID
        inner join tbl_asn_item i on a.Item_ID = i.Item_ID
        where i.Asn_ID = #{asnId}
        order by CAST(b.Box_No AS UNSIGNED)
    </select>
        
    <insert id="insertTblAsnBox" parameterType="TblAsnBox" useGeneratedKeys="true" keyProperty="boxId">
        insert into tbl_asn_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="articleId != null">Article_ID,</if>
            <if test="boxNo != null and boxNo != ''">Box_No,</if>
            <if test="batchNo != null">Batch_No,</if>
            <if test="quantity != null">Quantity,</if>
            <if test="palletNo != null">Pallet_No,</if>
            <if test="palletQty != null">Pallet_Qty,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="articleId != null">#{articleId},</if>
            <if test="boxNo != null and boxNo != ''">#{boxNo},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="palletNo != null">#{palletNo},</if>
            <if test="palletQty != null">#{palletQty},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <insert id="batchInsertTblAsnBox" parameterType="java.util.List">
        insert into tbl_asn_box(Article_ID, Box_No, Batch_No, Quantity, Pallet_No, Pallet_Qty, Create_Time, Create_By) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.articleId}, #{item.boxNo}, #{item.batchNo}, #{item.quantity}, #{item.palletNo}, #{item.palletQty}, #{item.createTime}, #{item.createBy})
        </foreach>
    </insert>

    <update id="updateTblAsnBox" parameterType="TblAsnBox">
        update tbl_asn_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="articleId != null">Article_ID = #{articleId},</if>
            <if test="boxNo != null and boxNo != ''">Box_No = #{boxNo},</if>
            <if test="batchNo != null">Batch_No = #{batchNo},</if>
            <if test="quantity != null">Quantity = #{quantity},</if>
            <if test="palletNo != null">Pallet_No = #{palletNo},</if>
            <if test="palletQty != null">Pallet_Qty = #{palletQty},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
        </trim>
        where Box_ID = #{boxId}
    </update>

    <delete id="deleteTblAsnBoxById" parameterType="Long">
        delete from tbl_asn_box where Box_ID = #{boxId}
    </delete>

    <delete id="deleteTblAsnBoxByIds" parameterType="String">
        delete from tbl_asn_box where Box_ID in 
        <foreach item="boxId" collection="array" open="(" separator="," close=")">
            #{boxId}
        </foreach>
    </delete>

    <delete id="deleteTblAsnBoxByArticleId" parameterType="Long">
        delete from tbl_asn_box where Article_ID = #{articleId}
    </delete>

    <delete id="deleteTblAsnBoxByAsnId" parameterType="Long">
        delete from tbl_asn_box where Article_ID in (
            select a.Article_ID from tbl_asn_article a 
            inner join tbl_asn_item i on a.Item_ID = i.Item_ID 
            where i.Asn_ID = #{asnId}
        )
    </delete>
</mapper>
