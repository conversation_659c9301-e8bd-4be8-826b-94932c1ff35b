<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblReturnBoxMapper">
    
    <resultMap type="TblReturnBox" id="TblReturnBoxResult">
        <result property="boxId"            column="Box_ID"            />
        <result property="returnId"         column="Return_ID"         />
        <result property="asnCode"          column="Asn_Code"          />
        <result property="orderCode"        column="Order_Code"        />
        <result property="itemNo"           column="Item_No"           />
        <result property="releaseNo"        column="Release_No"        />
        <result property="originalBatchNo"  column="Original_Batch_No" />
        <result property="newBatchNo"       column="New_Batch_No"      />
        <result property="originalLabel"    column="Original_Label"    />
        <result property="newLabel"         column="New_Label"         />
        <result property="boxIndex"         column="Box_Index"         />
        <result property="qty"              column="Qty"               />
        <result property="status"           column="Status"            />
        <result property="createTime"       column="Create_Time"       />
        <result property="createBy"         column="Create_By"         />
        <result property="updateTime"       column="Update_Time"       />
        <result property="updateBy"         column="Update_By"         />
    </resultMap>

    <sql id="selectTblReturnBoxVo">
        select Box_ID, Return_ID, Asn_Code, Order_Code, Item_No, Release_No, Original_Batch_No, New_Batch_No, Original_Label, New_Label, Box_Index, Qty, Status, Create_Time, Create_By, Update_Time, Update_By from tbl_return_box
    </sql>

    <select id="selectTblReturnBoxList" parameterType="TblReturnBox" resultMap="TblReturnBoxResult">
        <include refid="selectTblReturnBoxVo"/>
        <where>
            <if test="returnId != null"> and Return_ID = #{returnId}</if>
            <if test="asnCode != null  and asnCode != ''"> and Asn_Code = #{asnCode}</if>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code = #{orderCode}</if>
            <if test="itemNo != null  and itemNo != ''"> and Item_No = #{itemNo}</if>
            <if test="releaseNo != null  and releaseNo != ''"> and Release_No = #{releaseNo}</if>
            <if test="status != null  and status != ''"> and Status = #{status}</if>
        </where>
        order by Box_Index asc
    </select>
    
    <select id="selectTblReturnBoxByBoxId" parameterType="Long" resultMap="TblReturnBoxResult">
        <include refid="selectTblReturnBoxVo"/>
        where Box_ID = #{boxId}
    </select>

    <select id="selectTblReturnBoxByReturnId" parameterType="Long" resultMap="TblReturnBoxResult">
        <include refid="selectTblReturnBoxVo"/>
        where Return_ID = #{returnId}
        order by Box_Index asc
    </select>
        
    <insert id="insertTblReturnBox" parameterType="TblReturnBox" useGeneratedKeys="true" keyProperty="boxId">
        insert into tbl_return_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="returnId != null">Return_ID,</if>
            <if test="asnCode != null and asnCode != ''">Asn_Code,</if>
            <if test="orderCode != null and orderCode != ''">Order_Code,</if>
            <if test="itemNo != null and itemNo != ''">Item_No,</if>
            <if test="releaseNo != null and releaseNo != ''">Release_No,</if>
            <if test="originalBatchNo != null">Original_Batch_No,</if>
            <if test="newBatchNo != null">New_Batch_No,</if>
            <if test="originalLabel != null">Original_Label,</if>
            <if test="newLabel != null">New_Label,</if>
            <if test="boxIndex != null">Box_Index,</if>
            <if test="qty != null">Qty,</if>
            <if test="status != null">Status,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="returnId != null">#{returnId},</if>
            <if test="asnCode != null and asnCode != ''">#{asnCode},</if>
            <if test="orderCode != null and orderCode != ''">#{orderCode},</if>
            <if test="itemNo != null and itemNo != ''">#{itemNo},</if>
            <if test="releaseNo != null and releaseNo != ''">#{releaseNo},</if>
            <if test="originalBatchNo != null">#{originalBatchNo},</if>
            <if test="newBatchNo != null">#{newBatchNo},</if>
            <if test="originalLabel != null">#{originalLabel},</if>
            <if test="newLabel != null">#{newLabel},</if>
            <if test="boxIndex != null">#{boxIndex},</if>
            <if test="qty != null">#{qty},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <insert id="batchInsertTblReturnBox">
        insert into tbl_return_box( Return_ID, Asn_Code, Order_Code, Item_No, Release_No, Original_Batch_No, New_Batch_No, Original_Label, New_Label, Box_Index, Qty, Status, Create_Time, Create_By, Update_Time, Update_By) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.returnId}, #{item.asnCode}, #{item.orderCode}, #{item.itemNo}, #{item.releaseNo}, #{item.originalBatchNo}, #{item.newBatchNo}, #{item.originalLabel}, #{item.newLabel}, #{item.boxIndex}, #{item.qty}, #{item.status}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <update id="updateTblReturnBox" parameterType="TblReturnBox">
        update tbl_return_box
        <trim prefix="SET" suffixOverrides=",">
<!--            <if test="returnId != null">Return_ID = #{returnId},</if>-->
<!--            <if test="asnCode != null and asnCode != ''">Asn_Code = #{asnCode},</if>-->
<!--            <if test="orderCode != null and orderCode != ''">Order_Code = #{orderCode},</if>-->
<!--            <if test="itemNo != null and itemNo != ''">Item_No = #{itemNo},</if>-->
<!--            <if test="releaseNo != null and releaseNo != ''">Release_No = #{releaseNo},</if>-->
<!--            <if test="originalBatchNo != null">Original_Batch_No = #{originalBatchNo},</if>-->
            <if test="newBatchNo != null">New_Batch_No = #{newBatchNo},</if>
<!--            <if test="originalLabel != null">Original_Label = #{originalLabel},</if>-->
            <if test="newLabel != null">New_Label = #{newLabel},</if>
<!--            <if test="boxIndex != null">Box_Index = #{boxIndex},</if>-->
<!--            <if test="qty != null">Qty = #{qty},</if>-->
            <if test="status != null">Status = #{status},</if>
<!--            <if test="createTime != null">Create_Time = #{createTime},</if>-->
<!--            <if test="createBy != null">Create_By = #{createBy},</if>-->
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
        </trim>
        where Box_ID = #{boxId}
    </update>

    <update id="batchUpdateTblReturnBoxBatch">
        <foreach collection="list" item="item" separator=";">
            update tbl_return_box
            <set>
                <if test="item.newBatchNo != null">New_Batch_No = #{item.newBatchNo},</if>
                <if test="item.newLabel != null">New_Label = #{item.newLabel},</if>
                <if test="item.status != null">Status = #{item.status},</if>
                <if test="item.updateTime != null">Update_Time = #{item.updateTime},</if>
                <if test="item.updateBy != null">Update_By = #{item.updateBy},</if>
            </set>
            where Box_ID = #{item.boxId}
        </foreach>
    </update>

    <delete id="deleteTblReturnBoxByBoxId" parameterType="Long">
        delete from tbl_return_box where Box_ID = #{boxId}
    </delete>

    <delete id="deleteTblReturnBoxByBoxIds" parameterType="String">
        delete from tbl_return_box where Box_ID in 
        <foreach item="boxId" collection="array" open="(" separator="," close=")">
            #{boxId}
        </foreach>
    </delete>

    <delete id="deleteTblReturnBoxByReturnId" parameterType="Long">
        delete from tbl_return_box where Return_ID = #{returnId}
    </delete>
</mapper>
