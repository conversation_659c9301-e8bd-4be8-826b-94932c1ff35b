# tbl_order_asn_quantity表添加Delivery_Schedule_No字段修改总结

## 1. 数据库DDL修改

### 文件：`add_delivery_schedule_no_ddl.sql`
- 为`tbl_order_asn_quantity`表添加`Delivery_Schedule_No`字段
- 字段类型：`varchar(10) DEFAULT NULL COMMENT '交货计划行号'`
- 位置：在`Order_Line_No`字段之后

## 2. 实体类修改

### 文件：`datalink-data-manage/src/main/java/com/datalink/datamanage/domain/TblOrderAsnQuantity.java`
- 添加`deliveryScheduleNo`属性
- 添加对应的getter和setter方法

## 3. Mapper XML修改

### 文件：`datalink-data-manage/src/main/resources/mapper/datamanage/TblOrderMapper.xml`

#### 3.1 批量插入SQL修改
- `batchTblOrderAsnQuantity`方法：在INSERT语句中添加`Delivery_Schedule_No`字段

#### 3.2 更新SQL修改
- `updateTblOrderAsnQuantity`方法：在WHERE条件中添加`Delivery_Schedule_No`的条件判断

### 文件：`datalink-data-manage/src/main/resources/mapper/datamanage/TblAsnMapper.xml`

#### 3.3 结果映射修改
- `tblOrderAsnQuantityMap`：添加`deliveryScheduleNo`属性映射

#### 3.4 查询SQL修改
- `selectTblOrderAsnQuantityList`：在SELECT字段中添加`Delivery_Schedule_No`，在WHERE条件中添加该字段的查询条件

#### 3.5 更新订单数量SQL修改
- `updateOrderArticleQuantity`：在WHERE条件中添加`Delivery_Schedule_No`的条件判断

#### 3.6 ASN相关复杂更新SQL修改
以下方法都进行了修改，添加了与`tbl_order_item`表的关联以获取`Delivery_Schedule_No`：

- `reduceOrderArticleQuantityByAsnId`：
  - 在子查询中关联`tbl_order_item`表获取`Delivery_Schedule_No`
  - 在WHERE条件中添加`Delivery_Schedule_No`的匹配条件
  - 使用`COALESCE`函数处理NULL值

- `addOrderArticleQuantityByAsnId`：
  - 在子查询中关联`tbl_order_item`表获取`Delivery_Schedule_No`
  - 在WHERE条件中添加`Delivery_Schedule_No`的匹配条件
  - 使用`COALESCE`函数处理NULL值

- `recoverOrderArticleQuantityByAsnId`：
  - 在子查询中关联`tbl_order_item`表获取`Delivery_Schedule_No`
  - 在WHERE条件中添加`Delivery_Schedule_No`的匹配条件
  - 使用`COALESCE`函数处理NULL值

## 4. 业务逻辑修改

### 文件：`datalink-data-manage/src/main/java/com/datalink/datamanage/service/impl/TblOrderServiceImpl.java`

#### 4.1 新增订单时的修改
- `insertTblOrderItem`方法：在创建`TblOrderAsnQuantity`对象时设置`deliveryScheduleNo`字段

#### 4.2 更新订单时的修改
- `updateTblOrderItem`方法：在创建`TblOrderAsnQuantity`对象时设置`deliveryScheduleNo`字段

## 5. 关联关系说明

### 表关联关系
- `tbl_order_asn_quantity` ↔ `tbl_order_item`：
  - 通过`Order_Code`、`Order_Line_No`和`Delivery_Schedule_No`进行关联
  - `tbl_order_asn_quantity.Order_Code` = `tbl_order.Order_Code`
  - `tbl_order_asn_quantity.Order_Line_No` = `tbl_order_item.Item_No`
  - `tbl_order_asn_quantity.Delivery_Schedule_No` = `tbl_order_item.Delivery_Schedule_No`

### ASN相关关联
- `tbl_asn_item` → `tbl_order`：通过`Order_Code`关联
- `tbl_asn_article` → `tbl_order_item`：通过`Order_Line_No`与`Item_No`关联
- 在ASN相关的更新操作中，需要通过这些关联获取`Delivery_Schedule_No`

## 6. 注意事项

1. **NULL值处理**：使用`COALESCE`函数处理`Delivery_Schedule_No`可能为NULL的情况
2. **数据一致性**：确保在创建`TblOrderAsnQuantity`记录时，`deliveryScheduleNo`字段与对应的`TblOrderItem`记录保持一致
3. **查询性能**：如果查询频繁，可以考虑为`Delivery_Schedule_No`字段添加索引
4. **向后兼容**：现有的查询和更新操作通过条件判断保持向后兼容性

## 7. 测试建议

1. 测试新增订单时`tbl_order_asn_quantity`表的数据是否正确插入
2. 测试更新订单时`tbl_order_asn_quantity`表的数据是否正确更新
3. 测试ASN相关操作（创建、修改、删除）时订单数量的更新是否正确
4. 测试查询`TblOrderAsnQuantity`时是否能正确返回`deliveryScheduleNo`字段
5. 测试在有和没有`Delivery_Schedule_No`值的情况下，所有操作是否都能正常工作
