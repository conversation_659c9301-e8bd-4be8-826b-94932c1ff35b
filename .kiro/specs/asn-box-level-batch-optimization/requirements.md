# ASN箱级批次优化需求文档

## 介绍

当前ASN模块在tblAsnArticle表上记录总箱数和批次号，所有箱子共享相同的批次号。但实际业务需求是每个箱子可以有不同的批次号。本功能将重构数据结构，在article下新增箱信息层级，支持箱级别的批次号管理，并优化相关的查询接口和打印功能。

## 需求

### 需求1：箱信息数据结构重构

**用户故事：** 作为系统管理员，我希望能够为每个箱子单独设置批次号，以便满足实际业务中不同箱子有不同批次号的需求。

#### 验收标准

1. WHEN 系统启动时 THEN 系统 SHALL 创建新的箱信息表tbl_asn_box，包含箱号、批次号、数量、托号等字段
2. WHEN 创建ASN时 THEN 系统 SHALL 同时创建对应的箱信息记录
3. WHEN 修改ASN时 THEN 系统 SHALL 先删除原有箱信息再创建新的箱信息记录
4. WHEN 保存ASN数据时 THEN 系统 SHALL 验证article总箱数与箱子列表数量一致
5. WHEN 保存箱信息时 THEN 系统 SHALL 根据托盘配置自动计算并设置托号

### 需求2：WMS接口优化

**用户故事：** 作为WMS系统，我希望通过API接口获取的ASN数据包含准确的箱标签信息，而不是实时计算的结果。

#### 验收标准

1. WHEN WMS系统调用/api/asn/{asnCode}接口时 THEN 系统 SHALL 从箱信息表中获取箱标签数据
2. WHEN 返回ASN数据时 THEN 系统 SHALL 包含每个箱子的准确批次号和标签信息
3. WHEN 箱信息不存在时 THEN 系统 SHALL 返回适当的错误信息
4. WHEN 接口调用成功时 THEN 系统 SHALL 保持现有的JSON响应格式兼容性

### 需求3：打印功能优化

**用户故事：** 作为仓库操作员，我希望打印现品票和托标签时显示的箱标签信息是准确的，基于实际存储的箱信息数据。

#### 验收标准

1. WHEN 打印现品票时 THEN 系统 SHALL 从箱信息表中获取箱标签数据而非实时计算
2. WHEN 打印托标签时 THEN 系统 SHALL 从箱信息表中获取箱标签数据而非实时计算
3. WHEN 箱信息数据缺失时 THEN 系统 SHALL 显示错误提示并阻止打印
4. WHEN 打印成功时 THEN 系统 SHALL 确保标签上的批次号与实际箱信息一致

### 需求4：数据一致性保障

**用户故事：** 作为数据管理员，我希望系统能够保证ASN数据的一致性，确保箱信息与article信息匹配。

#### 验收标准

1. WHEN 创建或修改ASN时 THEN 系统 SHALL 验证所有箱子的总数量等于article的总数量
2. WHEN 删除ASN时 THEN 系统 SHALL 同时删除相关的箱信息记录
3. WHEN 数据不一致时 THEN 系统 SHALL 阻止保存操作并返回详细错误信息
4. WHEN 系统检测到数据异常时 THEN 系统 SHALL 记录错误日志便于排查

### 需求5：向后兼容性

**用户故事：** 作为系统维护人员，我希望新的箱信息功能不会影响现有的ASN功能和数据。

#### 验收标准

1. WHEN 升级系统时 THEN 系统 SHALL 保持现有ASN数据的完整性
2. WHEN 现有功能调用时 THEN 系统 SHALL 继续正常工作不受影响
3. WHEN 新旧数据共存时 THEN 系统 SHALL 能够正确处理两种数据格式
4. WHEN 数据迁移时 THEN 系统 SHALL 提供迁移脚本将现有数据转换为新格式