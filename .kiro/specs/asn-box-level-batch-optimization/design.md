# ASN箱级批次优化设计文档

## 概述

本设计文档描述了ASN箱级批次优化功能的技术实现方案。该功能将在现有的ASN模块基础上，新增箱信息管理层级，支持每个箱子独立的批次号管理，并优化相关的查询接口和打印功能。

## 架构

### 数据层架构
```
tbl_asn (ASN主表)
├── tbl_asn_item (ASN行项目表)
    ├── tbl_asn_article (ASN物料表)
        └── tbl_asn_box (新增：ASN箱信息表) ← 新增层级
```

### 服务层架构
- **TblAsnBoxService**: 新增箱信息管理服务
- **TblAsnService**: 修改现有ASN服务，集成箱信息处理
- **TblAsnApi**: 修改WMS API接口，从箱表获取数据
- **打印服务**: 修改现品票和托标签打印逻辑

## 组件和接口

### 1. 数据模型设计

#### 新增箱信息表 (tbl_asn_box)
```sql
CREATE TABLE `tbl_asn_box` (
  `box_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '箱信息ID',
  `article_id` bigint(20) NOT NULL COMMENT 'ASN物料ID',
  `box_no` varchar(50) NOT NULL COMMENT '箱号',
  `batch_no` varchar(40) DEFAULT NULL COMMENT '批次号',
  `quantity` decimal(18,6) NOT NULL COMMENT '箱内数量',
  `pallet_no` varchar(50) DEFAULT NULL COMMENT '托号',
  `box_label` varchar(500) DEFAULT NULL COMMENT '箱标签',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`box_id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_box_no` (`box_no`),
  CONSTRAINT `fk_asn_box_article` FOREIGN KEY (`article_id`) REFERENCES `tbl_asn_article` (`article_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ASN箱信息表';
```

#### 新增箱信息实体类 (TblAsnBox)
```java
public class TblAsnBox extends BaseEntity {
    private Long boxId;
    private Long articleId;
    private String boxNo;
    private String batchNo;
    private BigDecimal quantity;
    private String palletNo;
    private String boxLabel;
    // getter/setter方法
}
```

#### 修改TblAsnArticle实体类
```java
public class TblAsnArticle extends BaseEntity {
    // 现有字段保持不变
    
    // 新增箱信息列表
    private List<TblAsnBox> boxes;
    
    // 保持现有的Pallet和Box内部类用于向后兼容
    // 但数据来源改为从tbl_asn_box表获取
}
```

### 2. 数据访问层设计

#### 新增TblAsnBoxMapper
```java
public interface TblAsnBoxMapper {
    List<TblAsnBox> selectTblAsnBoxByArticleId(Long articleId);
    int insertTblAsnBox(TblAsnBox tblAsnBox);
    int batchInsertTblAsnBox(List<TblAsnBox> tblAsnBoxList);
    int deleteTblAsnBoxByArticleId(Long articleId);
    int deleteTblAsnBoxByAsnId(Long asnId);
}
```

#### 修改TblAsnMapper
- 新增查询方法支持关联箱信息表
- 修改selectTblAsnByAsnCode方法包含箱信息

### 3. 业务服务层设计

#### 新增ITblAsnBoxService
```java
public interface ITblAsnBoxService {
    List<TblAsnBox> selectTblAsnBoxByArticleId(Long articleId);
    int saveTblAsnBoxList(List<TblAsnBox> boxList);
    int deleteTblAsnBoxByArticleId(Long articleId);
    void generateBoxesFromArticle(TblAsnArticle article);
    void validateBoxConsistency(TblAsnArticle article, List<TblAsnBox> boxes);
}
```

#### 修改TblAsnServiceImpl
- **创建ASN时**: 同时创建箱信息记录
- **修改ASN时**: 先删除原有箱信息，再创建新的箱信息
- **删除ASN时**: 级联删除箱信息
- **数据验证**: 验证article总箱数与箱子列表数量一致

### 4. API接口设计

#### 修改TblAsnApi
```java
@GetMapping(value = "/{asnCode}")
public AjaxResult query(@PathVariable("asnCode") String asnCode) {
    TblAsn asn = asnService.selectTblAsnByAsnCode(asnCode);
    // 从箱信息表获取箱标签数据，而非实时计算
    asnService.populateBoxInfoFromDatabase(asn);
    return AjaxResult.success(asn);
}
```

## 数据模型

### 箱信息数据结构
```json
{
  "boxId": 1,
  "articleId": 100,
  "boxNo": "BOX001",
  "batchNo": "BATCH20240101",
  "quantity": 50.0,
  "palletNo": "PLT001",
  "boxLabel": "ASN@ASN001%PO@PO001%ITEM@001%RELEASE@REL001%BATCH@BATCH20240101%BOX@001%BQTY@50%"
}
```

### ASN响应数据结构（保持兼容性）
```json
{
  "asnCode": "ASN001",
  "detail": [
    {
      "articles": [
        {
          "articleNo": "MAT001",
          "packQty": 2,
          "pallet": [
            {
              "label": "PLT001",
              "box": [
                {
                  "label": "BOX001_LABEL",
                  "qty": 50.0
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 错误处理

### 数据一致性错误
- **箱数不匹配**: 当article.packQty与boxes.size()不一致时抛出异常
- **数量不匹配**: 当article.quantity与boxes总数量不一致时抛出异常
- **批次号为空**: 当箱信息中批次号为空时使用article的默认批次号

### API错误处理
- **ASN不存在**: 返回404错误
- **箱信息缺失**: 返回500错误并记录日志
- **数据格式错误**: 返回400错误

## 测试策略

### 单元测试
- **TblAsnBoxService**: 测试箱信息的CRUD操作
- **数据验证**: 测试箱数量和总数量的一致性验证
- **托盘计算**: 测试根据托盘配置自动计算托号的逻辑

### 集成测试
- **ASN创建流程**: 测试创建ASN时同时创建箱信息
- **ASN修改流程**: 测试修改ASN时先删后增的逻辑
- **WMS API**: 测试/api/asn/{asnCode}接口返回正确的箱信息

### 性能测试
- **大批量数据**: 测试处理大量箱信息时的性能
- **并发操作**: 测试并发创建/修改ASN时的数据一致性

## 实现细节

### 箱标签生成逻辑
```java
public String generateBoxLabel(TblAsnBox box, TblAsnArticle article, TblAsnItem item, TblAsn asn) {
    return String.format("ASN@%s%%PO@%s%%ITEM@%s%%RELEASE@%s%%BATCH@%s%%BOX@%s%%BQTY@%s%%",
        asn.getAsnCode(),
        item.getOrderCode(),
        article.getOrderLineNo(),
        article.getDeliveryScheduleNo(),
        box.getBatchNo(),
        box.getBoxNo(),
        box.getQuantity()
    );
}
```

### 托盘号计算逻辑
```java
public void calculatePalletNumbers(List<TblAsnBox> boxes, TblAsnArticle article, TblAsnItem item) {
    // 根据托盘配置和箱数量计算托盘号
    PalletInfoVO palletConfig = tblPalletService.selectPalletConfig(item.getPlantCode(), article.getArticleNo());
    if (palletConfig != null) {
        int boxesPerPallet = palletConfig.getSnp();
        for (int i = 0; i < boxes.size(); i++) {
            int palletIndex = i / boxesPerPallet + 1;
            boxes.get(i).setPalletNo("PLT" + String.format("%03d", palletIndex));
        }
    }
}
```

### 数据迁移策略
```java
public void migrateExistingData() {
    // 为现有的ASN数据生成箱信息记录
    List<TblAsnArticle> articles = tblAsnMapper.selectAllArticles();
    for (TblAsnArticle article : articles) {
        List<TblAsnBox> boxes = generateBoxesFromArticle(article);
        tblAsnBoxService.batchInsertTblAsnBox(boxes);
    }
}
```

## 向后兼容性保障

1. **保持现有API格式**: TblAsnArticle的pallet字段继续存在，但数据来源改为箱信息表
2. **渐进式迁移**: 支持新旧数据格式共存，优先使用箱信息表数据
3. **降级处理**: 当箱信息不存在时，回退到原有的实时计算逻辑
4. **配置开关**: 提供配置项控制是否启用新的箱信息功能