package com.datalink.web.controller.common;

import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.constant.Constants;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.utils.StringUtils;
import com.datalink.common.utils.file.FileUploadUtils;
import com.datalink.common.utils.file.FileUtils;
import com.datalink.datamanage.service.EmailNotificationService;
import com.datalink.framework.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@RestController
public class CommonController
{
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EmailNotificationService emailNotificationService;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
//            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String realFileName = fileName;
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 发送系统上线通知邮件
     */
    @PreAuthorize("@ss.hasRole('admin')")
    @PostMapping("/common/notifySystemUpgrade")
    public AjaxResult notifySystemUpgrade() {
        try {
            String sql = "select su.user_name as 用户名, sua.plain_password as 密码 ,su.email as 邮箱\n" +
                    "from sys_user su \n" +
                    "left join sys_user_account sua on su.user_name = sua.user_name\n" +
                    "where su.del_flag = '0' and su.status = '0' and su.user_id not in (1,2,3) and (sua.sendStatus is null or sua.sendStatus = 'FAIL')";

            List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
            if (rows == null || rows.isEmpty()) {
                return AjaxResult.error("未找到需要通知的有效用户");
            }

            int total = 0;
            for (Map<String, Object> row : rows) {
                String userName = Objects.toString(row.get("用户名"), "");
                String password = Objects.toString(row.get("密码"), "");
                String email = Objects.toString(row.get("邮箱"), "");
                if (StringUtils.isEmpty(email)) {
                    continue;
                }
                total++;
                // 异步发送并在服务中记录发送状态与消息
                emailNotificationService.sendUpgradeNoticeAsync(userName, password, email);
            }

            if (total == 0) {
                return AjaxResult.error("未找到有效邮箱的用户");
            }

            AjaxResult ajax = AjaxResult.success();
            ajax.put("queued", total);
            return ajax;
        }
        catch (Exception e) {
            log.error("发送系统上线通知发生异常", e);
            return AjaxResult.error("发送失败: " + e.getMessage());
        }
    }


    /**
     * PDF下载请求
     *
     * @param fileName 文件名称
     * @param delete 是否删除
     */
    @GetMapping("common/downloadPdf")
    public void pdfDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request)
    {
        try
        {
            if (!FileUtils.checkAllowDownload(fileName))
            {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = RuoYiConfig.getDownloadPath() + fileName;

            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
//            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete)
            {
                FileUtils.deleteFile(filePath);
            }
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/common/shareUpload")
    public AjaxResult shareUploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getShareFolder();
            String orgFileName = file.getOriginalFilename();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.uploadShare(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", orgFileName);
            ajax.put("url", fileName);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception
    {
        try
        {
            if (!FileUtils.checkAllowDownload(resource))
            {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = RuoYiConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        }
        catch (Exception e)
        {
            log.error("下载文件失败", e);
        }
    }
}
