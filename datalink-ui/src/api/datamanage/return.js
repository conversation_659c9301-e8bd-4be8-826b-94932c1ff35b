import request from "@/utils/request";

// 查询列表
export function listReturn(query) {
  return request({
    url: "/datamanage/return/list",
    method: "get",
    params: query,
  });
}
export function getReturnDetail(id) {
  return request({
    url: `/datamanage/return/detail/${id}`,
    method: "get",
  });
}

export function updateBatchNo(id, data) {
  return request({
    url: `/datamanage/return/updateBatchNo/${id}`,
    method: "put",
    data,
  });
}

export function printReturn(returnId) {
  return request({
    url: "/datamanage/return/print",
    method: "get",
    params: {
      returnId,
    },
  });
}

export function sendToWMS(id) {
  return request({
    url: `/datamanage/return/feedback/${id}`,
    method: "post",
  });
}
