<template>
  <div class="nr">
    <el-input
      v-model="innerFrom"
      :placeholder="placeholderFrom"
      :disabled="disabled"
      :clearable="clearable"
      :style="{ width: inputWidth }"
      @input="emitChange"
    />

    <span class="nr__sep">–</span>

    <el-input
      v-model="innerTo"
      :placeholder="placeholderTo"
      :disabled="disabled"
      :clearable="clearable"
      :style="{ width: inputWidth }"
      @input="emitChange"
    />
  </div>
</template>

<script>
export default {
  name: 'NumberRangeInput',
  props: {
    value: { type: Array, default: () => [null, null] }, // v-model: [from, to]
    placeholderFrom: { type: String, default: '' },
    placeholderTo: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: true },
    inputWidth: { type: String, default: '80px' }
  },
  data() {
    return {
      innerFrom: this.value[0],
      innerTo: this.value[1]
    }
  },
  watch: {
    value(val) {
      this.innerFrom = val[0]
      this.innerTo = val[1]
    }
  },
  methods: {
    emitChange() {
      const from = this.parseNum(this.innerFrom)
      const to = this.parseNum(this.innerTo)
      this.$emit('input', [from, to]) // 支持 v-model
      this.$emit('change', [from, to])
    },
    parseNum(v) {
      if (v === '' || v === null || v === undefined) return null
      const n = Number(v)
      return Number.isFinite(n) ? n : v // 允许非数值，交给 el-form 校验
    }
  }
}
</script>

<style scoped>
.nr {
  display: inline-flex;
  align-items: center;
}
.nr__sep {
  margin: 0 6px;
  color: #606266;
  user-select: none;
}
</style>
