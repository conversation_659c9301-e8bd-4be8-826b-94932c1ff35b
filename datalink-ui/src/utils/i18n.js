const titleMap = {
  首页: 'dashboard',
  订单下载: 'orderDownload',
  数据管理: 'dataManagement',
  数据接收: 'dataReceive',
  订单: 'order',
  预测: 'forecast',
  寄售库存: 'consignmentInventory',
  库存: 'inventory',
  验收明细: 'feedback',
  退货: 'return',
  收货反馈: 'receiptFeedback',
  ASN: 'ASN',
  数据发送: 'dataSend',
  系统管理: 'systemManagement',
  用户管理: 'userManagement',
  角色管理: 'roleManagement',
  菜单管理: 'menuManagement',
  供应商管理: 'supplierManagement',
  部门管理: 'supplierManagement',
  岗位管理: 'postManagement',
  字典管理: 'dictionaryManagement',
  参数设置: 'parameterSettings',
  通知公告: 'notice',
  日志管理: 'logManagement',
  操作日志: 'operationLog',
  登录日志: 'loginLog',
  系统监控: 'systemMonitoring',
  在线用户: 'onlineUsers',
  定时任务: 'scheduledTasks',
  数据监控: 'dataMonitoring',
  服务监控: 'serverMonitoring',
  缓存监控: 'cacheMonitoring',
  系统工具: 'systemTools',
  表单构建: 'formBuilder',
  代码生成: 'codeGeneration',
  系统接口: 'systemInterface',
  个人中心: 'profile',
  字典数据: 'dictData',
  调度日志: 'dispatchLog',
  修改生成配置: 'modifyGeneratedConfig',
  订单详情: 'orderDetail',
  预测详情: 'forecastDetail',
  寄售库存详情: 'consignmentInventoryDetail',
  库存详情: 'inventoryDetail',
  收货反馈详情: 'receiptFeedbackDetail',
  ASN详情: 'ASNDetail',
  ASN编辑: 'ASNEdit',
  内部预测: 'internalForecast',
  内示下载: 'internalForecastDownloadNew',
  '生成纳品书/受领书': 'deliveryNoteGenerate',
  票据打印: 'ticketPrint',
  托盘维护: 'palletMaintain',
  订单概览: 'orderOverview',
  采购订单详情: 'purchaseOrderDetail',
  内部预测概览: 'forecastOverview',
  验收明细概览: 'acceptanceDetailOverview',
  验收明细下载: 'acceptanceDownload',
  货量提示配车登记: 'loadProposalVehicleRegistration',
  货量提示配车确认: 'loadProposalVehicleConfirmation',
  供应计划: 'supplyPlan',
  单机能连携: 'kanban',
  '单机能连携数据(生产线)': 'kanbaA8245',
  '单机能连携数据(SV)': 'kanbaM4028',
  '单机能连携数据(KD)': 'kanbaN2396',
  '下载功能': 'downloadFunction',
  '打印功能': 'printFunction',
  '概览': 'overview'
}
// translate router.meta.title, be used in breadcrumb sidebar tagsview
export function generateTitle (title) {
  // 根据中文 title 从 titleMap 中获取对应的英文 key
  const key = titleMap[title]

  // 如果映射表中有对应的英文 key
  if (key) {
    // 检查是否存在对应的国际化翻译
    const hasKey = this.$te(`route.${key}`)
    if (hasKey) {
      // 如果存在翻译，则返回翻译后的值
      const translatedTitle = this.$t(`route.${key}`)
      return translatedTitle
    }
  }

  // 如果没有找到对应的 key 或翻译，则返回原始 title
  return title
}
