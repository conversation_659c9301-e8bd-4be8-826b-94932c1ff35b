<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="$t('system.user.profile.userinfo.nickName')" prop="nickName">
      <el-input v-model="user.nickName" />
    </el-form-item>
    <el-form-item :label="$t('system.user.profile.userinfo.phoneNumber')" prop="phonenumber">
      <el-input v-model="user.phonenumber" maxlength="11" />
    </el-form-item>
    <el-form-item :label="$t('system.user.profile.userinfo.email')" prop="email">
      <el-input v-model="user.email" maxlength="50" />
    </el-form-item>
    <el-form-item :label="$t('system.user.profile.userinfo.gender')">
      <el-radio-group v-model="user.sex">
        <el-radio :label="0">{{ $t('system.user.profile.userinfo.male') }}</el-radio>
        <el-radio :label="1">{{ $t('system.user.profile.userinfo.female') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{ $t('system.user.profile.userinfo.save') }}</el-button>
      <el-button type="danger" size="mini" @click="close">{{ $t('system.user.profile.userinfo.close') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserProfile } from "@/api/system/user";

export default {
  props: {
    user: {
      type: Object
    }
  },
  data() {
    return {
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: this.$t('system.user.profile.userinfo.nickNameRequired'), trigger: "blur" }
        ],
        email: [
          { required: true, message: this.$t('system.user.profile.userinfo.emailRequired'), trigger: "blur" },
          {
            type: "email",
            message: this.$t('system.user.profile.userinfo.invalidEmail'),
            trigger: ["blur", "change"]
          }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserProfile(this.user).then(response => {
            this.msgSuccess(this.$t('system.user.profile.userinfo.success'));
          });
        }
      });
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/index" });
    }
  }
};
</script>
