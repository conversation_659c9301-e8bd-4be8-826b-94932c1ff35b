<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="mb8">
      <el-form-item :label="$t('orderOverview.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('orderOverview.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('orderOverview.form.plant')" prop="plant">
        <el-input
          v-model="queryParams.plant"
          :placeholder="$t('orderOverview.placeholder.plant')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('orderOverview.form.dateRange')">
        <el-date-picker
          v-model="daterange"
          size="small"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          format="yyyy-MM-dd"
          :start-placeholder="$t('orderOverview.placeholder.startDate')"
          :end-placeholder="$t('orderOverview.placeholder.endDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('feedback.search')}}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('feedback.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="orderList"
      border
      stripe
      ref="orderTable"
      style="width: 100%"
    >
      <el-table-column :label="$t('orderOverview.table.suppCode')" prop="suppCode" align="center" />
      <el-table-column :label="$t('orderOverview.table.suppName')" prop="suppName" align="center" />
      <el-table-column :label="$t('orderOverview.table.orderNo')" prop="orderNo" align="center" />
      <el-table-column :label="$t('orderOverview.table.plant')" prop="plant" align="center" />
      <el-table-column :label="$t('orderOverview.table.operateTime')" prop="operateTime" align="center" />
      <el-table-column :label="$t('orderOverview.table.operateType')" prop="operateType" align="center" />
      <el-table-column :label="$t('orderOverview.table.operator')" prop="operator" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "orderOverview",
  data() {
    return {
      loading: true,
      orderList: [],
      total: 0,
      daterange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: '',
        plant: '',
        startDate: '',
        endDate: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.orderList = [
          {
            suppCode: 'S001',
            suppName: '供应商A',
            orderNo: 'ORD20240530001',
            plant: '工厂A',
            operateTime: '2025-05-30 10:00',
            operateType: '创建',
            operator: '张三'
          },
          {
            suppCode: 'S002',
            suppName: '供应商B',
            orderNo: 'ORD20240530002',
            plant: '工厂B',
            operateTime: '2025-05-30 11:00',
            operateType: '修改',
            operator: '李四'
          }
        ];
        this.total = this.orderList.length;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
