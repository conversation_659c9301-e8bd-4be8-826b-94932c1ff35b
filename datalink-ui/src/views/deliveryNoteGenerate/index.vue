<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item :label="$t('deliveryNoteGenerate.form.orderNo')" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          :placeholder="$t('deliveryNoteGenerate.placeholder.orderNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('deliveryNoteGenerate.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('deliveryNoteGenerate.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('deliveryNoteGenerate.form.shipStatus')" prop="shipStatus">
        <el-select v-model="queryParams.shipStatus" :placeholder="$t('deliveryNoteGenerate.placeholder.shipStatus')" clearable size="small">
          <el-option v-for="dict in shipStatusOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('deliveryNoteGenerate.form.dateRange')">
        <el-date-picker
          v-model="daterange"
          size="small"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          format="yyyy-MM-dd"
          :start-placeholder="$t('deliveryNoteGenerate.placeholder.startTime')"
          :end-placeholder="$t('deliveryNoteGenerate.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('feedback.search')}}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('feedback.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-document" size="mini" :loading="generateLoading" @click="handleGenerateSelected">{{$t('deliveryNoteGenerate.button.generate')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" :loading="printLoading" @click="handlePrintSelected">{{$t('deliveryNoteGenerate.button.print')}}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="deliveryList"
      border
      stripe
      @selection-change="handleSelectionChange"
      ref="deliveryTable"
    >
      <el-table-column :label="$t('deliveryNoteGenerate.table.index')" type="index" width="50" align="center" />
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.action')" width="180" align="center">
        <template slot-scope="scope">
          <template v-if="editRowIndex === scope.$index">
            <el-button type="primary" size="mini" @click="saveEdit(scope.$index)">{{$t('deliveryNoteGenerate.button.save')}}</el-button>
            <el-button size="mini" @click="cancelEdit">{{$t('deliveryNoteGenerate.button.cancel')}}</el-button>
          </template>
          <template v-else>
            <el-button type="text" size="mini" @click="editRow(scope.$index)">{{$t('deliveryNoteGenerate.button.edit')}}</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('deliveryNoteGenerate.table.orderNo')" prop="orderNo" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.lineNo')" prop="lineNo" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.releaseNo')" prop="releaseNo" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.partNo')" prop="partNo" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.partDesc')" prop="partDesc" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.orderQty')" prop="orderQty" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.deliveryQty')" prop="deliveryQty" align="center" width="160">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model.number="editCache.deliveryQty" size="small" type="number" min="0" />
          <el-input v-else v-model="scope.row.deliveryQty" size="small" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('deliveryNoteGenerate.table.totalDeliveryQty')" prop="totalDeliveryQty" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.qtyPerPack')" prop="qtyPerPack" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.deliveryDate')" prop="deliveryDate" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.plant')" prop="plant" align="center" />
      <el-table-column :label="$t('deliveryNoteGenerate.table.unit')" prop="unit" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "deliveryNoteGenerate",
  data() {
    return {
      loading: true,
      generateLoading: false,
      printLoading: false,
      ids: [],
      shipStatusOptions: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      deliveryList: [],
      title: "",
      open: false,
      daterange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        suppCode: null,
        shipStatus: null,
        orderByColumn: "",
        isAsc: "",
        startTime: null,
        endTime: null
      },
      editRowIndex: null,
      editCache: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.deliveryList = [
          {
            orderNo: 'PO20240528001',
            lineNo: '1',
            releaseNo: 'RL20240528001',
            partNo: 'P001',
            partDesc: '零件A',
            orderQty: 100,
            deliveryQty: 50,
            totalDeliveryQty: 200,
            qtyPerPack: 10,
            deliveryDate: '2025-05-28',
            plant: '工厂A',
            unit: 'PCS'
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.orderNo + '-' + item.lineNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleGenerateSelected() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('deliveryNoteGenerate.alert.selectRow'));
        return;
      }
      this.generateLoading = true;
      setTimeout(() => {
        this.generateLoading = false;
        this.$message.success(this.$t('deliveryNoteGenerate.alert.generateSuccess'));
      }, 1000);
    },
    handlePrintSelected() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('deliveryNoteGenerate.alert.selectRow'));
        return;
      }
      this.printLoading = true;
      setTimeout(() => {
        this.printLoading = false;
        this.$message.success(this.$t('deliveryNoteGenerate.alert.printSuccess'));
      }, 1000);
    },
    editRow(index) {
      if (this.editRowIndex !== null) {
        this.$message.warning(this.$t('deliveryNoteGenerate.alert.saveBeforeEdit'));
        return;
      }
      this.editRowIndex = index;
      this.editCache = { ...this.deliveryList[index] };
    },
    saveEdit(index) {
      this.$set(this.deliveryList, index, { ...this.editCache });
      this.editRowIndex = null;
      this.editCache = {};
      this.$message.success(this.$t('deliveryNoteGenerate.alert.saveSuccess'));
    },
    cancelEdit() {
      this.editRowIndex = null;
      this.editCache = {};
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
