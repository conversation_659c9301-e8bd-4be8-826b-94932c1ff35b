<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item :label="$t('acceptanceDownload.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('acceptanceDownload.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('acceptanceDownload.form.status')" prop="status">
        <el-select v-model="queryParams.status" :placeholder="$t('acceptanceDownload.placeholder.status')" clearable size="small">
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('acceptanceDownload.form.dateRange')">
        <el-date-picker
          v-model="daterange"
          size="small"
          value-format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          range-separator="-"
          format="yyyy-MM-dd HH:mm"
          :start-placeholder="$t('acceptanceDownload.placeholder.startTime')"
          :end-placeholder="$t('acceptanceDownload.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('feedback.search')}}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('feedback.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-download" size="mini" :loading="downloadLoading" @click="handleDownloadSelected">{{$t('acceptanceDownload.button.downloadSelectedRows')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-check" size="mini" :loading="confirmLoading" @click="handleConfirmSelected">{{$t('acceptanceDownload.button.confirmSelectedRows')}}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="acceptanceList"
      border
      stripe
      @selection-change="handleSelectionChange"
      ref="acceptanceTable"
    >
      <el-table-column :label="$t('acceptanceDownload.table.index')" type="index" width="50" align="center" />
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.action')" width="180" align="center">
        <template slot-scope="scope">
          <template v-if="editRowIndex === scope.$index">
            <el-button type="primary" size="mini" @click="saveEdit(scope.$index)">{{$t('acceptanceDownload.button.save')}}</el-button>
            <el-button size="mini" @click="cancelEdit">{{$t('acceptanceDownload.button.cancel')}}</el-button>
          </template>
          <template v-else>
            <el-button type="text" size="mini" @click="editRow(scope.$index)">{{$t('acceptanceDownload.button.edit')}}</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('acceptanceDownload.table.settlementCode')" prop="settlementCode" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.suppName')" prop="suppName" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.compCode')" prop="compCode" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.status')" prop="status" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.invoiceTotalTax')" prop="invoiceTotalTax" align="center" width="180">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model="editCache.invoiceTotalTax" size="small" />
          <el-input v-else v-model="scope.row.invoiceTotalTax" size="small" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('acceptanceDownload.table.invoiceNo')" prop="invoiceNo" align="center" width="180">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model="editCache.invoiceNo" size="small" />
          <el-input v-else v-model="scope.row.invoiceNo" size="small" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('acceptanceDownload.table.invoiceDate')" prop="invoiceDate" align="center" width="250">
        <template slot-scope="scope">
          <el-date-picker v-if="editRowIndex === scope.$index" v-model="editCache.invoiceDate" type="date" size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd" />
          <el-date-picker v-else v-model="scope.row.invoiceDate" type="date" size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('acceptanceDownload.table.invoiceAmount')" prop="invoiceAmount" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.currency')" prop="currency" align="center" />
      <el-table-column :label="$t('acceptanceDownload.table.receiveDate')" prop="receiveDate" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "acceptanceDownload",
  data() {
    return {
      loading: true,
      downloadLoading: false,
      confirmLoading: false,
      ids: [],
      statusOptions: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      acceptanceList: [],
      title: "",
      open: false,
      daterange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: null,
        status: null,
        orderByColumn: "",
        isAsc: "",
        startTime: null,
        endTime: null
      },
      editRowIndex: null,
      editCache: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.acceptanceList = [
          {
            settlementCode: 'JS20240527001',
            suppName: '供应商A',
            compCode: 'C001',
            status: '已验收',
            invoiceTotalTax: 1000,
            invoiceNo: 'INV001',
            invoiceDate: '2025-05-27',
            invoiceAmount: 1000,
            currency: 'CNY',
            receiveDate: '2025-05-27'
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.settlementCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleDownloadSelected() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('acceptanceDownload.alert.selectRow'));
        return;
      }
      this.downloadLoading = true;
      setTimeout(() => {
        this.downloadLoading = false;
        this.$message.success(this.$t('acceptanceDownload.alert.downloadSuccess'));
      }, 1000);
    },
    handleConfirmSelected() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('acceptanceDownload.alert.selectRow'));
        return;
      }
      this.confirmLoading = true;
      setTimeout(() => {
        this.confirmLoading = false;
        this.$message.success(this.$t('acceptanceDownload.alert.confirmSuccess'));
      }, 1000);
    },
    editRow(index) {
      if (this.editRowIndex !== null) {
        this.$message.warning(this.$t('acceptanceDownload.alert.saveBeforeEdit'));
        return;
      }
      this.editRowIndex = index;
      this.editCache = { ...this.acceptanceList[index] };
    },
    saveEdit(index) {
      this.$set(this.acceptanceList, index, { ...this.editCache });
      this.editRowIndex = null;
      this.editCache = {};
      this.$message.success(this.$t('acceptanceDownload.alert.saveSuccess'));
    },
    cancelEdit() {
      this.editRowIndex = null;
      this.editCache = {};
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
