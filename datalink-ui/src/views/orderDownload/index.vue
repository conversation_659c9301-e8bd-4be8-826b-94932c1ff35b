<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
    <el-form-item :label="$t('orderDownload.form.suppCode')" prop="suppCode">
      <el-input
        v-model="queryParams.suppCode"
        :placeholder="$t('orderDownload.placeholder.suppCode')"
        clearable
        size="small"
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>

    <el-form-item :label="$t('asn.status')" prop="kafkaStatus">
      <el-select v-model="queryParams.kafkaStatus" :placeholder="$t('asn.status')" clearable size="small">
        <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel"
          :value="dict.dictValue" />
      </el-select>
    </el-form-item>

    <el-form-item :label="$t('orderDownload.form.createTime')">
      <el-date-picker
        v-model="daterangeCreateTime"
        size="small"
        value-format="yyyy-MM-dd HH:mm"
        type="datetimerange"
        range-separator="-"
        format="yyyy-MM-dd HH:mm"
        :start-placeholder="$t('orderDownload.placeholder.startTime')"
        :end-placeholder="$t('orderDownload.placeholder.endTime')"
      />
    </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("feedback.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("feedback.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" :loading="printTxtLoading"
          @click="handlePrintTxt">{{
            $t('orderDownload.button.printSelectedRows') }}</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" :loading="printTxtLoading"
          @click="confirmSelectedRows">{{
            $t('orderDownload.button.confirmSelectedRows') }}</el-button>
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="feedbackList"
      @selection-change="handleSelectionChange"
    >
    <el-table-column
    :label="$t('orderDownload.table.index')"
    type="index"
    width="50"
    align="center"
  />
    <el-table-column type="selection" width="55" align="center" />

      <el-table-column
        :label="$t('orderDownload.table.createTime')"
        align="center"
        prop="createTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}')
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('orderDownload.table.suppCode')"
        align="center"
        prop="suppCode"
      />
      <el-table-column
        :label="$t('orderDownload.table.plantCode')"
        align="center"
        prop="plantCode"
      />
      <el-table-column
        :label="$t('orderDownload.table.status')"
        align="center"
        prop="status"
      />
      <el-table-column
        :label="$t('orderDownload.table.downloadingStatus')"
        align="center"
        prop="kafkaStatus"
      />
      <el-table-column
        :label="$t('orderDownload.table.lastDownloadingTime')"
        align="center"
        prop="lastDownloadingTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.lastDownloadingTime, '{y}-{m}-{d} {h}:{i}')
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import {
  listFeedback,
  printTxt
} from "@/api/datamanage/feedback";


export default {
  name: "orderDownload",
  data() {
    return {
      // 遮罩层
      loading: true,
      printTxtLoading: false,
      // 选中数组
      ids: [],
      statusOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货反馈表格数据
      feedbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: null,
        kafkaStatus: null,
        orderByColumn: "",
        isAsc: "",
      }
    };
  },
  created() {
    // if (this.$route.path.toLowerCase().endsWith('recv/feedback')) {
    //   this.queryParams.direction = 'I';
    // } else {
    //   this.queryParams.direction = 'O';
    // }
    this.getList();
  },

  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user)
      const roles = this.$store.state.user.roles;
      const remark = this.$store.state.user.remark;
      return roles && roles.some(role => role === 'carrier') && remark;
    }
  },
  methods: {
    /** 查询收货反馈列表 */
    getList() {
      this.loading = true;
      listFeedback(this.queryParams).then((response) => {
        this.feedbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.feedId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    confirmSelectedRows(){
    },
    handlePrintTxt() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('feedback.alert.selectOrder'))
        return
      }
      this.printTxtLoading = true;

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close();
      }, 15000);

      console.log(this.ids)
      printTxt(this.ids).then(response => {
        // 通过 window.open 打开 PDF
        window.open(process.env.VUE_APP_BASE_API + "/common/download?fileName=" + encodeURI(response.msg) + "&delete=true");
        loading.close()
        this.printTxtLoading = false;
      }).catch(() => {
        loading.close()
        this.printTxtLoading = false;  // 如果请求失败，重置 loading 状态

      });
    },
  },
};
</script>
