<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item :label="$t('return.returnNo')" prop="returnNo">
        <el-input
          v-model="queryParams.returnNo"
          :placeholder="$t('return.returnNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('return.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('return.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >{{ $t("return.button.search") }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("return.button.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          plain
          :loading="printLoading"
          @click="handlePrint"
        >{{
          $t('return.button.print') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          :loading="confirmLoading"
          @click="handleConfirm"
        >{{ $t('return.button.send') }}
        </el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        @queryTable="getList"
      />
    </el-row>

    <el-table
      v-loading="loading"
      :data="returnList"
      @row-click="handleRowClick"
    >
      <el-table-column width="50" align="center">
        <template slot-scope="{row}">
          <el-radio v-model="selectedReturnId" inert :label="row.returnId" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('return.returnNo')" align="center" prop="returnNo">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'ReturnDetail',
              params: { returnId: scope.row.returnId },
            }"
            class="link-type"
          >
            <span>{{ scope.row.returnNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('return.suppCode')" align="center" prop="suppCode" />
      <el-table-column :label="$t('return.status')" align="center" prop="status" />
      <el-table-column :label="$t('return.createTime')" align="center" prop="createTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('return.actionsText')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status !== 'Completed'"
            v-hasPermi="['datamanage:return:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row.returnId)"
          >{{ $t('return.button.edit') }}</el-button>
        </template>

      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listReturn,
  printReturn,
  sendToWMS
} from '@/api/datamanage/return'

export default {
  name: 'Return',
  data() {
    return {
      // 遮罩层
      loading: true,
      printLoading: false,
      confirmLoading: false,
      selectedReturnId: null,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      returnList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        returnNo: null,
        suppCode: null,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      }
    }
  },

  computed: {
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询收货反馈列表 */
    getList() {
      this.loading = true
      listReturn(this.queryParams).then((response) => {
        this.returnList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleRowClick(row) {
      this.selectedReturnId = row.returnId
    },
    /** 导出按钮操作 */
    handlePrint() {
      if (this.selectedReturnId === null) {
        this.$alert(this.$t('return.confirm.atLeastOneRequired'))
        return
      }

      const currentReturn = this.returnList.find(el => el.returnId === this.selectedReturnId)
      if (!currentReturn) return

      if (currentReturn.status !== 'Completed') {
        this.$alert(this.$t('return.alert.draftCanNotPrint'))
        return
      }
      this.printLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printReturn(this.selectedReturnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printLoading = false
        })
        .catch(() => {
          loading.close()
          this.printLoading = false
        })
    },
    async handleConfirm() {
      if (this.selectedReturnId === null) {
        this.$alert(this.$t('return.confirm.atLeastOneRequired'))
        return
      }

      const currentReturn = this.returnList.find(el => el.returnId === this.selectedReturnId)
      if (!currentReturn) return

      if (currentReturn.status === 'Completed') {
        this.$alert(this.$t('return.alert.canNotConfirm'))
        return
      }

      this.confirmLoading = true
      try {
        await sendToWMS(this.selectedReturnId)
        await this.getList()
        this.confirmLoading = false
        this.msgSuccess(this.$t('return.message.sendSuccess'))
      } catch (error) {
        this.confirmLoading = false
      }
    },
    handleUpdate(returnId) {
      this.$router.push({ name: 'ReturnEdit', params: { returnId }})
    }
  }
}
</script>

<style scoped>
.el-table {
  .el-radio {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
