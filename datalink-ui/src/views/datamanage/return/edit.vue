<template>
  <div class="app-container">
    <el-form ref="infoForm" :model="form" :rules="rules" label-width="auto">
      <el-card shadow="never" class="border-none margin-t24">
        <div slot="header">
          {{ $t("return.detail.baseInfoTitle") }}
        </div>

        <el-row :gutter="20">
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('return.returnNo')" prop="returnNo">
              <el-input v-model="form.returnNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('return.suppCode')" prop="suppCode">
              <el-input v-model="form.suppCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('return.status')" prop="status">
              <el-input v-model="form.status" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('return.createTime')" prop="createTime">
              <el-input v-model="form.createTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('return.detail.batchNo')" prop="batchNo">
              <el-date-picker v-model="form.batchNo" clearable type="date" value-format="yyyyMMdd" format="yyyyMMdd" style="width: 240px;" :editable="false" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div v-if="returnInfo.returnId" style="margin-top: 10px; text-align: center; margin-bottom: 10px">
      <template v-if="returnInfo.status === 'Completed'">
        <el-button
          type="primary"
          icon="el-icon-download"
          :loading="printLoading"
          @click="handlePrint"
        >{{
          $t('return.button.print') }}</el-button>
      </template>
      <template v-if="returnInfo.status !== 'Completed'">
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleSave"
        >{{ $t('return.button.save') }}
        </el-button>
      </template>
      <template v-if="returnInfo.status !== 'Completed'">
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >{{ $t('return.button.send') }}
        </el-button>
      </template>
    </div>

    <el-card shadow="never" class="border-none">
      <div slot="header">
        {{ $t("return.detail.boxInfoTitle") }}
      </div>
      <el-table
        v-loading="loading"
        :data="returnInfo.boxList"
      >
        <el-table-column :label="$t('return.detail.asnCode')" align="center" prop="asnCode" />
        <el-table-column :label="$t('return.detail.orderCode')" align="center" prop="orderCode" />
        <el-table-column :label="$t('return.detail.itemNo')" align="center" prop="itemNo" />
        <el-table-column :label="$t('return.detail.releaseNo')" align="center" prop="releaseNo" />
        <el-table-column :label="$t('return.detail.boxIndex')" align="center" prop="boxIndex" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import {
  printReturn,
  sendToWMS,
  updateBatchNo,
  getReturnDetail
} from '@/api/datamanage/return'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

export default {
  name: 'ReturnEdit',
  data() {
    return {
      returnInfo: {},
      loading: true,
      printLoading: false,
      confirmLoading: false,
      form: {
        returnNo: '',
        suppCode: '',
        status: '',
        createTime: null,
        batchNo: null
      },
      rules: {
        batchNo: [
          { required: true, message: this.$t('return.edit.validation.batchNoRequired'), trigger: 'blur' }
        ]
      }

    }
  },
  created() {
    this.getDetail()
  },

  methods: {
    getDetail() {
      const returnId = this.$route.params && this.$route.params.returnId
      if (returnId) {
        getReturnDetail(returnId).then((res) => {
          this.returnInfo = res.data
          let batchNo = res.data.boxList?.[0]?.newBatchNo
          const tmpBatchNo = dayjs(batchNo).format('YYYYMMDD')
          // Skip batches not in YYYYMMDD format.
          if (tmpBatchNo !== batchNo) {
            batchNo = null
          }
          this.form = {
            returnNo: res.data.returnNo,
            suppCode: res.data.suppCode,
            status: res.data.status,
            createTime: res.data.createTime,
            batchNo
          }
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handlePrint() {
      this.printLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printReturn(this.returnInfo.returnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printLoading = false
        })
        .catch(() => {
          loading.close()
          this.printLoading = false
        })
    },
    async handleConfirm() {
      this.$refs['infoForm'].validate(async(valid) => {
        if (valid) {
          this.confirmLoading = true
          try {
            await updateBatchNo(this.returnInfo.returnId, this.returnInfo.boxList.map(box => ({ ...box, newBatchNo: this.form.batchNo })))
            await sendToWMS(this.returnInfo.returnId)
            this.confirmLoading = false
            this.getDetail()
            this.msgSuccess(this.$t('return.message.saveSuccess'))
          } catch (error) {
            this.confirmLoading = false
          }
        }
      })
    },
    async handleSave() {
      this.$refs['infoForm'].validate(async(valid) => {
        if (valid) {
          this.confirmLoading = true
          try {
            await updateBatchNo(this.returnInfo.returnId, this.returnInfo.boxList.map(box => ({ ...box, newBatchNo: this.form.batchNo })))
            this.confirmLoading = false
            this.getDetail()
            this.msgSuccess(this.$t('return.message.saveSuccess'))
          } catch (error) {
            this.confirmLoading = false
          }
        }
      })
    }
  }
}
</script>

<style scoped></style>
