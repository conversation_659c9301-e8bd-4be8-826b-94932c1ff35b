<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t("return.detail.baseInfoTitle") }}
      </div>
      <el-table border :show-header="false" :data="baseTableData">
        <el-table-column
          prop="title1"
          width="185"
        />

        <el-table-column min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          width="185"
        />

        <el-table-column min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div v-if="returnInfo.returnId" style="margin-top: 10px; text-align: center; margin-bottom: 10px">
      <template v-if="returnInfo.status === 'Completed'">
        <el-button
          type="primary"
          icon="el-icon-download"
          :loading="printLoading"
          @click="handlePrint"
        >{{
          $t('return.button.print') }}</el-button>
      </template>
      <template v-if="returnInfo.status !== 'Completed'">
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >{{ $t('return.button.send') }}
        </el-button>
      </template>
    </div>

    <el-card shadow="never" class="border-none">
      <div slot="header">
        {{ $t("return.detail.boxInfoTitle") }}
      </div>
      <el-table
        v-loading="loading"
        :data="returnInfo.boxList"
      >
        <el-table-column :label="$t('return.detail.asnCode')" align="center" prop="asnCode" />
        <el-table-column :label="$t('return.detail.orderCode')" align="center" prop="orderCode" />
        <el-table-column :label="$t('return.detail.itemNo')" align="center" prop="itemNo" />
        <el-table-column :label="$t('return.detail.releaseNo')" align="center" prop="releaseNo" />
        <el-table-column :label="$t('return.detail.boxIndex')" align="center" prop="boxIndex" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import {
  printReturn,
  sendToWMS,
  getReturnDetail
} from '@/api/datamanage/return'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

export default {
  name: 'ReturnDetail',
  data() {
    return {
      returnInfo: {},
      loading: true,
      printLoading: false,
      confirmLoading: false
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData: function() {
      return [
        {
          title1: this.$t('return.returnNo'),
          content1: this.returnInfo.returnNo,
          title2: this.$t('return.suppCode'),
          content2: this.returnInfo.suppCode
        },
        {
          title1: this.$t('return.status'),
          content1: this.returnInfo.status,
          title2: this.$t('return.createTime'),
          content2: dayjs(this.returnInfo.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
          title1: this.$t('return.detail.batchNo'),
          content1: this.returnInfo.boxList?.[0]?.newBatchNo
        }
      ]
    }
  },
  created() {
    const returnId = this.$route.params && this.$route.params.returnId
    if (returnId) {
      getReturnDetail(returnId).then((res) => {
        this.returnInfo = res.data
      }).finally(() => {
        this.loading = false
      })
    }
  },

  methods: {
    handlePrint() {
      this.printLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printReturn(this.returnInfo.returnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printLoading = false
        })
        .catch(() => {
          loading.close()
          this.printLoading = false
        })
    },
    async handleConfirm() {
      this.confirmLoading = true
      try {
        await sendToWMS(this.returnInfo.returnId)
        await this.getList()
        this.confirmLoading = false
        this.msgSuccess(this.$t('order.message.sendSuccess'))
      } catch (error) {
        this.confirmLoading = false
      }
    }
  }
}
</script>

<style scoped></style>
