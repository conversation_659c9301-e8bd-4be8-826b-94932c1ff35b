<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true" class="mb8">
      <el-form-item :label="$t('palletMaintain.form.plantCode')" prop="plantCode">
        <el-input v-model="queryParams.plantCode" :placeholder="$t('palletMaintain.placeholder.plantCode')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('palletMaintain.form.materialCode')" prop="materialCode">
        <el-input v-model="queryParams.materialCode" :placeholder="$t('palletMaintain.placeholder.materialCode')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('palletMaintain.button.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('palletMaintain.button.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="palletList"
    >
      <el-table-column :label="$t('palletMaintain.table.plantCode')" prop="plantCode" align="center" />
      <el-table-column :label="$t('palletMaintain.table.materialCode')" min-width="150" prop="materialCode" align="center" />
      <el-table-column :label="$t('palletMaintain.table.partDesc')" min-width="150" prop="materialName" align="center" />
      <el-table-column :label="$t('palletMaintain.table.qtyPerPack')" prop="packQuantity" align="center" />
      <el-table-column :label="$t('palletMaintain.table.palletSnpQty')" min-width="150" prop="snpQuantity" align="center" />
      <el-table-column :label="$t('palletMaintain.table.palletLength')" prop="palletLength" align="center" />
      <el-table-column :label="$t('palletMaintain.table.palletWidth')" prop="palletWidth" align="center" />
      <el-table-column :label="$t('palletMaintain.table.palletHeight')" prop="palletHeight" align="center" />
      <el-table-column :label="$t('palletMaintain.table.containerType')" prop="containerType" align="center" />
      <el-table-column :label="$t('palletMaintain.table.action')" width="120" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="editRowPalletInfo(scope.row.materialPlantId)">{{ $t('palletMaintain.button.edit') }}</el-button>
          <template v-if="scope.row.palletId">
            <el-button type="text" size="mini" @click="deletePallet(scope.row.palletId)">{{ $t('palletMaintain.button.delete') }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改托盘维护对话框 -->
    <el-dialog :title="$t('palletMaintain.updatePalletInfo')" :visible.sync="open" width="500px" append-to-body @close="cancelEdit">
      <el-form :model="editCache" label-width="140px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('palletMaintain.table.palletSnpQty')" prop="snpQuantity">
              <el-input-number v-model="editCache.snpQuantity" :precision="0" :controls="false" :placeholder="$t('palletMaintain.table.palletSnpQtyPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('palletMaintain.table.palletLength')" :precision="0" prop="palletLength">
              <el-input-number v-model="editCache.palletLength" :precision="0" :controls="false" :placeholder="$t('palletMaintain.table.palletLengthPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('palletMaintain.table.palletWidth')" :precision="0" prop="palletWidth">
              <el-input-number v-model="editCache.palletWidth" :precision="0" :controls="false" :placeholder="$t('palletMaintain.table.palletWidthPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('palletMaintain.table.palletHeight')" :precision="0" prop="palletHeight">
              <el-input-number v-model="editCache.palletHeight" :precision="0" :controls="false" :placeholder="$t('palletMaintain.table.palletHeightPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('palletMaintain.table.containerType')" prop="containerType">
              <el-input v-model="editCache.containerType" :placeholder="$t('palletMaintain.table.containerTypePlaceholder')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveEdit">{{
          $t("feedback.confirm")
        }}</el-button>
        <el-button @click="cancelEdit">{{ $t("feedback.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPallet, upsertPallet, deletePallet } from '@/api/datamanage/pallet'

export default {
  name: 'PalletMaintain',
  data() {
    return {
      loading: true,
      palletList: [],
      total: 0,
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        plantCode: null,
        materialCode: null
      },
      editMaterialId: null,
      editCache: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      this.loading = true
      listPallet(this.queryParams).then(response => {
        this.palletList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    editRowPalletInfo(materialPlantId) {
      const current = this.palletList.find(item => item.materialPlantId === materialPlantId)
      if (current) {
        this.editMaterialId = materialPlantId
        this.editCache = {
          ...current,
          snpQuantity: current.snpQuantity === null ? undefined : current.snpQuantity,
          palletLength: current.palletLength === null ? undefined : current.palletLength,
          palletWidth: current.palletWidth === null ? undefined : current.palletWidth,
          palletHeight: current.palletHeight === null ? undefined : current.palletHeight
        }
        this.open = true
      }
    },
    saveEdit() {
      if (this.editCache) {
        upsertPallet(this.editCache)
          .then(() => {
            this.open = false
            this.editMaterialId = null
            this.editCache = {}
            this.msgSuccess(this.$t('palletMaintain.message.upsertSuccess'))
            this.getList()
          }).catch(() => {})
      }
    },
    cancelEdit() {
      this.open = false
      this.editMaterialId = null
      this.editCache = {}
    },
    deletePallet(palletId) {
      if (!palletId) return
      this.$confirm(
        this.$t('palletMaintain.confirm.deletePallet'),
        '',
        {
          confirmButtonText: this.$t('palletMaintain.button.confirm'),
          cancelButtonText: this.$t('palletMaintain.button.cancel'),
          type: 'warning'
        })
        .then(() => {
          deletePallet(palletId).then(() => {
            this.getList()
          }).catch(() => { })
        })
    }
  }
}
</script>

<style scoped>
.el-input-number {
  width: 100%
}

::v-deep .el-input-number input.el-input__inner {
  text-align: left;
}
</style>
