<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item :label="$t('forecast.form.forecastCode')" prop="forecastCode">
        <el-input
          v-model="queryParams.forecastCode"
          :placeholder="$t('forecast.form.inputForecastCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('forecast.form.version')" prop="version">
        <el-input
          v-model="queryParams.version"
          :placeholder="$t('forecast.form.inputVersion')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item v-if="queryParams.direction === 'I'" :label="$t('forecast.form.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('forecast.form.inputCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('forecast.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('forecast.form.inputSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('forecast.form.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('forecast.form.inputSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.form.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          :placeholder="$t('forecast.form.inputPlantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.form.plantName')" prop="plantName">
        <el-input
          v-model="queryParams.plantName"
          :placeholder="$t('forecast.form.inputPlantName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.table.createTime')">
        <el-date-picker
          v-model="dateRangeCreateTime"
          size="small"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          :start-placeholder="$t('forecast.form.startDate')"
          :end-placeholder="$t('forecast.form.endDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('forecast.form.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('forecast.form.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['datamanage:forecast:export']"
          type="primary"
          :loading="excelDownloading"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleDownloadExcel"
        >{{ $t('forecast.form.downloadExcel') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          :loading="confirmLoading"
          @click="forecastConfirm"
        >{{ $t('forecast.form.forecastConfirm') }}
        </el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table ref="forecastTableRef" v-loading="loading" :data="forecastList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('forecast.table.forecastCode')" align="center">
        <template slot-scope="scope">
          <router-link :to="{name: 'ForecastDetail', params: {forecastId: scope.row.forecastId}}" class="link-type">
            <span>{{ scope.row.forecastCode }}</span>
          </router-link>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('forecast.table.version')" align="center" prop="version" /> -->
      <el-table-column v-if="queryParams.direction === 'I'" :label="$t('forecast.table.compCode')" align="center" prop="compCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('forecast.table.suppCode')" align="center" prop="suppCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('forecast.table.suppName')" align="center" prop="suppName" />
      <el-table-column :label="$t('forecast.table.plantCode')" align="center" prop="plantCode" />
      <el-table-column :label="$t('forecast.table.plantName')" align="center" prop="plantName" />
      <el-table-column :label="$t('forecast.table.status')" align="center" prop="status" />
      <el-table-column :label="$t('forecast.table.downloadStatus')" align="center" prop="downloadStatus" />
      <el-table-column :label="$t('forecast.table.lastDownloadTime')" align="center" prop="lastDownloadTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastDownloadTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('forecast.table.createTime')" align="center" prop="createTime" width="154">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="$t('forecast.dialog.title')" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('forecast.dialog.forecastCode')" prop="forecastcode">
          <el-input v-model="form.forecastcode" :placeholder="$t('forecast.dialog.inputForecastCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.version')" prop="version">
          <el-input v-model="form.version" :placeholder="$t('forecast.dialog.inputVersion')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.compCode')" prop="compcode">
          <el-input v-model="form.compcode" :placeholder="$t('forecast.dialog.inputCompCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.plantCode')" prop="plantcode">
          <el-input v-model="form.plantcode" :placeholder="$t('forecast.dialog.inputPlantCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.plantName')" prop="plantname">
          <el-input v-model="form.plantname" :placeholder="$t('forecast.dialog.inputPlantName')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.suppCode')" prop="suppcode">
          <el-input v-model="form.suppcode" :placeholder="$t('forecast.dialog.inputSuppCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.suppName')" prop="suppname">
          <el-input v-model="form.suppname" :placeholder="$t('forecast.dialog.inputSuppName')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('forecast.dialog.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('forecast.dialog.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listForecast, downloadForecastExcel, delForecast, addForecast, updateForecast, confirmForecast } from '@/api/datamanage/forecast'
import { commonDownloadFile } from '@/api/datamanage/common'
import { formatNow } from '@/utils/index'

export default {
  name: 'Forecast',
  data() {
    return {
      // 遮罩层
      loading: true,
      excelDownloading: false,
      confirmLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预测表格数据
      forecastList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      dateRangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        forecastCode: null,
        version: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        direction: 'I',
        orderByColumn: 'createTime',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        forecastcode: [
          { required: true, message: this.$t('forecast.dialog.inputForecastCode'), trigger: 'blur' }
        ],
        version: [
          { required: true, message: this.$t('forecast.dialog.inputVersion'), trigger: 'blur' }
        ],
        compcode: [
          { required: true, message: this.$t('forecast.dialog.inputCompCode'), trigger: 'blur' }
        ],
        plantcode: [
          { required: true, message: this.$t('forecast.dialog.inputPlantCode'), trigger: 'blur' }
        ],
        plantname: [
          { required: true, message: this.$t('forecast.dialog.inputPlantName'), trigger: 'blur' }
        ],
        suppcode: [
          { required: true, message: this.$t('forecast.dialog.inputSuppCode'), trigger: 'blur' }
        ],
        suppname: [
          { required: true, message: this.$t('forecast.dialog.inputSuppName'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    if (this.$route.path.toLowerCase().endsWith('recv/forecast')) {
      this.queryParams.direction = 'I'
    } else {
      this.queryParams.direction = 'O'
    }
    this.getList()
  },
  methods: {
    /** 查询预测列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      const dateRangeCreateTime = this.dateRangeCreateTime
      if (
        !!dateRangeCreateTime && dateRangeCreateTime.length
      ) {
        this.queryParams.params['createTimeBegin'] = dateRangeCreateTime?.[0]
        this.queryParams.params['createTimeEnd'] = dateRangeCreateTime?.[1]
      }
      listForecast(this.queryParams).then(response => {
        this.forecastList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        forecastId: null,
        forecastCode: null,
        version: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        suppCode: null,
        suppName: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRangeCreateTime = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.forecastId)
      console.log(this.ids)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.$t('forecast.dialog.titleAdd')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const forecastId = row.forecastId || this.ids
      this.$router.push('/forecast/detail/' + forecastId)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.forecastId != null) {
            updateForecast(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('forecast.messages.updateSuccess'))
                this.open = false
                this.getList()
              }
            })
          } else {
            addForecast(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('forecast.messages.addSuccess'))
                this.open = false
                this.getList()
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const forecastids = row.forecastId || this.ids
      this.$confirm(
        this.$t('forecast.messages.deleteConfirm', { id: forecastids }),
        this.$t('common.warning'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        return delForecast(forecastids)
      }).then(() => {
        this.getList()
        this.msgSuccess(this.$t('forecast.messages.deleteSuccess'))
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleDownloadExcel() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('forecast.alert.selectAtLeastOne'))
        return
      }
      this.excelDownloading = true
      downloadForecastExcel(this.ids).then((res) => {
        commonDownloadFile(res.msg, `${this.$t('route.forecast')}${formatNow()}.xlsx`)
          .then(() => {
            this.excelDownloading = false
            this.getList()
          })
      }).catch(() => {
        this.excelDownloading = false
      })
    },
    forecastConfirm() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('forecast.alert.selectAtLeastOne'))
        return
      }

      const selection = this.$refs.forecastTableRef.selection
      const notNewCode = selection.filter(el => el.status !== 'New').map(el => el.forecastCode)
      if (notNewCode.length > 0) {
        this.$alert(this.$t('forecast.alert.notAllNew', { notNewCode }))
        return
      }

      this.confirmLoading = true
      confirmForecast(this.ids).then(() => {
        this.confirmLoading = false
        this.getList()
      }).catch(() => {
        this.confirmLoading = false
      })
    }
  }
}
</script>
