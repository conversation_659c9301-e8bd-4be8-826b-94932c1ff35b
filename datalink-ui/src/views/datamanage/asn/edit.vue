<template>
  <div class="app-container">
    <el-form ref="asn" :model="asn" :rules="rules" label-width="auto">
      <el-card shadow="never" class="border-none margin-t24">
        <div slot="header">
          {{ $t('asn.edit.asnInfo') }}
        </div>

        <el-row :gutter="20">
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.asnCode')" prop="asnCode">
              <el-input v-model="asn.asnCode" :placeholder="$t('asn.edit.enterAsnCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.compCode')" prop="compCode">
              <el-input v-model="asn.compCode" :placeholder="$t('asn.edit.enterCompCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.suppCode')" prop="suppCode">
              <el-input v-model="asn.suppCode" :placeholder="$t('asn.edit.enterSuppCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.suppName')" prop="suppName">
              <el-input v-model="asn.suppName" :placeholder="$t('asn.edit.enterSuppName')" disabled />
            </el-form-item>
          </el-col>
          <!-- <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.planDeliveryDate')" prop="planDeliveryDate">
              <el-date-picker clearable size="small" v-model="asn.planDeliveryDate" type="date"
                value-format="yyyy-MM-dd" :placeholder="$t('asn.edit.selectPlanDeliveryDate')">
              </el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.deliveryDate')" prop="deliveryDate">
              <el-date-picker v-model="asn.deliveryDate" size="small" clearable type="datetime" :placeholder="$t('asn.edit.selectDeliveryDate')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <div style="margin-top: 10px;text-align: center">
        <el-button
          type="primary"
          :loading="loading"
          :disabled="hasInvalidQuantity"
          @click="save(false)"
        >{{ $t('asn.edit.save') }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="hasInvalidQuantity"
          @click="save(true)"
        >{{ $t('asn.edit.send') }}
        </el-button>
        <el-button @click="cancel">{{ $t('asn.edit.cancel') }}
        </el-button>
      </div>
      <el-card shadow="never" class="border-none margin-t24" style="margin-top: 10px">
        <div slot="header">
          {{ $t('asn.edit.lineItemInfo') }}
        </div>

        <el-table :data="asn.detail" default-expand-all>
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-card shadow="never" class="border-none margin-t24">
                <div slot="header">
                  {{ $t('asn.edit.articleInfo') }}
                </div>
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button
                      v-hasPermi="['datamanage:asn:edit']"
                      type="primary"
                      plain
                      icon="el-icon-plus"
                      size="mini"
                      @click="addArticle(props.row)"
                    >{{ $t('asn.edit.addArticle') }}
                    </el-button>
                  </el-col>
                </el-row>
                <el-table :data="props.row.articles" default-expand-all>
                  <el-table-column type="expand">
                    <template slot-scope="data">
                      <box-table v-model="data.row.boxes" />
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('asn.edit.orderLineNo')" align="center" prop="orderLineNo" />
                  <el-table-column :label="$t('asn.edit.deliveryScheduleNo')" align="center" prop="deliveryScheduleNo" />
                  <el-table-column :label="$t('asn.edit.articleNo')" align="center" prop="articleNo" />
                  <el-table-column :label="$t('asn.edit.articleName')" align="center" prop="articleName" />
                  <el-table-column :label="$t('asn.edit.quantity')" align="center" prop="quantity">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.quantity'"
                        label-width="0"
                        size="small"
                        :rules="rules.quantity"
                        class="mb-0"
                      >
                        <el-input-number
                          v-model="scope.row.quantity"
                          :min="0.0001"
                          :max="scope.row.maxQty"
                          :controls="false"
                          style="width: 100%"
                          @blur="calcPackQty(props.row.orderCode, scope.row.orderLineNo)"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('asn.edit.unit')" align="center" prop="unit" />
                  <!-- <el-table-column :label="$t('asn.edit.batchNo')" align="center" prop="batchNo" /> -->
                  <el-table-column :label="$t('asn.edit.qtyPerPack')" align="center" prop="qtyPerPack">
                    <!-- <template slot-scope="scope">
                      <el-form-item :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.qtyPerPack'"
                        label-width="0" :rules="rules.qtyPerPack">
                        <el-input-number v-model="scope.row.qtyPerPack" :min="0.0001" :controls="false"
                          style="width: 100%" @blur="calcPackQty"></el-input-number>
                      </el-form-item>
                    </template> -->
                  </el-table-column>
                  <el-table-column :label="$t('asn.edit.packQty')" align="center" prop="packQty" />
                  <!-- <el-table-column :label="$t('asn.edit.nonStd')" align="center" prop="nonStd">
                    <template slot-scope="scope">
                      <el-form-item :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.nonStd'"
                        label-width="0">
                        <el-input v-model="scope.row.nonStd"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column> -->
                  <el-table-column :label="$t('asn.edit.actions')" align="center">
                    <template slot-scope="scope">
                      <el-button
                        v-hasPermi="['datamanage:asn:edit']"
                        size="mini"
                        type="danger"
                        icon="el-icon-delete"
                        @click="deleteArticle(props.row, scope.row.orderLineNo)"
                      >{{ $t('asn.edit.delete') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column :label="$t('asn.edit.dnNo')" align="center" prop="dnNo" />
          <el-table-column :label="$t('asn.edit.orderCode')" align="center" prop="orderCode" />
          <el-table-column :label="$t('asn.edit.plantCode')" align="center" prop="plantCode" />
          <el-table-column :label="$t('asn.edit.plantName')" align="center" prop="plantName" />
          <!--  <el-table-column :label="$t('asn.edit.unloadingNo')" align="center">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.unloadingNo'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.unloadingNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <!--  <el-table-column :label="$t('asn.edit.unloadingName')" align="center" prop="unloadingName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.unloadingName'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.unloadingName"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <!-- <el-table-column :label="$t('asn.edit.sendLocNo')" align="center" prop="sendLocNo">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.sendLocNo'" label-width="0">
                <el-input v-model="scope.row.sendLocNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <!-- <el-table-column :label="$t('asn.edit.sendLocName')" align="center" prop="sendLocName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.sendLocName'" label-width="0">
                <el-input v-model="scope.row.sendLocName"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <el-table-column :label="$t('asn.edit.rcvLocNo')" align="center" prop="rcvLocNo" />
          <!--  <el-table-column :label="$t('asn.edit.rcvLocNo')" align="center" prop="rcvLocNo">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.rcvLocNo'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.rcvLocNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <!-- <el-table-column :label="$t('asn.edit.rcvLocName')" align="center" prop="rcvLocName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.rcvLocName'" label-width="0">
                <el-input v-model="scope.row.rcvLocName"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
        </el-table>
      </el-card>
    </el-form>
    <el-dialog :title="$t('asn.edit.selectOrderItem')" :visible.sync="open" width="650px" append-to-body>
      <el-table :data="orderItems" @selection-change="orderItemSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column min-width="150" :label="$t('asn.edit.itemNo')" align="center" prop="itemNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.deliveryScheduleNo')" align="center" prop="deliveryScheduleNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.articleNo')" align="center" prop="articleNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.articleName')" align="center" prop="articleName" />
        <el-table-column min-width="150" :label="$t('asn.edit.quantity')" align="center" prop="quantity" />
        <el-table-column min-width="150" :label="$t('asn.edit.unit')" align="center" prop="unit" />
        <el-table-column min-width="150" :label="$t('asn.edit.netPrice')" align="center" prop="netPrice" />
        <el-table-column min-width="150" :label="$t('asn.edit.priceUnit')" align="center" prop="priceUnit" />
        <el-table-column min-width="150" :label="$t('asn.edit.currencyCode')" align="center" prop="currencyCode" />
        <el-table-column
          min-width="150"
          :label="$t('asn.edit.deliveryDate')"
          align="center"
          prop="deliveryDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.qtyPerPack')" align="center" prop="qtyPerPack" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.workbinNo')" align="center" prop="workbinNo" />
        <!-- <el-table-column min-width="250" :label="$t('asn.edit.dialog.workbinName')" align="center" prop="workbinName" /> -->
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.state')" align="center" prop="state" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.purDocType')" align="center" prop="purDocType" />
        <!-- <el-table-column min-width="150" :label="$t('asn.edit.dialog.itemType')" align="center" prop="itemType" /> -->
        <el-table-column min-width="280" :label="$t('asn.edit.dialog.text')" align="center" prop="text" />
        <el-table-column
          min-width="320"
          :label="$t('asn.edit.dialog.orderNetWorth')"
          align="center"
          prop="orderNetWorth"
        />
        <el-table-column min-width="320" :label="$t('asn.edit.dialog.delIden')" align="center" prop="delIden" />
        <el-table-column min-width="280" :label="$t('asn.edit.dialog.shortText')" align="center" prop="shortText" />
        <el-table-column
          min-width="150"
          :label="$t('asn.edit.dialog.oldArticleNo')"
          align="center"
          prop="oldArticleNo"
        />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.unloadingNo')" align="center" prop="unloadingNo" />
        <el-table-column
          min-width="150"
          :label="$t('asn.edit.dialog.unloadingName')"
          align="center"
          prop="unloadingName"
        />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.stockLoc')" align="center" prop="stockLoc" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.locDes')" align="center" prop="locDes" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.locAdd')" align="center" prop="locAdd" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.rcvName')" align="center" prop="rcvName" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.rcvTel')" align="center" prop="rcvTel" />
        <el-table-column
          min-width="150"
          :label="$t('asn.edit.dialog.inspeStrategy')"
          align="center"
          prop="inspeStrategy"
        />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.zipCode')" align="center" prop="zipCode" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.city')" align="center" prop="city" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.countryCode')" align="center" prop="countryCode" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.addTimeZone')" align="center" prop="addTimeZone" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street2')" align="center" prop="street2" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street3')" align="center" prop="street3" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street4')" align="center" prop="street4" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="!canAddArticle" @click="doAddArticle()">{{ $t('asn.edit.confirm')
        }}
        </el-button>
        <el-button @click="cancelAdd">{{ $t('asn.edit.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addAsn, getAsn, listOrderAsnQuantities, updateAsn } from '@/api/datamanage/asn'
import { listOrderItems, listOrder } from '@/api/datamanage/order'
import dayjs from 'dayjs'
import { Message, MessageBox } from 'element-ui'
import { mapActions, mapState } from 'vuex'
import BoxTable from './components/BoxTable.vue'

export default {
  name: 'AsnEdit',
  components: { BoxTable },
  data() {
    return {
      asn: {
        asnCode: ' ',
        compCode: '',
        suppCode: '',
        suppName: '',
        deliveryDate: null,
        detail: []
      },
      rules: {
        deliveryDate: [
          { required: true, message: this.$t('asn.edit.validation.deliveryDateRequired'), trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: this.$t('asn.edit.validation.quantityRequired'), trigger: 'blur' }
        ],
        qtyPerPack: [
          { required: true, message: this.$t('asn.edit.validation.qtyPerPackRequired'), trigger: 'blur' }
        ]
      },

      allOrderItems: [],
      open: false,
      orderItems: [],
      toAddArticles: [],
      toAddArticleItem: {},
      canAddArticle: false,
      orderAsnQuantityMap: {},
      asnMaxPackQty: 99999,
      hasInvalidQuantity: false,
      loading: false
    }
  },
  computed: {
    ...mapState('asn', ['asnEdit', 'asnCreate'])
  },
  watch: {
    asnCreate: {
      handler(newVal) {
        if (newVal) {
          this.initializeFromCreate(newVal)
        }
      },
      immediate: true
    },
    asnEdit: {
      handler(newVal) {
        if (newVal) {
          this.initializeFromEdit(newVal)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getConfigKey('asn.maxPackQty').then(response => {
      if (response.msg) {
        this.asnMaxPackQty = parseInt(response.msg)
      }
    })
  },

  methods: {
    calcPackQty(orderCode, orderLineNo) {
      for (const item of this.asn.detail) {
        if (item.orderCode !== orderCode) continue
        for (const article of item.articles) {
          if (article.orderLineNo !== orderLineNo) continue
          if (article.quantity && article.qtyPerPack) {
            article.packQty = Math.ceil(article.quantity / article.qtyPerPack)
            const boxes = []
            for (let i = 0; i < article.packQty; i++) {
              const remaining = article.quantity - i * article.qtyPerPack
              boxes.push({
                boxNo: i + 1,
                batchNo: null,
                quantity: Math.min(remaining, article.qtyPerPack)
              })
            }
            article.boxes = boxes
          }
        }
      }
    },

    addArticle(lineItem) {
      const sameOrder = this.allOrderItems.filter(orderItem => orderItem.orderId === lineItem.orderId)
      this.toAddArticleItem = lineItem
      this.orderItems = sameOrder.filter(orderItem => !lineItem.articles.some(article => article.orderLineNo === orderItem.itemNo && article.deliveryScheduleNo === orderItem.deliveryScheduleNo))
      this.open = true
    },

    doAddArticle() {
      for (const article of this.toAddArticles) {
        this.toAddArticleItem.articles.push(article)
      }
      this.toAddArticles = []
      this.orderItems = []
      this.open = false
      this.toAddArticleItem = {}
      this.canAddArticle = false
    },

    cancelAdd() {
      this.toAddArticles = []
      this.orderItems = []
      this.open = false
      this.toAddArticleItem = {}
      this.canAddArticle = false
    },

    orderItemSelectionChange(selection) {
      this.toAddArticles = []
      const quantityInfoList = this.orderAsnQuantityMap[this.toAddArticleItem.orderCode]
      for (const orderItem of selection) {
        for (const quantityInfo of quantityInfoList) {
          if (quantityInfo.orderLineNo === orderItem.itemNo && quantityInfo.unsentQuantity > 0) {
            const packQty = orderItem.qtyPerPack ? Math.ceil(quantityInfo.unsentQuantity / orderItem.qtyPerPack) : Math.ceil(quantityInfo.unsentQuantity)
            const qtyPerPack = orderItem.qtyPerPack ? orderItem.qtyPerPack : 1
            const boxes = []
            for (let i = 0; i < packQty; i++) {
              const remaining = quantityInfo.unsentQuantity - i * qtyPerPack
              boxes.push({
                boxNo: i + 1,
                batchNo: null,
                quantity: Math.min(remaining, qtyPerPack)
              })
            }
            this.toAddArticles.push({
              orderLineNo: orderItem.itemNo,
              deliveryScheduleNo: orderItem.deliveryScheduleNo,
              articleNo: orderItem.articleNo,
              articleName: orderItem.articleName,
              quantity: quantityInfo.unsentQuantity,
              qtyPerPack,
              unit: orderItem.unit,
              packQty,
              startWith: '',
              endWith: '',
              nonStd: 'N',
              maxQty: quantityInfo.unsentQuantity,
              boxes
            })
            break
          }
        }
      }
      this.canAddArticle = this.toAddArticles.length > 0
    },

    deleteArticle(item, orderLineNo) {
      let index = -1
      for (let i = 0; i < item.articles.length; i++) {
        if (item.articles[i].orderLineNo === orderLineNo) {
          index = i
          break
        }
      }
      if (index >= 0) {
        item.articles.splice(index, 1)
        const quantityInfoList = this.orderAsnQuantityMap[item.orderCode]
        for (const quantityInfo of quantityInfoList) {
          if (quantityInfo.orderLineNo === orderLineNo) {
            quantityInfo.unsentQuantity = quantityInfo.quantity
            break
          }
        }
      }
    },

    closeTab() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.go(-1)
    },

    cancel() {
      MessageBox.confirm(this.$t('asn.edit.confirmCancel')).then(() => {
        if (this.$refs.asn) {
          this.$refs.asn.clearValidate()
          this.$refs.asn.resetFields()
        }
        this.clearAsnData()
        this.closeTab()
      }).catch(() => { })
    },

    async save(needSend) {
      if (this.loading) return

      if (this.asn.detail.length === 0) {
        Message.info(this.$t('asn.edit.atLeastOneLineItem'))
        return
      } else {
        for (const item of this.asn.detail) {
          if (item.articles.length === 0) {
            Message.info(this.$t('asn.edit.atLeastOneArticle'))
            return
          }
        }
      }

      let totalPackQty = 0
      for (const item of this.asn.detail) {
        for (const article of item.articles) {
          if (article.packQty) {
            totalPackQty += article.packQty
          }
        }
      }

      if (totalPackQty > this.asnMaxPackQty) {
        Message.info(this.$t('asn.edit.maxPackQtyExceeded', { max: this.asnMaxPackQty }))
        return
      }

      this.$refs['asn'].validate(async(valid) => {
        if (valid) {
          this.loading = true
          // FIXME 临时处理，添加默认值
          this.asn.detail = this.asn.detail.map(item => ({ ...item, articles: item.articles.map(article => ({ ...article, batchNo: '20250731' })) }))
          const allHaveBatchNo = this.asn.detail.every(item => item.articles.every(article => article.boxes.every(box => box.batchNo)))
          if (!allHaveBatchNo) {
            Message.error(this.$t('asn.edit.someBoxNoBatchNo'))
            this.loading = false
            return
          }
          if (!this.asn.asnId) {
            this.asn.asnCode = null
            this.asn.direction = 'O'
            const kafkaStatus = needSend ? '1' : '0'
            addAsn({ ...this.asn, kafkaStatus }).then(res => {
              if (needSend) {
                Message.success(this.$t('asn.edit.sendSuccess'))
              } else {
                Message.success(this.$t('asn.edit.createSuccess'))
              }
              this.closeTab()
            }).catch(() => {
              this.loading = false
            }).finally(() => {
              this.loading = false
            })
          } else {
            let kafkaStatus = this.asn.kafkaStatus
            if (needSend) {
              kafkaStatus = '1'
            }
            updateAsn({ ...this.asn, kafkaStatus }).then(res => {
              if (needSend) {
                Message.success(this.$t('asn.edit.sendSuccess'))
              } else {
                Message.success(this.$t('asn.edit.saveSuccess'))
              }
              this.closeTab()
            }).catch(() => {
              this.loading = false
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },
    initializeFromCreate(createData) {
      const params = JSON.parse(JSON.stringify(createData))
      this.asn = {
        asnCode: ' ',
        deliveryDate: null,
        compCode: params.compCode,
        suppCode: params.suppCode,
        suppName: params.suppName,
        detail: []
      }
      const orders = params.orders
      const affectedOrders = []
      this.hasInvalidQuantity = false

      let processedOrders = 0
      const totalOrders = orders.length
      let globalEarliestDeliveryDate = null

      this.allOrderItems = []
      for (const order of orders) {
        listOrderAsnQuantities({ orderCode: order.orderCode, compCode: this.asn.compCode }).then(qres => {
          this.orderAsnQuantityMap[order.orderCode] = qres.data
          listOrderItems({ orderId: order.orderId }).then(({ rows }) => {
            this.allOrderItems.push(...rows)
            const selectedItems = rows.filter(row => order.items.some(item => item.itemId === row.itemId && item.deliveryScheduleNo === row.deliveryScheduleNo))
            if (selectedItems.length === 0) return
            const articles = []
            for (const item of selectedItems) {
              if (!item.deliveryDate) continue
              const fullDeliveryDate = item.deliveryDate
              // if (fullDeliveryDate.length === 10) {
              //   fullDeliveryDate = fullDeliveryDate + ' 00:00:00'
              // }
              const currentTimestamp = Date.parse(fullDeliveryDate)
              if (isNaN(currentTimestamp)) continue

              if (!globalEarliestDeliveryDate) {
                globalEarliestDeliveryDate = { ...item, deliveryDate: fullDeliveryDate }
              } else {
                const earliestFullDate = globalEarliestDeliveryDate.deliveryDate
                // if (earliestFullDate.length === 10) {
                //   earliestFullDate = earliestFullDate + ' 00:00:00'
                // }
                const earliestTimestamp = Date.parse(earliestFullDate)
                if (currentTimestamp < earliestTimestamp) {
                  globalEarliestDeliveryDate = { ...item, deliveryDate: fullDeliveryDate }
                }
              }
            }

            for (const orderItem of selectedItems) {
              for (const orderAsnQuantity of qres.data) {
                if (orderAsnQuantity.orderLineNo === orderItem.itemNo &&
                  orderAsnQuantity.deliveryScheduleNo === orderItem.deliveryScheduleNo &&
                  orderAsnQuantity.unsentQuantity > 0
                ) {
                  const packQty = orderItem.qtyPerPack ? Math.ceil(orderAsnQuantity.unsentQuantity / orderItem.qtyPerPack) : Math.ceil(orderAsnQuantity.unsentQuantity)
                  const qtyPerPack = orderItem.qtyPerPack ? orderItem.qtyPerPack : 1
                  const boxes = []
                  for (let i = 0; i < packQty; i++) {
                    const remaining = orderAsnQuantity.unsentQuantity - i * qtyPerPack
                    boxes.push({
                      boxNo: i + 1,
                      batchNo: null,
                      quantity: Math.min(remaining, qtyPerPack)
                    })
                  }
                  articles.push({
                    orderLineNo: orderItem.itemNo,
                    deliveryScheduleNo: orderItem.deliveryScheduleNo,
                    articleNo: orderItem.articleNo,
                    articleName: orderItem.articleName,
                    quantity: orderAsnQuantity.unsentQuantity,
                    qtyPerPack,
                    unit: orderItem.unit,
                    packQty,
                    startWith: '',
                    endWith: '',
                    nonStd: 'N',
                    maxQty: orderAsnQuantity.unsentQuantity,
                    boxes
                  })
                  if (orderAsnQuantity.unsentQuantity <= 0) {
                    this.hasInvalidQuantity = true
                    if (!affectedOrders.includes(order.orderCode)) {
                      affectedOrders.push(order.orderCode)
                    }
                  }
                  break
                }
              }
            }

            this.asn.detail.push({
              orderId: order.orderId,
              orderCode: order.orderCode,
              plantCode: order.plantCode,
              plantName: order.plantName,
              sendLocNo: '',
              sendLocName: '',
              unloadingNo: selectedItems[0].unloadingNo,
              unloadingName: selectedItems[0].unloadingName,
              rcvLocNo: selectedItems[0].stockLoc,
              rcvLocName: '',
              articles: articles
            })

            processedOrders++
            if (processedOrders === totalOrders && globalEarliestDeliveryDate) {
              const deliveryDate = globalEarliestDeliveryDate.deliveryDate

              this.asn.deliveryDate = dayjs(deliveryDate).format()
              // if (deliveryDate && deliveryDate.length === 10) { // 只有日期部分 YYYY-MM-DD
              //   // 创建一个带时区的日期对象
              //   const date = new Date(deliveryDate + 'T00:00:00')
              //   this.asn.deliveryDate = date
              // } else {
              //   this.asn.deliveryDate = new Date(deliveryDate)
              // }
            }

            if (processedOrders === totalOrders && this.hasInvalidQuantity) {
              MessageBox.alert(
                this.$t('asn.edit.validation.quantityUsedUp') + '\n' +
                this.$t('asn.edit.validation.affectedOrders') + ': ' + affectedOrders.join(', '),
                this.$t('asn.edit.warning'),
                {
                  confirmButtonText: this.$t('asn.edit.confirm')
                }
              )
            }
          })
        })
      }
    },
    initializeFromEdit(asnId) {
      this.allOrderItems = []
      const loadAsnDetails = () => {
        getAsn(asnId).then(({ data }) => {
          this.asn = { ...data, detail: [] }
          for (const order of data.detail) {
            listOrderAsnQuantities({ orderCode: order.orderCode, compCode: this.asn.compCode }).then(qres => {
              this.orderAsnQuantityMap[order.orderCode] = qres.data
              listOrder({ orderCode: order.orderCode }).then((res) => {
                if (res.rows.length > 0) {
                  this.asn.detail.push({ ...order, orderId: res.rows[0].orderId })
                  listOrderItems({ orderId: res.rows[0].orderId }).then((res) => {
                    this.allOrderItems.push(...res.rows)
                  })
                }
              })
            })
          }
        })
      }

      if (asnId) {
        loadAsnDetails(asnId)
      }
    },
    beforeDestroy() {
      this.asn = {
        detail: []
      }
      this.clearAsnData()
    },
    saveBoxTableRef(index) {
      const refName = 'boxTable_' + index
      if (this.$refs[refName]) {
        this.$set(this.boxTableRefs, index, this.$refs[refName])
      }
    },
    ...mapActions('asn', ['clearAsnData'])
  }
}
</script>

<style scoped>
.mb-0 {
  margin-bottom: 0 !important
}
</style>
