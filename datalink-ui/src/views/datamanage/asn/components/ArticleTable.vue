<template>
  <el-card shadow="never" class="border-none margin-t24">
    <div slot="header">
      {{ $t('asn.article.materialInfo') }}
    </div>
    <el-table v-loading="loading" :data="asnArticleList" default-expand-all>
      <el-table-column type="expand">
        <template slot-scope="data">
          <box-table :value="data.row.boxes" readonly />
        </template>
      </el-table-column>
      <el-table-column :label="$t('asn.article.orderLineNo')" align="center" prop="orderLineNo" />
      <el-table-column :label="$t('asn.article.releaseNo')" align="center" prop="deliveryScheduleNo" />
      <el-table-column :label="$t('asn.article.articleNo')" align="center" prop="articleNo" />
      <el-table-column :label="$t('asn.article.articleName')" align="center" prop="articleName" />
      <el-table-column :label="$t('asn.article.quantity')" align="center" prop="quantity" />
      <el-table-column :label="$t('asn.article.unit')" align="center" prop="unit" />
      <el-table-column :label="$t('asn.article.qtyPerPack')" align="center" prop="qtyPerPack" />
      <el-table-column :label="$t('asn.article.packQty')" align="center" prop="packQty" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getArticleList()"
    />
  </el-card>
</template>

<script>
import { listAsnArticles } from '@/api/datamanage/asn'
import BoxTable from './BoxTable.vue'

export default {
  name: 'AsnArticle',
  components: { BoxTable },
  props: {
    itemId: Number,
    needLoad: Boolean
  },
  data() {
    return {
      loading: true,
      asnArticleList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemId: null
      },
      total: 0
    }
  },
  watch: {
    'needLoad': {
      handler: function(val, oldVal) {
        if (!oldVal && val) {
          this.getArticleList()
        }
      },
      immediate: true
    }
  },
  methods: {
    getArticleList() {
      this.loading = true
      this.queryParams.itemId = this.itemId
      listAsnArticles(this.queryParams).then(response => {
        this.loading = false
        this.asnArticleList = response.rows
        this.total = response.total
      })
    }
  }
}
</script>

<style scoped>

</style>
