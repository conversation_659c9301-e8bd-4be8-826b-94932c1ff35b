<template>
  <el-card shadow="never" class="border-none margin-t24">
    <el-form
      ref="formRef"
      :model="form"
      :inline="true"
    >
      <div v-if="!readonly" style="margin-bottom: 24px;">
        <el-form-item
          size="small"
          prop="boxNoRange"
          :label="$t('asn.edit.box.boxNo')"
          class="mb-0"
          :rules="rules.boxNoRange"
        >
          <number-range-input v-model="form.boxNoRange" />
        </el-form-item>
        <el-form-item
          size="small"
          prop="batchNo"
          :label="$t('asn.edit.box.batchNo')"
          class="mb-0"
          :rules="rules.batchNo"
        >
          <el-date-picker v-model="form.batchNo" clearable type="date" value-format="yyyyMMdd" format="yyyyMMdd" :editable="false" style="width: 160px;" />
        </el-form-item>
        <el-button
          size="small"
          type="primary"
          @click="onChangMultiBatchNo"
        >{{ $t('asn.edit.box.batchEdit') }}
        </el-button>
      </div>
      <el-table ref="boxTableRef" :data="form.boxes" max-height="320">
        <el-table-column v-if="!readonly" type="selection" width="54" align="center" />
        <el-table-column :label="$t('asn.edit.box.boxNo')" align="center" prop="boxNo" />
        <el-table-column v-if="!readonly" :label="$t('asn.edit.box.batchNo')" align="center" prop="batchNo">
          <template slot-scope="scope">
            <el-form-item
              size="small"
              :prop="'boxes.' + scope.$index + '.batchNo'"
              label-width="0"
              class="mb-0"
              :rules="rules.batchNo"
            >
              <el-date-picker v-model="scope.row.batchNo" clearable type="date" value-format="yyyyMMdd" format="yyyyMMdd" :editable="false" style="width: 140px;" />
            </el-form-item>
          </template>

        </el-table-column>
        <el-table-column v-if="readonly" :label="$t('asn.edit.box.batchNo')" align="center" prop="batchNo" />
        <el-table-column :label="$t('asn.edit.box.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('asn.edit.box.palletNo')" align="center" prop="palletNo" />
      </el-table>
    </el-form>
  </el-card>
</template>

<script>
import { Message } from 'element-ui'
import NumberRangeInput from '@/components/NumberRangeInput'

export default {
  name: 'AsnArticleBox',
  components: {
    NumberRangeInput
  },
  props: {
    value: {
      type: Array,
      default: () => {
        return []
      },
      validator(value) {
        return Array.isArray(value) && value.every(
          item => typeof item === 'object' && 'batchNo' in item && 'boxNo' in item
        )
      }
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        batchNo: null,
        boxNoRange: [null, null],
        boxes: []
      },
      rules: {
        batchNo: [
          { required: true, message: this.$t('asn.edit.box.pleaseInputBatchNo'), trigger: 'blur' }
        ],
        boxNoRange: [
          {
            // arrow function for get this
            validator: (rule, val, cb) => {
              const [from, to] = val || []

              // 都不填，允许
              if ((from == null || from === '') && (to == null || to === '')) {
                cb()
                return
              }

              // 有一个为空
              if ((from == null || from === '') || (to == null || to === '')) {
                cb(new Error(this.$t('asn.edit.box.pleaseInputRange')))
                return
              }

              cb() // 校验通过
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal) {
          this.form.boxes = newVal.slice()
        }
      },
      immediate: true
    }
  },
  methods: {
    onChangMultiBatchNo() {
      this.$refs.formRef.validateField('batchNo', (errorMessage) => {
        if (!errorMessage) {
          let boxes = this.$refs.boxTableRef.selection
          const [range1, range2] = this.form.boxNoRange || []
          if (range1 && range2) {
            const start = Math.min(range1, range2)
            const end = Math.max(range1, range2)
            const allBoxes = this.$refs.boxTableRef.tableData || []

            boxes = [...boxes, ...allBoxes.filter(box => {
              return box.boxNo >= start && box.boxNo <= end
            })]
          }

          if (boxes.length <= 0) {
            Message.info(this.$t('asn.edit.box.atLeastOneBox'))
            return
          }

          const boxNos = Array.from(new Set(boxes.map(el => el.boxNo)))
          this.form.boxes = this.form.boxes.map(originBox => {
            const matched = boxNos.some(boxNo => boxNo === originBox.boxNo)
            if (matched) {
              return { ...originBox, batchNo: this.form.batchNo }
            }
            return originBox
          })

          this.$emit('input', this.form.boxes)
          this.form.batchNo = null
          this.form.boxNoRange = [null, null]
        }
      })
    }
  }
}
</script>

<style scoped>

.mb-0 {
  margin-bottom: 0;
}
</style>
