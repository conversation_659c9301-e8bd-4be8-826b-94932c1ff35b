<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t("asn.detail.asnInfo") }}
      </div>
      <el-table border :show-header="false" :data="baseTableData">
        <el-table-column
          prop="title1"
          :label="$t('asn.detail.title')"
          width="185"
        />

        <el-table-column :label="$t('asn.detail.content')" min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('asn.detail.title')"
          width="185"
        />

        <el-table-column :label="$t('asn.detail.content')" min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div
      v-if="asn.asnId && asn.kafkaStatus !== '0'"
      style="margin-top: 10px; text-align: center; margin-bottom: 10px"
    >
      <el-button
        type="warning"
        icon="el-icon-printer"
        :loading="printPickingListLoading"
        @click="handlePrintPickList"
      >
        {{ $t('asn.detail.printPickingList') }}
      </el-button>
      <el-button
        :loading="printNpsSlsLoading"
        type="primary"
        icon="el-icon-printer"
        @click="handlePrintNpsSls"
      >{{ $t("asn.button.printNpsSls") }}
      </el-button>
      <el-button
        type="success"
        icon="el-icon-printer"
        :loading="printProdTagLoading"
        @click="handlePrintProdTag"
      >
        {{ $t('asn.button.printProdTag') }}
      </el-button>
      <el-button
        type="primary"
        icon="el-icon-printer"
        :loading="printPalletTagLoading"
        @click="handlePrintPalletTag"
      >
        {{ $t('asn.button.printPalletTag') }}
      </el-button>
    </div>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t("asn.detail.lineItemInfo") }}
      </div>
      <el-table
        v-loading="loading"
        :data="asnitemList"
        :row-key="getRowKey"
        :expand-row-keys="expandKeys"
        @expand-change="loadArticleList"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <ArticleTable
              :item-id="props.row.itemId"
              :need-load="props.row.loaded"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('asn.detail.dnNo')" align="center" prop="dnNo" /> -->
        <el-table-column
          :label="$t('asn.detail.orderCode')"
          align="center"
          prop="orderCode"
        />
        <el-table-column
          :label="$t('asn.detail.plantCode')"
          align="center"
          prop="plantCode"
        />
        <el-table-column
          :label="$t('asn.detail.plantName')"
          align="center"
          prop="plantName"
        />
        <!-- <el-table-column :label="$t('asn.detail.unloadingNo')" align="center" prop="unloadingNo" /> -->
        <!-- <el-table-column :label="$t('asn.detail.unloadingName')" align="center" prop="unloadingName" /> -->
        <!-- <el-table-column :label="$t('asn.detail.sendLocNo')" align="center" prop="sendLocNo" /> -->
        <!-- <el-table-column :label="$t('asn.detail.sendLocName')" align="center" prop="sendLocName" /> -->
        <el-table-column
          :label="$t('asn.detail.rcvLocNo')"
          align="center"
          prop="rcvLocNo"
        />
        <!-- <el-table-column :label="$t('asn.detail.rcvLocName')" align="center" prop="rcvLocName" /> -->
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import {
  getAsn,
  listAsnItem,
  printNpsSls,
  printProdTag,
  printPalletTag,
  printPickingListByAsnCode
} from '@/api/datamanage/asn'
import ArticleTable from './components/ArticleTable'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

export default {
  name: 'AsnDetail',
  components: {
    ArticleTable
  },
  data() {
    return {
      asn: {},
      asnitemList: [],
      editable: true,
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        asnId: null
      },
      expandKeys: [],
      printNpsSlsLoading: false,
      printPickingListLoading: false,
      printPalletTagLoading: false,
      printProdTagLoading: false
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData: function () {
      return [
        {
          title1: this.$t('asn.detail.asnCode'),
          content1: this.asn.asnCode,
          title2: this.$t('asn.detail.compCode'),
          content2: this.asn.compCode
        },
        {
          title1: this.$t('asn.detail.suppCode'),
          content1: this.asn.suppCode,
          title2: this.$t('asn.detail.suppName'),
          content2: this.asn.suppName
        },
        {
          // title1: this.$t('asn.detail.planDeliveryDate'),
          // content1: this.asn.planDeliveryDate,
          title1: this.$t('asn.detail.deliveryDate'),
          content1: dayjs(this.asn.deliveryDate).format('YYYY-MM-DD HH:mm:ss')
        }
      ]
    }
  },
  created() {
    const asnId = this.$route.params && this.$route.params.asnId
    this.queryParams.asnId = asnId
    if (asnId) {
      getAsn(asnId).then((res) => {
        this.asn = res.data
      })
      this.getList()
    }
  },

  methods: {
    /** 查询订单行项目列表 */
    getList() {
      this.loading = true
      listAsnItem(this.queryParams).then((response) => {
        this.asnitemList = response.rows
        for (const asnItem of this.asnitemList) {
          asnItem.loaded = false
        }
        this.total = response.total
        this.loading = false
        if (this.asnitemList.length > 0) {
          this.expandKeys.push(this.asnitemList[0].itemId)
          if (!this.asnitemList[0].loaded) {
            this.asnitemList[0].loaded = true
          }
        }
      })
    },
    loadArticleList(row, expandedRows) {
      if (!row.loaded) {
        row.loaded = true
      }
    },
    getRowKey(row) {
      return row.itemId
    },
    handlePrintNpsSls() {
      this.printNpsSlsLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printNpsSls(this.asn.asnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printNpsSlsLoading = false
        })
        .catch(() => {
          loading.close()
          this.printNpsSlsLoading = false
        })
    },
    handlePrintPickList() {
      this.printPickingListLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printPickingListByAsnCode(this.asn.asnId, dayjs.tz.guess())
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printPickingListLoading = false
        })
        .catch(() => {
          loading.close()
          this.printPickingListLoading = false
        })
    },
    /** 打印现品票 */
    handlePrintProdTag() {
      if (this.checkASNId === null) {
        this.$alert(this.$t('asn.alert.selectOnePrint'))
        return
      }
      this.printNpsSlsLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printProdTag(this.asn.asnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printNpsSlsLoading = false
        })
        .catch(() => {
          loading.close()
          this.printNpsSlsLoading = false
        })
    },
    /** 打印托标签 */
    handlePrintPalletTag() {
      this.printPalletTagLoading = true
      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)
      printPalletTag(this.asn.asnId)
        .then((response) => {
          loading.close()
          window.open(
            process.env.VUE_APP_BASE_API +
              '/common/downloadPdf?fileName=' +
              encodeURI(response.msg) +
              '&delete=false'
          )
          this.printPalletTagLoading = false
        })
        .catch(() => {
          loading.close()
          this.printPalletTagLoading = false
        })
    }
  }
}
</script>

<style scoped></style>
