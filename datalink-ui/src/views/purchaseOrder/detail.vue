<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t("purchaseOrder.orderDetail.baseInfoTitle") }}
      </div>
      <el-table border :show-header="false" :data="baseTableData">
        <el-table-column
          prop="title1"
          :label="$t('purchaseOrder.orderDetail.title')"
          width="185"
        />
        <el-table-column
          :label="$t('purchaseOrder.orderDetail.content')"
          min-width="310"
        >
          <template slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="title2"
          :label="$t('purchaseOrder.orderDetail.title')"
          width="185"
        />
        <el-table-column
          :label="$t('purchaseOrder.orderDetail.content')"
          min-width="310"
        >
          <template slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <p></p>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t("purchaseOrder.orderDetail.partsInfoTitle") }}
      </div>
      <el-table v-loading="loading" :data="orderItems">
        <el-table-column
          min-width="120"
          :label="$t('purchaseOrder.orderDetail.partNo')"
          align="center"
          prop="partNo"
        />
        <el-table-column
          min-width="200"
          :label="$t('purchaseOrder.orderDetail.partDesc')"
          align="center"
          prop="partDesc"
        />
        <el-table-column
          min-width="100"
          :label="$t('purchaseOrder.orderDetail.quantity')"
          align="center"
          prop="quantity"
        />
        <el-table-column
          min-width="120"
          :label="$t('purchaseOrder.orderDetail.orderStatus')"
          align="center"
          prop="orderStatus"
        />
        <el-table-column
          min-width="80"
          :label="$t('purchaseOrder.orderDetail.unit')"
          align="center"
          prop="unit"
        />
        <el-table-column
          min-width="100"
          :label="$t('purchaseOrder.orderDetail.qtyPerPack')"
          align="center"
          prop="qtyPerPack"
        />
        <el-table-column
          min-width="120"
          :label="$t('purchaseOrder.orderDetail.buyerCode')"
          align="center"
          prop="buyerCode"
        />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getItemList"
      />
    </el-card>
  </div>
</template>

<script>
import { getOrderOnly, listOrderItems } from "@/api/datamanage/order";

export default {
  name: 'PurchaseOrderDetail',
  data() {
    return {
      order: {},
      orderItems: [],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId: null,
      },
    };
  },
  created() {
    const orderId = this.$route.params.orderId;
    this.queryParams.orderId = orderId;
    this.getOrder(orderId);
    this.getItemList();
  },
  methods: {
    getOrder(orderId) {
      getOrderOnly(orderId).then((res) => {
        this.order = res.data
      })
    },
    getItemList() {
      this.loading = true;
      this.queryParams.params = {};
      listOrderItems(this.queryParams).then((res) => {
        this.orderItems = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
  },
  computed: {
    baseTableData() {
      return [
        {
          title1: this.$t("purchaseOrder.orderDetail.orderCode"),
          content1: this.order.orderCode,
          title2: this.$t("purchaseOrder.orderDetail.lineNo"),
          content2: this.order.lineNo,
        },
        {
          title1: this.$t("purchaseOrder.orderDetail.releaseNo"),
          content1: this.order.releaseNo,
          title2: this.$t("purchaseOrder.orderDetail.suppAddr"),
          content2: this.order.suppAddr,
        },
        {
          title1: this.$t("purchaseOrder.orderDetail.rcvAddr"),
          content1: this.order.rcvAddr,
          title2: this.$t("purchaseOrder.orderDetail.depot"),
          content2: this.order.depot,
        },
        {
          title1: this.$t("purchaseOrder.orderDetail.plantName"),
          content1: this.order.plantName,
          title2: this.$t("purchaseOrder.orderDetail.shipStatus"),
          content2: this.order.shipStatus,
        },
        {
          title1: this.$t("purchaseOrder.orderDetail.deliveryDate"),
          content1: this.order.deliveryDate,
        },
      ];
    },
  },
};
</script>

<style scoped></style>
