<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item :label="$t('purchaseOrder.form.orderNo')">
        <el-input v-model="queryParams.orderNo" :placeholder="$t('purchaseOrder.placeholder.orderNo')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('purchaseOrder.form.partNo')">
        <el-input v-model="queryParams.partNo" :placeholder="$t('purchaseOrder.placeholder.partNo')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('purchaseOrder.form.shipStatus')">
        <el-select v-model="queryParams.shipStatus" :placeholder="$t('purchaseOrder.form.shipStatusPlaceholder')" clearable size="small">
          <el-option v-for="item in shipStatusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('purchaseOrder.form.dateRange')">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          :range-separator="$t('purchaseOrder.form.to')"
          :start-placeholder="$t('purchaseOrder.form.startDate')"
          :end-placeholder="$t('purchaseOrder.form.endDate')"
          value-format="yyyy-MM-dd"
          size="small"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('purchaseOrder.search') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table :data="orderList" border stripe style="width: 100%; margin-top: 20px;">
      <el-table-column :label="$t('purchaseOrder.table.orderNo')" align="center">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'PurchaseOrderDetail',
              params: { orderId: scope.row.orderId },
              query: getQueryParams()
            }"
            class="link-type"
          >
            <span>{{ scope.row.orderCode }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column prop="lineNo" :label="$t('purchaseOrder.table.lineNo')" width="70" />
      <el-table-column prop="releaseNo" :label="$t('purchaseOrder.table.releaseNo')" width="120" />
      <el-table-column prop="orderStatus" :label="$t('purchaseOrder.table.orderStatus')" width="100" />
      <el-table-column prop="shipStatus" :label="$t('purchaseOrder.table.shipStatus')" width="100" />
      <el-table-column prop="partNo" :label="$t('purchaseOrder.table.partNo')" width="120" />
      <el-table-column prop="partDesc" :label="$t('purchaseOrder.table.partDesc')" />
      <el-table-column prop="deliveryDate" :label="$t('purchaseOrder.table.deliveryDate')" width="120" />
      <el-table-column prop="orderQty" :label="$t('purchaseOrder.table.orderQty')" width="100" />
      <el-table-column prop="unit" :label="$t('purchaseOrder.table.unit')" width="70" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  listOrder
} from '@/api/datamanage/order'

export default {
  name: 'PurchaseOrder',
  components: {},
  data () {
    return {
      loading: true,
      ids: [],
      showSearch: true,
      total: 0,
      orderList: [],
      shipStatusOptions: [],
      title: '',
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        articleNo: null,
        unloadingNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        isRead: '',
        isComplete: ''
      }
    }
  },
  computed: {
  },
  created () {
    this.getList()
  },
  methods: {
    handleQuery () {
      this.queryParams.pageNum = 1
      // 更新路由查询参数
      const query = this.getQueryParams()
      this.$router.replace({ query: { ...query } }).catch(() => {})
      this.getList()
    },
    getQueryParams () {
      const query = {}
      // 保存基本查询参数
      Object.keys(this.queryParams).forEach(key => {
        if (this.queryParams[key] !== null && this.queryParams[key] !== '') {
          query[key] = this.queryParams[key]
        }
      })
      // 保存日期范围
      if (this.daterangeCreateTime?.length) {
        query.daterangeCreateTime = this.daterangeCreateTime.join(',')
      }
      if (this.daterangeTimeBegin?.length) {
        query.daterangeTimeBegin = this.daterangeTimeBegin.join(',')
      }
      if (this.daterangeTimeEnd?.length) {
        query.daterangeTimeEnd = this.daterangeTimeEnd.join(',')
      }
      if (this.daterangeSapUpdateTime?.length) {
        query.daterangeSapUpdateTime = this.daterangeSapUpdateTime.join(',')
      }
      if (this.daterangeReceiveTime?.length) {
        query.daterangeReceiveTime = this.daterangeReceiveTime.join(',')
      }
      // 添加返回路径
      query.returnPath = this.$route?.fullPath || '/'
      return query
    },
    resetQuery () {
      this.daterangeTimeBegin = []
      this.daterangeTimeEnd = []
      this.daterangeCreateTime = []
      this.daterangeSapUpdateTime = []
      this.daterangeReceiveTime = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        orderCode: null,
        articleNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        orderByColumn: '',
        isAsc: '',
        isRead: '',
        isComplete: '',
        unloadingNo: null
      }
      this.resetForm('queryForm')
      // 清空路由查询参数
      this.$router.replace({ query: {} }).catch(() => {})
      this.handleQuery()
    },
    getList () {
      this.loading = true
      this.queryParams.params = {}
      if (this.daterangeTimeBegin && this.daterangeTimeBegin.length) {
        this.queryParams.params['beginTimeBegin'] = this.daterangeTimeBegin[0]
        this.queryParams.params['endTimeBegin'] = this.daterangeTimeBegin[1]
      }
      if (this.daterangeTimeEnd && this.daterangeTimeEnd.length) {
        this.queryParams.params['beginTimeEnd'] = this.daterangeTimeEnd[0]
        this.queryParams.params['endTimeEnd'] = this.daterangeTimeEnd[1]
      }
      listOrder(this.queryParams).then(response => {
        this.orderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    cancel () {
      this.open = false
      this.reset()
    },
    reset () {
      this.form = {
        orderId: null,
        orderCode: null,
        articleNo: null,
        depot: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        plantCode: null,
        plantName: null,
        deliveryDate: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      }
      this.items = []
      this.resetForm('form')
    }
  }
}
</script>
