<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="mb8">
      <el-form-item :label="$t('forecastOverview.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('forecastOverview.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecastOverview.form.dateRange')">
        <el-date-picker
          v-model="daterange"
          size="small"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          format="yyyy-MM-dd"
          :start-placeholder="$t('forecastOverview.placeholder.startDate')"
          :end-placeholder="$t('forecastOverview.placeholder.endDate')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('feedback.search')}}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('feedback.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="forecastList"
      border
      stripe
      ref="forecastTable"
      style="width: 100%"
    >
      <el-table-column :label="$t('forecastOverview.table.sendTime')" prop="sendTime" align="center" />
      <el-table-column :label="$t('forecastOverview.table.suppCode')" prop="suppCode" align="center" />
      <el-table-column :label="$t('forecastOverview.table.suppName')" prop="suppName" align="center" />
      <el-table-column :label="$t('forecastOverview.table.plant')" prop="plant" align="center" />
      <el-table-column :label="$t('forecastOverview.table.operateTime')" prop="operateTime" align="center" />
      <el-table-column :label="$t('forecastOverview.table.operateType')" prop="operateType" align="center" />
      <el-table-column :label="$t('forecastOverview.table.operator')" prop="operator" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "forecastOverview",
  data() {
    return {
      loading: true,
      forecastList: [],
      total: 0,
      daterange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: '',
        startDate: '',
        endDate: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.forecastList = [
          {
            sendTime: '2025-05-30 09:00',
            suppCode: 'S001',
            suppName: '供应商A',
            plant: '工厂A',
            operateTime: '2025-05-30 10:00',
            operateType: '创建',
            operator: '张三'
          },
          {
            sendTime: '2025-05-30 09:30',
            suppCode: 'S002',
            suppName: '供应商B',
            plant: '工厂B',
            operateTime: '2025-05-30 11:00',
            operateType: '修改',
            operator: '李四'
          }
        ];
        this.total = this.forecastList.length;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
