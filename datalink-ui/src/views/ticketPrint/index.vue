<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item :label="$t('ticketPrint.form.orderNo')" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          :placeholder="$t('ticketPrint.placeholder.orderNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('ticketPrint.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('ticketPrint.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('ticketPrint.form.shipStatus')" prop="shipStatus">
        <el-select
          v-model="queryParams.shipStatus"
          :placeholder="$t('ticketPrint.placeholder.shipStatus')"
          clearable
          size="small"
        >
          <el-option v-for="dict in shipStatusOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('ticketPrint.form.dateRange')">
        <el-date-picker
          v-model="daterange"
          size="small"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          format="yyyy-MM-dd"
          :start-placeholder="$t('ticketPrint.placeholder.startTime')"
          :end-placeholder="$t('ticketPrint.placeholder.endTime')"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-document" size="mini" @click="handlePrintDeliveryAcceptance">{{$t('ticketPrint.button.printDeliveryAcceptance')}}</el-button>
        <el-button icon="el-icon-tickets" size="mini" @click="handlePrintPickList">{{$t('ticketPrint.button.printPickList')}}</el-button>
        <el-button type="primary" icon="el-icon-printer" size="mini" @click="openItemTagDialog">{{$t('ticketPrint.button.printItemTag')}}</el-button>
        <el-button type="primary" icon="el-icon-box" size="mini" @click="handlePrintPalletTag">{{$t('ticketPrint.button.printPalletTag')}}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="ticketList"
      border
      stripe
      style="width: 100%; min-width: 800px;"
      @selection-change="handleSelectionChange"
      highlight-current-row
      ref="ticketTable"
    >
      <el-table-column :label="$t('ticketPrint.table.index')" type="index" width="60" align="center" />
      <el-table-column type="selection" fixed width="55" align="center" />
      <el-table-column :label="$t('ticketPrint.table.asnNo')" prop="asnNo" align="center" />
      <el-table-column :label="$t('ticketPrint.table.orderNo')" prop="orderNo" align="center" />
      <el-table-column :label="$t('ticketPrint.table.lineNo')" prop="lineNo" align="center" />
      <el-table-column :label="$t('ticketPrint.table.releaseNo')" prop="releaseNo" align="center" />
      <el-table-column :label="$t('ticketPrint.table.partNo')" prop="partNo" align="center" />
      <el-table-column :label="$t('ticketPrint.table.partDesc')" prop="partDesc" align="center" />
      <el-table-column :label="$t('ticketPrint.table.plant')" prop="plant" align="center" />
      <el-table-column :label="$t('ticketPrint.table.shipDate')" prop="shipDate" align="center" />
      <el-table-column :label="$t('ticketPrint.table.orderQty')" prop="orderQty" align="center" />
      <el-table-column :label="$t('ticketPrint.table.deliveryQty')" prop="deliveryQty" align="center" />
      <el-table-column :label="$t('ticketPrint.table.qtyPerPack')" prop="qtyPerPack" align="center" />
      <el-table-column :label="$t('ticketPrint.table.unit')" prop="unit" align="center" />
      <el-table-column :label="$t('ticketPrint.table.shipStatus')" prop="shipStatus" align="center" />
      <el-table-column :label="$t('ticketPrint.table.deliveryDate')" prop="deliveryDate" align="center" />
      <el-table-column :label="$t('ticketPrint.table.warehouse')" prop="warehouse" align="center" />
      <el-table-column :label="$t('ticketPrint.table.palletCount')" prop="palletCount" align="center" />
      <el-table-column :label="$t('ticketPrint.table.containerType')" prop="containerType" align="center" />
      <el-table-column :label="$t('ticketPrint.table.totalDeliveryQty')" prop="totalDeliveryQty" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="$t('ticketPrint.dialog.itemTagTitle')"
      :visible.sync="itemTagDialogVisible"
      width="800px"
      @close="resetItemTagDialog"
    >
      <el-form :inline="true" :model="itemTagForm" class="item-tag-form" style="margin-bottom: 10px">
        <el-form-item :label="$t('ticketPrint.dialog.lineNo')">
          <el-input v-model="itemTagForm.lineNo" size="small" style="width: 120px" />
        </el-form-item>
        <el-form-item :label="$t('ticketPrint.dialog.orderNo')">
          <el-input v-model="itemTagForm.orderNo" size="small" style="width: 150px" />
        </el-form-item>
        <el-form-item :label="$t('ticketPrint.dialog.batchNo')">
          <el-input v-model="itemTagForm.batchNo" size="small" style="width: 120px" />
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 10px">
        <el-button type="primary" size="mini" @click="addBatchNo">{{$t('ticketPrint.dialog.addBatchNo')}}</el-button>
        <el-button type="danger" size="mini" @click="removeBatchNo">{{$t('ticketPrint.dialog.removeBatchNo')}}</el-button>
        <el-button type="primary" icon="el-icon-printer" size="mini" @click="printItemTag">{{$t('ticketPrint.dialog.printItemTag')}}</el-button>
        <el-button type="warning" size="mini" @click="reprintItemTag">{{$t('ticketPrint.dialog.reprintItemTag')}}</el-button>
      </div>
      <el-table :data="itemTagTable" border size="small" style="width: 100%; margin-bottom: 10px">
        <el-table-column :label="$t('ticketPrint.dialog.table.index')" type="index" width="50" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.orderNo')" prop="orderNo" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.lineNo')" prop="lineNo" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.releaseNo')" prop="releaseNo" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.partNo')" prop="partNo" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.partDesc')" prop="partDesc" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.batchNo')" prop="batchNo" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.snp')" prop="qtyPerPack" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.deliveryQty')" prop="deliveryQty" align="center" />
        <el-table-column :label="$t('ticketPrint.dialog.table.deliveryDate')" prop="deliveryDate" align="center" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "ticketPrint",
  data() {
    return {
      loading: true,
      printLoading: false,
      ids: [],
      shipStatusOptions: [],
      showSearch: true,
      total: 0,
      ticketList: [],
      daterange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        suppCode: null,
        shipStatus: null,
        startTime: null,
        endTime: null
      },
      // 多选内容
      selectedRows: [],
      // 现品票弹窗相关
      itemTagDialogVisible: false,
      itemTagForm: {
        lineNo: '',
        orderNo: '',
        batchNo: ''
      },
      itemTagTable: [],
      selectedItemTagIndex: null
    };

  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.ticketList = [
          {
            asnNo: 'ASN20240528001',
            orderNo: 'PO20240528001',
            lineNo: '1',
            releaseNo: 'RL20240528001',
            partNo: 'P001',
            partDesc: '零件A',
            plant: '工厂A',
            shipDate: '2025-05-28',
            orderQty: 100,
            deliveryQty: 50,
            qtyPerPack: 10,
            unit: 'PCS',
            shipStatus: '已发货',
            deliveryDate: '2025-05-29',
            warehouse: 'WH01',
            palletCount: 2,
            containerType: '塑料箱',
            totalDeliveryQty: 200
          }
        ];
        this.total = 1;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },

    handlePrintDeliveryAcceptance() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$alert(this.$t('ticketPrint.alert.selectRow'));
        return;
      }
      // TODO: Print delivery/acceptance note logic for selectedRows
      this.$message.success(this.$t('ticketPrint.alert.printSuccess'));
    },
    handlePrintPickList() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$alert(this.$t('ticketPrint.alert.selectRow'));
        return;
      }
      // TODO: Print pick list logic for selectedRows
      this.$message.success(this.$t('ticketPrint.alert.printSuccess'));
    },
    openItemTagDialog() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$alert(this.$t('ticketPrint.alert.selectRow'));
        return;
      }
      // 初始化弹窗表单和表格数据，默认填第一个
      this.itemTagDialogVisible = true;
      this.itemTagForm = {
        lineNo: this.selectedRows[0].lineNo || '',
        orderNo: this.selectedRows[0].orderNo || '',
        batchNo: ''
      };
      // 所有选中行作为初始数据
      this.itemTagTable = this.selectedRows.map(row => Object.assign({}, row, { batchNo: '' }));
      this.selectedItemTagIndex = null;
    },
    printItemTag() {
      // 打印当前表格所有现品票
      this.$message.success(this.$t('ticketPrint.alert.printSuccess'));
    },
    reprintItemTag() {
      this.$message.success(this.$t('ticketPrint.dialog.reprintSuccess'));
    },
    addBatchNo() {
      // 根据输入的批次号更新当前选中行的批次号
      if (!this.itemTagForm.lineNo || !this.itemTagForm.orderNo) {
        this.$alert(this.$t('ticketPrint.dialog.inputLineOrder'));
        return;
      }
      // 查找行并更新批次号
      const idx = this.itemTagTable.findIndex(row => row.lineNo === this.itemTagForm.lineNo && row.orderNo === this.itemTagForm.orderNo);
      if (idx !== -1) {
        this.$set(this.itemTagTable, idx, Object.assign({}, this.itemTagTable[idx], { batchNo: this.itemTagForm.batchNo }));
      } else {
        // 新增一行
        this.itemTagTable.push({
          orderNo: this.itemTagForm.orderNo,
          lineNo: this.itemTagForm.lineNo,
          releaseNo: '',
          partNo: '',
          partDesc: '',
          batchNo: this.itemTagForm.batchNo,
          qtyPerPack: '',
          deliveryQty: '',
          deliveryDate: ''
        });
      }
      this.itemTagForm.batchNo = '';
    },
    removeBatchNo() {
      // 删除指定批次号（按行号和订单号）
      const idx = this.itemTagTable.findIndex(row => row.lineNo === this.itemTagForm.lineNo && row.orderNo === this.itemTagForm.orderNo);
      if (idx !== -1) {
        this.itemTagTable.splice(idx, 1);
      } else {
        this.$alert(this.$t('ticketPrint.dialog.noBatchToDelete'));
      }
    },
    resetItemTagDialog() {
      this.itemTagDialogVisible = false;
      this.itemTagForm = { lineNo: '', orderNo: '', batchNo: '' };
      this.itemTagTable = [];
      this.selectedItemTagIndex = null;
    },
    handlePrintPalletTag() {
      if (!this.selectedRows || this.selectedRows.length === 0) {
        this.$alert(this.$t('ticketPrint.alert.selectRow'));
        return;
      }
      // TODO: Print pallet tag logic for selectedRows
      this.$message.success(this.$t('ticketPrint.alert.printSuccess'));
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleItemTagRowClick(row, column, event) {
      this.selectedItemTagIndex = this.itemTagTable.indexOf(row);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.el-table {
  overflow-x: auto;
}
</style>
