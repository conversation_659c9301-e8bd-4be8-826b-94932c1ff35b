<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" class="mb8">
      <el-form-item :label="$t('palletMaintain.form.plant')" prop="plant">
        <el-input v-model="queryParams.plant" :placeholder="$t('palletMaintain.placeholder.plant')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('palletMaintain.form.partNo')" prop="partNo">
        <el-input v-model="queryParams.partNo" :placeholder="$t('palletMaintain.placeholder.partNo')" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{$t('palletMaintain.button.search')}}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{$t('palletMaintain.button.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="palletList"
      border
      stripe
      ref="palletTable"
      style="width: 100%"
    >
      <el-table-column :label="$t('palletMaintain.table.index')" type="index" width="50" align="center" />
      <el-table-column :label="$t('palletMaintain.table.action')" width="180" align="center">
        <template slot-scope="scope">
          <template v-if="editRowIndex === scope.$index">
            <el-button type="primary" size="mini" @click="saveEdit(scope.$index)">{{$t('palletMaintain.button.save')}}</el-button>
            <el-button size="mini" @click="cancelEdit">{{$t('palletMaintain.button.cancel')}}</el-button>
          </template>
          <template v-else>
            <el-button type="text" size="mini" @click="editRow(scope.$index)">{{$t('palletMaintain.button.edit')}}</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('palletMaintain.table.plant')" prop="plant" align="center" />
      <el-table-column :label="$t('palletMaintain.table.partNo')" prop="partNo" align="center" />
      <el-table-column :label="$t('palletMaintain.table.partDesc')" prop="partDesc" align="center" />
      <el-table-column :label="$t('palletMaintain.table.qtyPerPack')" prop="qtyPerPack" align="center" />
      <el-table-column :label="$t('palletMaintain.table.palletSnpQty')" prop="palletSnpQty" align="center">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model.number="editCache.palletSnpQty" size="small" type="number" min="0" />
          <el-input v-else v-model.number="scope.row.palletSnpQty" size="small" type="number" min="0" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('palletMaintain.table.palletLength')" prop="palletLength" align="center">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model.number="editCache.palletLength" size="small" type="number" min="0" />
          <el-input v-else v-model.number="scope.row.palletLength" size="small" type="number" min="0" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('palletMaintain.table.palletWidth')" prop="palletWidth" align="center">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model.number="editCache.palletWidth" size="small" type="number" min="0" />
          <el-input v-else v-model.number="scope.row.palletWidth" size="small" type="number" min="0" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('palletMaintain.table.palletHeight')" prop="palletHeight" align="center">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model.number="editCache.palletHeight" size="small" type="number" min="0" />
          <el-input v-else v-model.number="scope.row.palletHeight" size="small" type="number" min="0" disabled />
        </template>
      </el-table-column>
      <el-table-column :label="$t('palletMaintain.table.containerType')" prop="containerType" align="center">
        <template slot-scope="scope">
          <el-input v-if="editRowIndex === scope.$index" v-model="editCache.containerType" size="small" />
          <el-input v-else v-model="scope.row.containerType" size="small" disabled />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "palletMaintain",
  data() {
    return {
      loading: true,
      palletList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        plant: '',
        partNo: ''
      },
      editRowIndex: null,
      editCache: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // TODO: Replace with actual API call
      setTimeout(() => {
        this.palletList = [
          {
            plant: this.$t('palletMaintain.table.plant') + 'A',
            partNo: 'P001',
            partDesc: this.$t('palletMaintain.table.partDesc') + 'A',
            qtyPerPack: 10,
            palletSnpQty: 100,
            palletLength: 1200,
            palletWidth: 800,
            palletHeight: 150,
            containerType: this.$t('palletMaintain.table.containerType')
          },
          {
            plant: this.$t('palletMaintain.table.plant') + 'B',
            partNo: 'P002',
            partDesc: this.$t('palletMaintain.table.partDesc') + 'B',
            qtyPerPack: 20,
            palletSnpQty: 200,
            palletLength: 1300,
            palletWidth: 900,
            palletHeight: 160,
            containerType: this.$t('palletMaintain.table.containerType')
          }
        ];
        this.total = this.palletList.length;
        this.loading = false;
      }, 500);
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
    editRow(index) {
      if (this.editRowIndex !== null) {
        this.$message.warning(this.$t('palletMaintain.alert.saveBeforeEdit'));
        return;
      }
      this.editRowIndex = index;
      this.editCache = { ...this.palletList[index] };
    },
    saveEdit(index) {
      this.$set(this.palletList, index, { ...this.editCache });
      this.editRowIndex = null;
      this.editCache = {};
      this.$message.success(this.$t('palletMaintain.alert.saveSuccess'));
    },
    cancelEdit() {
      this.editRowIndex = null;
      this.editCache = {};
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}
.mb8 {
  margin-bottom: 8px;
}
</style>
