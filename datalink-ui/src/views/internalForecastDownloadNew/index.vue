<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item
        :label="$t('internalForecastDownloadNew.form.suppCode')"
        prop="suppCode"
      >
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('internalForecastDownloadNew.placeholder.suppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('internalForecastDownloadNew.form.status')" prop="kafkaStatus">
        <el-select
          v-model="queryParams.kafkaStatus"
          :placeholder="$t('internalForecastDownloadNew.placeholder.status')"
          clearable
          size="small"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('internalForecastDownloadNew.form.createTime')">
        <el-date-picker
          v-model="daterangeCreateTime"
          size="small"
          value-format="yyyy-MM-dd HH:mm"
          type="datetimerange"
          range-separator="-"
          format="yyyy-MM-dd HH:mm"
          :start-placeholder="
            $t('internalForecastDownloadNew.placeholder.startTime')
          "
          :end-placeholder="
            $t('internalForecastDownloadNew.placeholder.endTime')
          "
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("internalForecastDownloadNew.button.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("internalForecastDownloadNew.button.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printTxtLoading"
          :disabled="multiple"
          @click="handlePrintTxt"
          >{{
            $t("internalForecastDownloadNew.button.printSelectedRows")
          }}</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="confirmSelectedRows"
          >{{
            $t("internalForecastDownloadNew.button.confirmSelectedRows")
          }}</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="feedbackList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.index')"
        type="index"
        width="50"
        align="center"
      />
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.createTime')"
        align="center"
        prop="createTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('internalForecastDownloadNew.table.suppCode')"
        align="center"
        prop="suppCode"
      />
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.plantCode')"
        align="center"
        prop="plantCode"
      />
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.status')"
        align="center"
        prop="status"
      />
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.status')"
        align="center"
        prop="kafkaStatus"
      />
      <el-table-column
        :label="$t('internalForecastDownloadNew.table.lastDownloadingTime')"
        align="center"
        prop="lastDownloadingTime"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.lastDownloadingTime, "{y}-{m}-{d} {h}:{i}")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
// import { listFeedback, printTxt } from "@/api/datamanage/feedback";

export default {
  name: "internalForecastDownloadNew",
  data() {
    return {
      // 遮罩层
      loading: false,
      printTxtLoading: false,
      // 选中数组
      ids: [],
      statusOptions: [
        { dictValue: '0', dictLabel: this.$t('internalForecastDownloadNew.status.notDownloaded') },
        { dictValue: '1', dictLabel: this.$t('internalForecastDownloadNew.status.downloaded') }
      ],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货反馈表格数据
      feedbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: null,
        kafkaStatus: null,
        orderByColumn: "",
        isAsc: "",
      }
    }
  },
  created() {
    this.getList();
  },

  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user);
      const roles = this.$store.state.user.roles;
      const remark = this.$store.state.user.remark;
      return roles && roles.some((role) => role === "carrier") && remark;
    }
  },
  methods: {
    // 查询收货反馈列表（前端mock实现）
    getList() {
      this.loading = true;
      setTimeout(() => {
        // mock数据
        const mockData = [];
        for (let i = 0; i < 33; i++) {
          mockData.push({
            feedId: i + 1,
            createTime: `2025-05-2${i % 9 + 1} 09:0${i % 6}`,
            suppCode: 'SUPP' + (100 + i),
            plantCode: 'PLANT' + (10 + i % 3),
            status: i % 2 === 0 ? this.$t('internalForecastDownloadNew.status.normal') : this.$t('internalForecastDownloadNew.status.abnormal'),
            kafkaStatus: i % 2 === 0 ? '0' : '1',
            lastDownloadingTime: `2025-05-2${i % 9 + 1} 12:0${i % 6}`
          });
        }
        // 简单过滤
        let filtered = mockData;
        if (this.queryParams.suppCode) {
          filtered = filtered.filter(item => item.suppCode.includes(this.queryParams.suppCode));
        }
        if (this.queryParams.kafkaStatus) {
          filtered = filtered.filter(item => item.kafkaStatus === this.queryParams.kafkaStatus);
        }
        // 分页
        this.total = filtered.length;
        const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
        const end = start + this.queryParams.pageSize;
        this.feedbackList = filtered.slice(start, end);
        this.loading = false;
      }, 400);
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.queryParams.suppCode = null;
      this.queryParams.kafkaStatus = null;
      this.daterangeCreateTime = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.feedId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 确认选中行（前端模拟）
    confirmSelectedRows() {
      if (this.ids.length === 0) {
        this.$message.warning(this.$t('internalForecastDownloadNew.alert.selectOrder'));
        return;
      }
      this.$message.success(this.$t('internalForecastDownloadNew.alert.confirmSuccess'));
    },
    // 打印选中行（前端模拟）
    handlePrintTxt() {
      if (this.ids.length === 0) {
        this.$message.warning(this.$t('internalForecastDownloadNew.alert.selectOrder'));
        return;
      }
      this.printTxtLoading = true;
      setTimeout(() => {
        this.printTxtLoading = false;
        this.$message.success(this.$t('internalForecastDownloadNew.alert.printSuccess'));
      }, 1200);
    }
  }
}
</script>
