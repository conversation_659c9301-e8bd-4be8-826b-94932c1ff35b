name: Deploy SPS_HMC Prod

on:
  workflow_dispatch:
    inputs:
      build:
        description: '选择构建的部分'
        required: true
        type: choice
        options:
          - frontend
          - frontend-proxy
          - backend
          - templates
          - all
        default: 'backend'

jobs:
  frontend:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'frontend' || github.event.inputs.build == 'all' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            datalink-ui/

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install
        working-directory: datalink-ui/

      - name: Production Config
        run: cp .env.production.example .env.production
        working-directory: datalink-ui/

      - name: Build
        run: npm run build:prod
        working-directory: datalink-ui/

      - name: Empty Dir
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} ssh ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }} 'rm -rf /home/<USER>/sps-app/ui/*'

      - name: Deploy to Server via SCP
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} scp -r datalink-ui/dist/* ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }}:/home/<USER>/sps-app/ui/

      - name: Reload Nginx
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} ssh ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }} 'sudo nginx -s reload'

  frontend-proxy:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'frontend-proxy' || github.event.inputs.build == 'all' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            datalink-ui/

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'

      - name: Install dependencies
        run: npm install
        working-directory: datalink-ui/

      - name: Production Config
        run: |
          cat > .env.production <<'EOF'
          # 生产环境配置
          ENV = 'production'

          # DataLink管理系统/生产环境
          VUE_APP_BASE_API = '/cn/prod-api'
          VUE_APP_API_TARGET = 'http://127.0.0.1:8080'
          VUE_APP_SUB_PATH = '/cn'
          EOF
        working-directory: datalink-ui/

      - name: Build
        run: npm run build:prod
        working-directory: datalink-ui/

      - name: Empty Dir
        run: sshpass -p ${{ secrets.SPS_PROXY_SERVER_PASS }} ssh ${{secrets.SPS_PROXY_SERVER_USERNAME}}@${{ secrets.SPS_PROXY_SERVER }} 'rm -rf /home/<USER>/sps-cn/ui/*'

      - name: Deploy to Server via SCP
        run: sshpass -p ${{ secrets.SPS_PROXY_SERVER_PASS }} scp -r datalink-ui/dist/* ${{secrets.SPS_PROXY_SERVER_USERNAME}}@${{ secrets.SPS_PROXY_SERVER }}:/home/<USER>/sps-cn/ui/

      - name: Reload Nginx
        run: sshpass -p ${{ secrets.SPS_PROXY_SERVER_PASS }} ssh ${{secrets.SPS_PROXY_SERVER_USERNAME}}@${{ secrets.SPS_PROXY_SERVER }} 'sudo nginx -s reload'
        
  backend:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'backend' || github.event.inputs.build == 'all' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 8
        uses: actions/setup-java@v2
        with:
          java-version: '8'
          distribution: 'adopt'

      - name: Build Backend
        run: /opt/maven/bin/mvn clean package -DskipTests


      - name: Deploy Backend to Server via SCP
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} scp datalink-admin/target/*.jar ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }}:/home/<USER>/sps-app/app/

      - name: Restart Backend Service
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} ssh ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }} 'sudo systemctl restart sps-app.service'

  templates:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'templates' || github.event.inputs.build == 'all' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          sparse-checkout: |
            datalink-data-manage/src/main/resources/templates/

      - name: Deploy Templates to Server via SCP
        run: sshpass -p ${{ secrets.SPS_PROD_SERVER_PASS }} scp datalink-data-manage/src/main/resources/templates/* ${{secrets.SPS_PROD_SERVER_USERNAME}}@${{ secrets.SPS_PROD_SERVER }}:/home/<USER>/sps-app/template/
