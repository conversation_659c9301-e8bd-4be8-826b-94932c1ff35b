-- 为tbl_order_asn_quantity表添加Delivery_Schedule_No字段的DDL
ALTER TABLE `tbl_order_asn_quantity` 
ADD COLUMN `Delivery_Schedule_No` varchar(10) DEFAULT NULL COMMENT '交货计划行号' AFTER `Order_Line_No`;

-- 为新字段添加索引（可选，根据查询需求决定）
-- CREATE INDEX `idx_delivery_schedule_no` ON `tbl_order_asn_quantity` (`Delivery_Schedule_No`);

-- 如果需要复合索引来优化查询性能（可选）
CREATE INDEX `idx_order_delivery_schedule` ON `tbl_order_asn_quantity` (`Order_Code`, `Order_Line_No`, `Delivery_Schedule_No`);
