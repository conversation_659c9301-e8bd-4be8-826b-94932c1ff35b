-- ASN箱级批次功能测试脚本

-- 1. 测试数据准备
-- 创建测试ASN数据（如果不存在）
INSERT IGNORE INTO tbl_asn (Asn_ID, Asn_Code, Comp_Code, Supp_Code, Supp_Name, Plan_Delivery_Date, Delivery_Date, Direction, Kafka_Status, Create_Time, Create_By)
VALUES (9999, 'TEST_ASN_001', '1000', 'SUP001', '测试供应商', '2025-07-20', '2025-07-19', 'I', '0', NOW(), 'test');

-- 创建测试ASN行项目
INSERT IGNORE INTO tbl_asn_item (Item_ID, Dn_No, Order_Code, Plant_Code, Plant_Name, Unloading_No, Unloading_Name, Asn_ID, Create_Time, Create_By)
VALUES (9999, 'TEST_ASN_001-1', 'PO001', '1000', '测试工厂', 'UL001', '卸货点1', 9999, NOW(), 'test');

-- 创建测试ASN物料
INSERT IGNORE INTO tbl_asn_article (Article_ID, Article_No, Article_Name, Quantity, Unit, Batch_No, Order_Line_No, Qty_Per_Pack, Pack_Qty, Non_Std, Start_With, Item_ID, Create_Time, Create_By)
VALUES (9999, 'MAT001', '测试物料', 100.0000, 'PC', 'BATCH001', '10', 10.0000, 10.0000, 'N', 'TEST001', 9999, NOW(), 'test');

-- 2. 测试箱信息自动生成
-- 为测试物料生成箱信息
INSERT INTO tbl_asn_box (Article_ID, Box_No, Batch_No, Quantity, Create_Time, Create_By)
SELECT 
    9999 as Article_ID,
    LPAD(seq, 3, '0') as Box_No,
    'BATCH001' as Batch_No,
    10.0000 as Quantity,
    NOW() as Create_Time,
    'test' as Create_By
FROM (
    SELECT 1 as seq UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL
    SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10
) box_seq;

-- 3. 测试不同批次号的箱信息
-- 更新部分箱子的批次号，模拟箱级批次管理
UPDATE tbl_asn_box 
SET Batch_No = 'BATCH002' 
WHERE Article_ID = 9999 AND Box_No IN ('001', '002', '003');

UPDATE tbl_asn_box 
SET Batch_No = 'BATCH003' 
WHERE Article_ID = 9999 AND Box_No IN ('004', '005');

-- 4. 验证查询功能
-- 查询ASN及其箱信息
SELECT 
    '测试查询ASN箱信息' as test_case,
    a.Asn_Code,
    art.Article_No,
    art.Article_Name,
    art.Pack_Qty as total_boxes,
    COUNT(b.Box_ID) as actual_boxes,
    GROUP_CONCAT(DISTINCT b.Batch_No) as batch_numbers
FROM tbl_asn a
JOIN tbl_asn_item i ON a.Asn_ID = i.Asn_ID
JOIN tbl_asn_article art ON i.Item_ID = art.Item_ID
LEFT JOIN tbl_asn_box b ON art.Article_ID = b.Article_ID
WHERE a.Asn_Code = 'TEST_ASN_001'
GROUP BY a.Asn_Code, art.Article_No, art.Article_Name, art.Pack_Qty;

-- 5. 验证箱级批次号功能
-- 显示每个箱子的详细信息
SELECT 
    '箱级批次详情' as test_case,
    a.Asn_Code,
    art.Article_No,
    b.Box_No,
    b.Batch_No,
    b.Quantity,
    b.Pallet_No
FROM tbl_asn a
JOIN tbl_asn_item i ON a.Asn_ID = i.Asn_ID
JOIN tbl_asn_article art ON i.Item_ID = art.Item_ID
JOIN tbl_asn_box b ON art.Article_ID = b.Article_ID
WHERE a.Asn_Code = 'TEST_ASN_001'
ORDER BY b.Box_No;

-- 6. 验证数据一致性
-- 检查箱数量与物料总箱数是否一致
SELECT 
    '数据一致性检查' as test_case,
    art.Article_No,
    art.Pack_Qty as expected_boxes,
    COUNT(b.Box_ID) as actual_boxes,
    SUM(b.Quantity) as total_quantity,
    art.Quantity as expected_quantity,
    CASE 
        WHEN art.Pack_Qty = COUNT(b.Box_ID) AND art.Quantity = SUM(b.Quantity) THEN '✓ 数据一致'
        ELSE '✗ 数据不一致'
    END as consistency_check
FROM tbl_asn_article art
LEFT JOIN tbl_asn_box b ON art.Article_ID = b.Article_ID
WHERE art.Article_ID = 9999
GROUP BY art.Article_ID, art.Article_No, art.Pack_Qty, art.Quantity;

-- 7. 测试批次号统计
-- 统计不同批次号的箱数
SELECT 
    '批次号统计' as test_case,
    b.Batch_No,
    COUNT(*) as box_count,
    SUM(b.Quantity) as total_quantity
FROM tbl_asn_box b
WHERE b.Article_ID = 9999
GROUP BY b.Batch_No
ORDER BY b.Batch_No;

-- 8. 清理测试数据（可选，取消注释以执行）
/*
DELETE FROM tbl_asn_box WHERE Article_ID = 9999;
DELETE FROM tbl_asn_article WHERE Article_ID = 9999;
DELETE FROM tbl_asn_item WHERE Item_ID = 9999;
DELETE FROM tbl_asn WHERE Asn_ID = 9999;
*/

-- 测试结果说明：
-- 1. 如果所有查询都返回预期结果，说明箱级批次功能正常
-- 2. 数据一致性检查应该显示"✓ 数据一致"
-- 3. 批次号统计应该显示3个不同的批次号
-- 4. 箱级批次详情应该显示10个箱子，每个箱子有对应的批次号
