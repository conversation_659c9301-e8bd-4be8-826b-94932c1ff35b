-- 退货功能相关表结构

-- ----------------------------
-- Table structure for tbl_return
-- ----------------------------
DROP TABLE IF EXISTS `tbl_return`;
CREATE TABLE `tbl_return`
(
    `Return_ID`   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '退货ID',
    `Return_No`   varchar(255) NOT NULL COMMENT '退货单号',
    `Supp_Code`   varchar(50)  NOT NULL COMMENT '供应商代码',
    `Status`      varchar(20)  DEFAULT 'New' COMMENT '状态：New-新建，Processing-处理中，Completed-已完成',
    `Create_Time` datetime     DEFAULT current_timestamp() COMMENT '创建时间',
    `Create_By`   varchar(50)  DEFAULT NULL COMMENT '创建者',
    `Update_Time` datetime     DEFAULT NULL ON UPDATE current_timestamp() COMMENT '更新时间',
    `Update_By`   varchar(50)  DEFAULT NULL COMMENT '更新者',
    `Remark`      varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`Return_ID`) USING BTREE,
    UNIQUE KEY `uk_return_no` (`Return_No`) COMMENT '退货单号唯一索引',
    KEY `idx_supp_code` (`Supp_Code`) COMMENT '供应商代码索引',
    KEY `idx_status` (`Status`) COMMENT '状态索引'
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退货主表';

-- ----------------------------
-- Table structure for tbl_return_box
-- ----------------------------
DROP TABLE IF EXISTS `tbl_return_box`;
CREATE TABLE `tbl_return_box`
(
    `Box_ID`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '箱子ID',
    `Return_ID`         bigint(20) NOT NULL COMMENT '退货ID',
    `Asn_Code`          varchar(255) DEFAULT NULL COMMENT 'ASN编号',
    `Order_Code`        varchar(255) DEFAULT NULL COMMENT '订单编号',
    `Item_No`           varchar(50)  DEFAULT NULL COMMENT '行号',
    `Release_No`        varchar(50)  DEFAULT NULL COMMENT '下达号',
    `Original_Batch_No` varchar(50)  DEFAULT NULL COMMENT '原批次号',
    `New_Batch_No`      varchar(50)  DEFAULT NULL COMMENT '新批次号',
    `Original_Label`    varchar(255) DEFAULT NULL COMMENT '原标签',
    `New_Label`         varchar(255) DEFAULT NULL COMMENT '新标签',
    `Box_Index`         int(11)      DEFAULT NULL COMMENT '箱子序号',
    `Qty`               int(11)      DEFAULT NULL COMMENT '数量',
    `Status`            varchar(20)  DEFAULT 'New' COMMENT '状态：New-新建，Modified-已修改，Completed-已完成',
    `Create_Time`       datetime     DEFAULT current_timestamp() COMMENT '创建时间',
    `Create_By`         varchar(50)  DEFAULT NULL COMMENT '创建者',
    `Update_Time`       datetime     DEFAULT NULL ON UPDATE current_timestamp() COMMENT '更新时间',
    `Update_By`         varchar(50)  DEFAULT NULL COMMENT '更新者',
    PRIMARY KEY (`Box_ID`) USING BTREE,
    KEY `idx_return_id` (`Return_ID`) COMMENT '退货ID索引',
    KEY `idx_asn_code` (`Asn_Code`) COMMENT 'ASN编号索引',
    KEY `idx_order_code` (`Order_Code`) COMMENT '订单编号索引',
    KEY `idx_status` (`Status`) COMMENT '状态索引'
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='退货箱表';
