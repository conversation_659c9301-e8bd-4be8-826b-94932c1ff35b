-- ASN箱级批次优化数据迁移脚本
-- 为现有ASN数据生成对应的箱信息记录

-- 1. 为现有的ASN物料生成箱信息
INSERT INTO tbl_asn_box (Article_ID, Box_No, Batch_No, Quantity, Create_Time, Create_By)
SELECT 
    a.Article_ID,
    box_seq.seq as Box_No,
    a.Batch_No,
    a.Qty_Per_Pack as Quantity,
    NOW() as Create_Time,
    'system' as Create_By
FROM tbl_asn_article a
CROSS JOIN (
    SELECT 1 as seq UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL
    SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL
    SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL
    SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL SELECT 20 UNION ALL
    SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23 UNION ALL SELECT 24 UNION ALL SELECT 25 UNION ALL
    SELECT 26 UNION ALL SELECT 27 UNION ALL SELECT 28 UNION ALL SELECT 29 UNION ALL SELECT 30 UNION ALL
    SELECT 31 UNION ALL SELECT 32 UNION ALL SELECT 33 UNION ALL SELECT 34 UNION ALL SELECT 35 UNION ALL
    SELECT 36 UNION ALL SELECT 37 UNION ALL SELECT 38 UNION ALL SELECT 39 UNION ALL SELECT 40 UNION ALL
    SELECT 41 UNION ALL SELECT 42 UNION ALL SELECT 43 UNION ALL SELECT 44 UNION ALL SELECT 45 UNION ALL
    SELECT 46 UNION ALL SELECT 47 UNION ALL SELECT 48 UNION ALL SELECT 49 UNION ALL SELECT 50 UNION ALL
    SELECT 51 UNION ALL SELECT 52 UNION ALL SELECT 53 UNION ALL SELECT 54 UNION ALL SELECT 55 UNION ALL
    SELECT 56 UNION ALL SELECT 57 UNION ALL SELECT 58 UNION ALL SELECT 59 UNION ALL SELECT 60 UNION ALL
    SELECT 61 UNION ALL SELECT 62 UNION ALL SELECT 63 UNION ALL SELECT 64 UNION ALL SELECT 65 UNION ALL
    SELECT 66 UNION ALL SELECT 67 UNION ALL SELECT 68 UNION ALL SELECT 69 UNION ALL SELECT 70 UNION ALL
    SELECT 71 UNION ALL SELECT 72 UNION ALL SELECT 73 UNION ALL SELECT 74 UNION ALL SELECT 75 UNION ALL
    SELECT 76 UNION ALL SELECT 77 UNION ALL SELECT 78 UNION ALL SELECT 79 UNION ALL SELECT 80 UNION ALL
    SELECT 81 UNION ALL SELECT 82 UNION ALL SELECT 83 UNION ALL SELECT 84 UNION ALL SELECT 85 UNION ALL
    SELECT 86 UNION ALL SELECT 87 UNION ALL SELECT 88 UNION ALL SELECT 89 UNION ALL SELECT 90 UNION ALL
    SELECT 91 UNION ALL SELECT 92 UNION ALL SELECT 93 UNION ALL SELECT 94 UNION ALL SELECT 95 UNION ALL
    SELECT 96 UNION ALL SELECT 97 UNION ALL SELECT 98 UNION ALL SELECT 99 UNION ALL SELECT 100
) box_seq
WHERE box_seq.seq <= a.Pack_Qty
AND a.Pack_Qty > 0
AND a.Qty_Per_Pack > 0;

-- 2. 验证数据迁移结果
-- 检查箱信息总数是否与物料箱数一致
SELECT 
    '数据迁移验证' as check_type,
    COUNT(*) as total_boxes,
    SUM(a.Pack_Qty) as expected_boxes,
    CASE 
        WHEN COUNT(*) = SUM(a.Pack_Qty) THEN '✓ 数据一致' 
        ELSE '✗ 数据不一致' 
    END as result
FROM tbl_asn_box b
JOIN tbl_asn_article a ON b.Article_ID = a.Article_ID;

-- 3. 检查是否有物料没有生成箱信息
SELECT 
    a.Article_ID,
    a.Article_No,
    a.Pack_Qty,
    COUNT(b.Box_ID) as actual_boxes
FROM tbl_asn_article a
LEFT JOIN tbl_asn_box b ON a.Article_ID = b.Article_ID
GROUP BY a.Article_ID, a.Article_No, a.Pack_Qty
HAVING COUNT(b.Box_ID) != a.Pack_Qty
ORDER BY a.Article_ID;

-- 4. 统计迁移结果
SELECT 
    '迁移统计' as info_type,
    COUNT(DISTINCT a.Article_ID) as total_articles,
    COUNT(b.Box_ID) as total_boxes_created,
    AVG(a.Pack_Qty) as avg_boxes_per_article
FROM tbl_asn_article a
LEFT JOIN tbl_asn_box b ON a.Article_ID = b.Article_ID;

-- 注意事项：
-- 1. 此脚本会为所有现有的ASN物料生成箱信息
-- 2. 箱号格式为001, 002, 003...，最多支持100箱
-- 3. 如果某个物料的箱数超过100，需要扩展box_seq的数量
-- 4. 批次号从原物料记录中复制
-- 5. 托盘信息暂时为空，后续可根据业务需要填充
