-- 邮件提醒功能配置项初始化脚本

-- 邮件功能开关
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('邮件功能开关', 'email.enabled', 'false', 'Y', 'admin', NOW(), '控制邮件提醒功能是否启用，true-启用，false-禁用');

-- SMTP服务器配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('SMTP服务器地址', 'smtp.host', 'smtp.partner.outlook.cn', 'Y', 'admin', NOW(), 'SMTP邮件服务器地址');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('SMTP端口号', 'smtp.port', '587', 'Y', 'admin', NOW(), 'SMTP邮件服务器端口号');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('发件邮箱', 'smtp.username', '', 'Y', 'admin', NOW(), '发送邮件的邮箱地址');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('邮箱密码', 'smtp.password', '', 'Y', 'admin', NOW(), '发送邮件的邮箱密码或授权码');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('SSL启用', 'smtp.ssl.enable', 'false', 'Y', 'admin', NOW(), '是否启用SSL加密，true-启用，false-禁用');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('发件人名称', 'email.from.name', 'SPS_HMC系统', 'Y', 'admin', NOW(), '邮件发件人显示名称');

-- 邮件模板配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark)
VALUES ('订单通知邮件模板', 'email.template.order',
        '<html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #4CAF50; color: white; padding: 15px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
                .button { background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }
                .footer { text-align: center; padding: 10px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>订单通知</h2>
                </div>
                <div class="content">
                    <p>尊敬的用户您好，</p>
                    <p>您有一个新的采购订单：<strong>{orderCode}</strong>，请及时登录系统查看详细信息。</p>
                    <a href="{viewLink}" class="button">立即查看</a>
                    <p>谢谢。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发出，请勿直接回复</p>
                </div>
            </div>
        </body>
        </html>', 'Y', 'admin', NOW(), '订单接收通知邮件模板');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('预测通知邮件模板', 'email.template.forecast', 
'<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2196F3; color: white; padding: 15px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { background-color: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }
        .footer { text-align: center; padding: 10px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>预测通知</h2>
        </div>
        <div class="content">
            <p>尊敬的用户您好，</p>
            <p>您有一个新的采购內示，发送时间为：<strong>{receiveTime}</strong>，请及时登录系统查看详细信息。</p>
            <a href="{viewLink}" class="button">立即查看</a>
            <p>谢谢。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发出，请勿直接回复</p>
        </div>
    </div>
</body>
</html>', 'Y', 'admin', NOW(), '预测接收通知邮件模板');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('结算单通知邮件模板', 'email.template.feedback', 
'<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #FF9800; color: white; padding: 15px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { background-color: #FF9800; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }
        .footer { text-align: center; padding: 10px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>结算单通知</h2>
        </div>
        <div class="content">
            <p>尊敬的用户您好，</p>
            <p>您有一个新的验收明细：<strong>{dnNo}</strong>，请及时登录系统查看详细信息。</p>
            <a href="{viewLink}" class="button">立即查看</a>
            <p>谢谢。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发出，请勿直接回复</p>
        </div>
    </div>
</body>
</html>', 'Y', 'admin', NOW(), '结算单接收通知邮件模板');

INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('退货通知邮件模板', 'email.template.return', 
'<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f44336; color: white; padding: 15px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .button { background-color: #f44336; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }
        .footer { text-align: center; padding: 10px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>退货通知</h2>
        </div>
        <div class="content">
            <p>尊敬的用户您好，</p>
            <p>您有一个新的退货信息：<strong>{returnNo}</strong>，请及时登录系统查看详细信息。</p>
            <a href="{viewLink}" class="button">立即查看</a>
            <p>谢谢。</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发出，请勿直接回复</p>
        </div>
    </div>
</body>
</html>', 'Y', 'admin', NOW(), '退货接收通知邮件模板');

-- 系统链接配置
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, remark) 
VALUES ('系统访问链接', 'system.base.url', 'http://localhost:8080', 'Y', 'admin', NOW(), '系统访问基础URL，用于邮件中的链接生成');
